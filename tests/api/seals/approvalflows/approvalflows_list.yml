#审批列表查询
variables:
  keyword: ""
  status: []
  type: 2
  sortType: 1
request:
  url: ${ENV(footstone_api_url)}/v1/approvalflows/list
  method: POST
  headers:
    Content-Type: "application/json"
    X-Tsign-Open-App-Id: "$app_id"
    X-Tsign-Open-Auth-Mode: "simple"
    X-Tsign-Open-Operator-Id: $operator
    X-Tsign-Open-Tenant-Id: $space_orgId
    X-Tsign-Service-Group: ${ENV(envCode)}
  json:
    {
      "keyword": $keyword,
      "pageNum": 0,
      "pageSize": 10,
      "sortType": $sortType, #排序类型 1-发起时间降序（默认） 2-发起时间升序 3-审批完成时间降序 4-审批完成时间升序
      "status": $status, # 1 待审批 2审批通过 3 审批驳回 （不传默认所有）
      "type": $type   # 1 我收到的 2我发起的 3待我审批 4我已审批
    }