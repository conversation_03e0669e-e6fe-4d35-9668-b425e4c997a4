#!/usr/bin/env bash

function api_split(){
 echo '=========================== api原文件：'$1
#一个api文件定义的api方法个数
api_line=`grep -n "api:" $1 |awk -F':' '{print $1}'`
array=($api_line)
length=${#array[@]}
for ((i=0; i<$length; i++))
do
    j=`expr $i + 1`
    tstart=`expr ${array[$i]} + 1`
    echo '====api begin line: '$tstart;

    api_var=`sed -n  $tstart'p' $1 | sed -e 's/def: [a-zA-Z0-9_.-]*(\(.*\)).*/\1/g; s/[\$]//g'`
    echo '====api_vars: '$api_var

    api_name=`sed -n  $tstart'p' $1 | sed 's/def: \([a-zA-Z0-9_.-]*\)(.*/\1/g; s/^\s*//g'`
    api_file_name=$2"/"$api_name".yml"
    echo '====api_file_name: '$api_file_name

    #拆分api开始
    if [ $j -eq $length ]
    then
#            cat $1|awk 'NR>=s' s=$tstart|sed -e '1s/def:/name:/g;1s/(.*)\(.*\)/\1/g' > $api_file_name
            cat $1|awk 'NR>=s' s=$tstart|sed -e '1s/def:/#def:/g; ' > $api_file_name
    else
            tend=`expr ${array[$j]} - 1`
            echo '====api end line: '$tend;
#            cat $1|awk 'NR>=s && NR<=e' s=$tstart e=$tend|sed -e '1s/def:/name:/g;1s/(.*)\(.*\)/\1/g' > $api_file_name
            cat $1|awk 'NR>=s && NR<=e' s=$tstart e=$tend|sed -e '1s/def:/#def:/g;' > $api_file_name
    fi

     ##搜索调用api方法的文件，替换api方法为api文件路径
     echo '搜索调用api方法的文件 替换'
     sed -i 's|api:\s*'$api_name'(.*)|api: '$api_file_name'|g;' `grep -rl 'api:\s*'$api_name'(.*)' ./`
done
}





api_split  ./api/signflow/flowSigners.yml  ./api/signflow/flowSigners
