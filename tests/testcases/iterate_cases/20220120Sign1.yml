- config:
    name: "0120接口测试用例"
    base_url: ${ENV(base_url)}
    variables:
      - group: ${ENV(envCode)}
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - fileId: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - accountId: ${ENV(tengqing_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - accountId_tsign: ${ENV(account_id1_in_tsign)}

- test:
    name: 分步创建流程
    variables:
      - json: {"flowConfigInfo":{"buttonConfig":{"propButton":"1,2,6,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}},"autoArchive":"true","businessScene":"签署","extend":{},"initiatorAccountId":"$accountId_tsign","initiatorAuthorizedAccountId":"$accountId_tsign","payerAccountId":""}
    api: api/signflow/v3/signflows.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.flowId,32]
      - eq: [content.message, 成功]

- test:
    name: 添加合同文档
    variables:
      - flowId: $flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加签名域，fieldType==1，无法添加
    variables:
      - flowId: $flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$accountId_tsign","actorIndentityType":"0","authorizedAccountId":"$accountId_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":1,"signDateBean":{"posPage":"1","posX":411,"posY":498},"order":1,"posBean":{"posPage":"1","posX":496,"posY":14},"sealType":"","sealId":"","signType":1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]

- test:
    name: 添加签名域，fieldType==0，可以添加
    variables:
      - flowId: $flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$accountId_tsign","actorIndentityType":"0","authorizedAccountId":"$accountId_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":0,"signDateBean":{"posPage":"1","posX":411,"posY":498},"order":1,"posBean":{"posPage":"1","posX":496,"posY":14},"sealType":"","sealId":"","signType":1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加签名域，fieldType==null，可以添加
    variables:
      - flowId: $flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$accountId_tsign","actorIndentityType":"0","authorizedAccountId":"$accountId_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":null,"signDateBean":{"posPage":"1","posX":411,"posY":498},"order":1,"posBean":{"posPage":"1","posX":496,"posY":14},"sealType":"","sealId":"","signType":1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加签名域，未传fieldType，可以添加
    variables:
      - flowId: $flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$accountId_tsign","actorIndentityType":"0","authorizedAccountId":"$accountId_tsign","assignedPosbean":false,"signDateBeanType":"1","signDateBean":{"posPage":"1","posX":411,"posY":498},"order":1,"posBean":{"posPage":"1","posX":496,"posY":14},"sealType":"","sealId":"","signType":1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加签名域，fieldType为2，可以添加
    variables:
      - flowId: $flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$accountId_tsign","actorIndentityType":"0","authorizedAccountId":"$accountId_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一二","remarkFieldHeight":200,"remarkFieldWidth":200},"signDateBean":{"posPage":"1","posX":411,"posY":498},"order":1,"posBean":{"posPage":"1","posX":496,"posY":14},"sealType":"","sealId":"","signType":1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程开启
    variables:
      - flowId: $flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 添加签名域，已开启+自动归档的流程不允许添加
    variables:
      - flowId: $flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$accountId_tsign","actorIndentityType":"0","authorizedAccountId":"$accountId_tsign","assignedPosbean":false,"signDateBeanType":"1","signDateBean":{"posPage":"1","posX":411,"posY":498},"order":1,"posBean":{"posPage":"1","posX":496,"posY":14},"sealType":"","sealId":"","signType":1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]