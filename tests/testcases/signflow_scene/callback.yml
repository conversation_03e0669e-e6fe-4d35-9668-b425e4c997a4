- config:
    name: 回调通知
    base_url: ${ENV(footstone_api_url)}
    variables:
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_Id1: ${ENV(appId_xuanyuan)}
      - app_Id2: ${ENV(appId_wukong)}
      - path_pdf: data/个人借贷合同.pdf
      - mobile1: "***********"
      - mobile2: "***********"
      - name1: 梁贤红
      - idNo1: 331082199211223080
      - thirdPartyUserId1: a${get_randomString()}
      - thirdPartyUserId2: b${get_randomString()}
      - name2: esigntest东营伟信建筑安装工程有限公司
      - idNo2: 91370502060407048H
      - operator: ${getOid($mobile1)}
      - app_id: ${ENV(BZQ-App-Id)}

- test:
    name: 创建流程：
    variables:
      - app_id: $app_Id2
      - autoArchive: False
      - businessScene: 创建流程:签署人有thirdid
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

          #- test:
          #    name: 创建账号有信息
        #    variables:
        #        - app_id: $app_Id2
        #          #        - json: {    "properties": { "name": $name1  },     "credentials": {  "idno": $idNo1 },
        #             "idcards": {
        #               "projThirdparty": {
        #                 "thirdpartyUserId": $thirdPartyUserId1,
        #                 "thirdpartyUserType": "ALI_PAY"
        #               }  }   }
        #    api: api/user/accounts/create_accounts_v3.yml
        #    extract:
        #        - oid2: content.data.accountId
        #    validate:
      - eq: ["content.code", 0]
#    output:
#        -  $oid2

- test:
    name: 创建账号
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id2
      - idNo: $idNo1
      - mobile: $mobile1
      - name: $name1
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid1: content.data.accountId


- test:
    name: 创建个人模板印章
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - app_id: $app_Id2
      - accountId: $oid1
      - color: "BLUE"
      - height: 136
      - width: 136
      - alias: "蓝色印章"
      - type: "BORDERLESS"
    extract:
      - sealId1: content.data.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 获取上传文件url
    variables:
      - app_id: $app_Id2
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $oid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_Id2
      - flowId: $flowId1
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 添加流程签署区-thirdid
    variables:
      - app_id: $app_Id2
      - flowId: $flowId1
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId1,$flowId1,$posBean,0,$oid1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_Id2
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: 添加或更新签署区并执行签署
    variables:
      - app_id: $app_Id2
      - flowId: $flowId1
      - accountId: $operator
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield:  {actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $operator ,autoExecute: false, extendFieldDatas: {},fileId: $fileId1, flowId: $flowId1,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId1,sealType: 1,signType: 1,signerAccountId: $operator}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",成功]

- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - app_id: $app_Id2
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 创建流程2
    variables:
      - app_id: $app_Id2
      - autoArchive: False
      - businessScene: 创建流程：签署人无third
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "创建新账号"
    api: api/user/accounts/create_accounts.yml   #无email/mobile
    variables:
      - app_id: $app_Id2
      - idNo: "331082199211223080"
      - idType: "19"
      - name: "梁贤红"
      - mobile: ***********
      - email: <EMAIL>
    extract:
      - oid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: 创建个人模板印章
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - app_id: $app_Id2
      - accountId: $oid2
      - color: "BLUE"
      - height: 136
      - width: 136
      - alias: "蓝色印章"
      - type: "BORDERLESS"
    extract:
      - sealId2: content.data.sealId
    validate:
      - eq: [status_code, 200]




- test:
    name: 获取上传文件url
    variables:
      - app_id: $app_Id2
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $oid2
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 添加流程文档，悟空
    variables:
      - flowId: $flowId2
      - app_id: $app_Id2
      - doc1: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加流程签署区
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
      - posBean: {}
      - signfield: {actorIndentityType: 0,assignedItem: 0,assignedPosbean: false,assignedSeal: false,authorizedAccountId: $oid2 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId2, flowId: $flowId2,order: 1,posBean: $posBean,sealFileKey: "",sealId: '',sealType: 1,signType: 0,signerAccountId: $oid2}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 添加或更新签署区并执行签署
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
      - accountId: $oid2
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield:  {actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid2 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId2, flowId: $flowId2,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid2}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",成功]



- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

