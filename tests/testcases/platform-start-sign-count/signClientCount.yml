- config:
    name: "签署-签动作相关的操作端统计"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ${ENV(envCode)}
      - app_id: ${ENV(xuanyuan_realname)}
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - fileId2: a152d745f521490aaedcadafed493b98
      - mobile_fengjiu: ***********
      - mobile_caogu: ***********
      - sealId1: 8fa71acc-425d-4270-9fa7-b0e91f57fbef

- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: ckl201911141994
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "创建草谷账号"
    variables:
      - app_id: $write_app_Id
      - group: $w_group
      - thirdPartyUserId: caogu201911141132
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建凤九账号"
    variables:
      - thirdPartyUserId: fengjiu20191114199132191431252002
      - name: 曾艳
      - idNumber: 362324199301130629
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - fengjiu_accountId: content.data.accountId

- test:
    name: "创建甘宁账号"
    variables:
      - thirdPartyUserId: yre00152224212341
      - name: 甘舰戈
      - idNumber: ******************
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ganning: content.data.accountId


- test:
    name: 创建多人签署流程
    variables:
      - extend:
      - autoArchive: False
      - businessScene:  创建钟灵签署流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档
    variables:
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#流程添加个人签名域
- test:
    name: "个人-陈凯丽"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_ckl: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "个人-草谷"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"0","authorizedAccountId":$caogu_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":500,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_caogu: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "个人-凤九"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$fengjiu_accountId,"actorIndentityType":"0","authorizedAccountId":$fengjiu_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":500,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_fengjiu: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "个人-甘宁"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ganning,"actorIndentityType":"0","authorizedAccountId":$accountId_ganning,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":500,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_ganning: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


#开启流程
- test:
    name: 开启流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

##流程签署完成后 更新流程抄送人为null
- test:
    name: "根据登陆凭证获取钟灵accountId"
    variables:
      - idcard: ***********
      - type: MOBILE
    api: api/iterate_cases/getByIdcard.yml
    extract:
      - stu_accountId_ckl: content.data.items.0.accountId

- test:
    name: 查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $stu_accountId_ckl
    extract:
      - ckl_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: "根据登陆凭证获取草谷accountId"
    variables:
      - idcard: ***********
      - type: MOBILE
    api: api/iterate_cases/getByIdcard.yml
    extract:
      - stu_accountId_caogu: content.data.items.0.accountId

- test:
    name: 查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $stu_accountId_caogu
    extract:
      - caogu_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: "根据登陆凭证获取凤九accountId"
    variables:
      - idcard: ***********
      - type: MOBILE
    api: api/iterate_cases/getByIdcard.yml
    extract:
      - stu_accountId_fengjiu: content.data.items.0.accountId

- test:
    name: 查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $stu_accountId_fengjiu
    extract:
      - fengjiu_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: "根据登陆凭证获取甘宁accountId"
    variables:
      - idcard: ***********
      - type: MOBILE
    api: api/iterate_cases/getByIdcard.yml
    extract:
      - stu_accountId_ganning: content.data.items.0.accountId

- test:
    name: 查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $stu_accountId_ganning
    extract:
      - ganning_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 查询钟灵签署区
    variables:
      - json: {
        "flowId": $sign_flowId1,
        "order": 1,
        "signerAccountId": $accountId_ckl,
        "signerAuthorizedAccountId": $accountId_ckl
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", null]

#钟灵签署
- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId_ckl
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId_ckl
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "添加或更新签署区并执行签署-钟灵"
    variables:
      - flowId: $sign_flowId1
      - client: WEB
      - accountId: $stu_accountId_ckl
      - addSignfields:
      - updateSignfields: [{"sealId":$ckl_sealId, authorizedAccountId: $accountId_ckl, "signerOperatorAuthorizerId":$stu_accountId_ckl,"signerOperatorId":$stu_accountId_ckl,"signfieldId":$signfieldId_ckl}]
      - signfieldIds:
    api: api/platform-start-sign-count/V1_addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]



#草谷签署
- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId_caogu
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId_caogu
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加或更新签署区并执行签署-草谷"
    variables:
      - flowId: $sign_flowId1
      - client: H5
      - accountId: $stu_accountId_caogu
      - addSignfields:
      - updateSignfields: [{"sealId":$caogu_sealId, authorizedAccountId: $caogu_accountId, "signerOperatorAuthorizerId":$stu_accountId_caogu,"signerOperatorId":$stu_accountId_caogu,"signfieldId":$signfieldId_caogu}]
      - signfieldIds:
    api: api/platform-start-sign-count/V1_addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]



#凤九签署
- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId_fengjiu
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId_fengjiu
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加或更新签署区并执行签署-凤九"
    variables:
      - flowId: $sign_flowId1
      - client: ALI_PAY_MINI
      - accountId: $stu_accountId_fengjiu
      - addSignfields:
      - updateSignfields: [{"sealId":$fengjiu_sealId, authorizedAccountId: $fengjiu_accountId, "signerOperatorAuthorizerId":$stu_accountId_fengjiu,"signerOperatorId":$stu_accountId_fengjiu,"signfieldId":$signfieldId_fengjiu}]
      - signfieldIds:
    api: api/platform-start-sign-count/V1_addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 甘宁拒签
    variables:
      - refuseReason: 拒绝签署
      - accountId: $accountId_ganning
      - flowId: $sign_flowId1
      - client: WE_CHAT
    api: api/platform-start-sign-count/signersRefuse.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


#新增rpc接口查询签署区的clientid信息
- test:
    name: 查询钟灵签署区
    variables:
      - json: {
        "flowId": $sign_flowId1,
        "order": 1,
        "signerAccountId": $accountId_ckl,
        "signerAuthorizedAccountId": $accountId_ckl
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", 5]

- test:
    name: 查询草谷签署区
    variables:
      - json: {
        "flowId": $sign_flowId1,
        "order": 1,
        "signerAccountId": $caogu_accountId,
        "signerAuthorizedAccountId": $caogu_accountId
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", 4]

- test:
    name: 查询凤九签署区
    variables:
      - json: {
        "flowId": $sign_flowId1,
        "order": 1,
        "signerAccountId": $fengjiu_accountId,
        "signerAuthorizedAccountId": $fengjiu_accountId
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", 1]

- test:
    name: 查询甘宁签署区
    variables:
      - json: {
        "flowId": $sign_flowId1,
        "order": 1,
        "signerAccountId": $accountId_ganning,
        "signerAuthorizedAccountId": $accountId_ganning
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", 2]










- test:
    name: 创建多人签署流程2
    variables:
      - extend:
      - autoArchive: False
      - businessScene:  创建多人签署流程2
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档
    variables:
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $sign_flowId2
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#流程添加个人签名域
- test:
    name: "个人-陈凯丽2"
    variables:
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_ckl2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "个人-草谷2"
    variables:
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"0","authorizedAccountId":$caogu_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":500,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_caogu2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "个人-凤九"
    variables:
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId1,"signerAccountId":$fengjiu_accountId,"actorIndentityType":"0","authorizedAccountId":$fengjiu_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":500,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_fengjiu2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "个人-甘宁"
    variables:
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ganning,"actorIndentityType":"0","authorizedAccountId":$accountId_ganning,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":500,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_ganning2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


#开启流程
- test:
    name: 开启流程
    variables:
      - flowId: $sign_flowId2
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



#钟灵签署
- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId_ckl
      - flowId: $sign_flowId2
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId_ckl
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "添加或更新签署区并执行签署-钟灵"
    variables:
      - flowId: $sign_flowId2
      - client:
      - accountId: $stu_accountId_ckl
      - addSignfields:
      - updateSignfields: [{"sealId":$ckl_sealId, authorizedAccountId: $accountId_ckl, "signerOperatorAuthorizerId":$stu_accountId_ckl,"signerOperatorId":$stu_accountId_ckl,"signfieldId":$signfieldId_ckl2}]
      - signfieldIds:
    api: api/platform-start-sign-count/V1_addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]



#草谷签署
- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId_caogu
      - flowId: $sign_flowId2
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId_caogu
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加或更新签署区并执行签署-草谷"
    variables:
      - flowId: $sign_flowId2
      - client: APP_IOS
      - accountId: $stu_accountId_caogu
      - addSignfields:
      - updateSignfields: [{"sealId":$caogu_sealId, authorizedAccountId: $caogu_accountId, "signerOperatorAuthorizerId":$stu_accountId_caogu,"signerOperatorId":$stu_accountId_caogu,"signfieldId":$signfieldId_caogu2}]
      - signfieldIds:
    api: api/platform-start-sign-count/V3_addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]



#凤九签署
- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId_fengjiu
      - flowId: $sign_flowId2
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId_fengjiu
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加或更新签署区并执行签署-凤九"
    variables:
      - flowId: $sign_flowId2
      - client:
      - accountId: $stu_accountId_fengjiu
      - addSignfields:
      - updateSignfields: [{"sealId":$fengjiu_sealId, authorizedAccountId: $fengjiu_accountId, "signerOperatorAuthorizerId":$stu_accountId_fengjiu,"signerOperatorId":$stu_accountId_fengjiu,"signfieldId":$signfieldId_fengjiu2}]
      - signfieldIds:
    api: api/platform-start-sign-count/V3_addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 甘宁拒签
    variables:
      - refuseReason: 拒绝签署
      - accountId: $accountId_ganning
      - flowId: $sign_flowId2
      - client:
    api: api/platform-start-sign-count/signersRefuse.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


#新增rpc接口查询签署区的clientid信息
- test:
    name: 查询钟灵签署区
    variables:
      - json: {
        "flowId": $sign_flowId2,
        "order": 1,
        "signerAccountId": $accountId_ckl,
        "signerAuthorizedAccountId": $accountId_ckl
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", null]

- test:
    name: 查询草谷签署区
    variables:
      - json: {
        "flowId": $sign_flowId2,
        "order": 1,
        "signerAccountId": $caogu_accountId,
        "signerAuthorizedAccountId": $caogu_accountId
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", 10]

- test:
    name: 查询凤九签署区
    variables:
      - json: {
        "flowId": $sign_flowId2,
        "order": 1,
        "signerAccountId": $fengjiu_accountId,
        "signerAuthorizedAccountId": $fengjiu_accountId
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", null]

- test:
    name: 查询甘宁签署区
    variables:
      - json: {
        "flowId": $sign_flowId2,
        "order": 1,
        "signerAccountId": $accountId_ganning,
        "signerAuthorizedAccountId": $accountId_ganning
      }
    api: api/platform-start-sign-count/queryTaskInfoInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.source", null]