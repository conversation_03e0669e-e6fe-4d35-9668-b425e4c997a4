config:
  name: 前程无忧二期
testcases:

  发起不传经办人:
    testcase: testcases/51job_transfer/createFlowOneStep_no_singerAccount.yml

  流程开启时转交:
    testcase: testcases/51job_transfer/signflow_start_transfer.yml

  流程开启时不转交:
    testcase: testcases/51job_transfer/signflow_start_no_transfer.yml

  全程不转交（企业未开启转交）:
    testcase: testcases/51job_transfer/signflow_notransfer.yml

  ********迭代:
    testcase: testcases/iterate_cases/********Sign.yml
