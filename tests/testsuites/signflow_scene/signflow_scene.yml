config:
  name: 签署场景测试


testcases:

  签署场景测试:
    testcase: testcases/signflow_scene/orderSign.yml
  二级审批:
    testcase: testcases/approval_flow/审批流-二级审批.yml

#    个人单文档签署，未实名:
#        testcase: testcases/signflow_scene/onePersonSign.yml

#    多人多文档签署:
#        testcase: testcases/signflow_scene/multipleSign.yml
#
#    静默签署场景:
#        testcase: testcases/signflow_scene/silentSign.yml
#
#    有参与人、附件、转签:
#        testcase: testcases/signflow_scene/transferSign.yml
#
#    顺序签署:
#       testcase: testcases/signflow_scene/orderSign.yml
#       variables:
#       fileId1: $fileId1
#       orgOid1: $orgOid1
#       personOid1: $personOid1
#       personOid2: $personOid2
#       personOid3: $personOid3

#- test:
#    name: 批量签署
#    testcase: testcases/signflow_scene/batchSign.yml
