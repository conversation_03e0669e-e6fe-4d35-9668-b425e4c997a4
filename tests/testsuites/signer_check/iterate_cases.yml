config:
  name: 迭代case

testcases:
  0513迭代case:
    testcase: testcases/iterate_cases/20210513Sign.yml

  0422迭代case-主体名称中括号忽略全角半角:
    testcase: testcases/iterate_cases/20210422Sign.yml

  创建流程的noticeType和redirectDelayTime参数校验:
    testcase: testcases/iterate_cases/20210108config.yml

  0109迭代case-文本签:
    testcase: testcases/iterate_cases/0109Sign.yml

  0623迭代case-文本签P1:
    testcase: testcases/iterate_cases/0623Sign_P1.yml

  0623迭代case-文本签P7:
    testcase: testcases/iterate_cases/0623Sign_P7.yml

  0623迭代case-pdf验签:
    testcase: testcases/iterate_cases/0623Sign_pdfS.yml

  0402迭代case-经办人:
    testcase: testcases/iterate_cases/0402Sign.yml

  0528迭代case-印章可见-handsign接口关于sealIds字段的校验:
    testcase: testcases/iterate_cases/0528Sign_handsign.yml

  0528迭代case-印章可见-getSignSeals接口关于sealIds字段的校验-轩辕实名:
    testcase: testcases/iterate_cases/0528Sign_getSignSeals_xuanyuan.yml

  0528迭代case-印章可见-getSignSeals接口关于sealIds字段的校验-悟空非实名:
    testcase: testcases/iterate_cases/0528Sign_getSignSeals_wukong.yml

  0528迭代case-印章可见-getSignSeals接口关于sealIds字段的校验-悟空实名-非:
    testcase: testcases/iterate_cases/0528Sign_getSignSeals_wukong_not_in_white.yml

  0528迭代case-印章可见-getSignSeals接口关于sealIds字段的校验-悟空实名-白名单:
    testcase: testcases/iterate_cases/0528Sign_getSignSeals_wukong_in_white.yml

  0611迭代case:
    testcase: testcases/iterate_cases/0611Sign.yml

  0611迭代case-0611onePerson_100signField_V3:
    testcase: testcases/iterate_cases/0611onePerson_100signField_V3.yml

  0611迭代case-0611Sign_100person:
    testcase: testcases/iterate_cases/0611Sign_100person.yml

  0628Sign_forceWill:
    testcase: testcases/iterate_cases/0628Sign_forceWill.yml

  0628Sign_noforceWill:
    testcase: testcases/iterate_cases/0628Sign_noforceWill.yml

  0709Sign:
    testcase: testcases/iterate_cases/0709Sign.yml

  0723Sign_attachment:
    testcase: testcases/iterate_cases/0723Sign_attachment.yml


  #
  #    20210819迭代用例/薪福通:
  #        testcase: testcases/iterate_cases/20210819Sign.yml


