- config:
    name: "分享一期 - 无核验的签署流程"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}

- test:
    name: "创建签署人账号 - 王瑶济"
    variables:
      - thirdPartyUserId: 1234567gfdsadfgh
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId: content.data.accountId



- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: ckl-***********
      - name: 陈凯丽
      - idNumber:
      - idType:
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: 创建accountId_ckl对应标准签appid下oid的个人模板印章1
    variables:
      - app_id: $app_id
      - accountId: $standardoid
      - color: BLACK
      - height: 120
      - width: 120
      - alias: p_黑色印章
      - type: BORDERLESS
    api: api/seals/seals/create_p_template_seal.yml
    extract:
      - sealIdPer1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询个人印章列表-如果已有印章返回印章id
    variables:
      - app_id: $app_id
      - accountId: $standardoid
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealIdPer1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["status_code", 200]

- test:
    name: 创建流程
    variables:
      - app_id: $app_id
      - extend:
      - autoArchive: True
      - businessScene: 无分享id流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: "添加个人签名域-陈凯丽"
    variables:
      - app_id: $app_id
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "流程参与人查询 -signDocDetail"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - queryAccountId: $accountId_ckl
      - resourceShareId: ""
      - authorizerIds: $accountId_ckl
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",1]
      - eq: ["content.data.flowDocs.0.docStatus",1]


- test:
    name: "非流程参与人查询 -signDocDetail"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - resourceShareId: ""
      - authorizerIds: $accountId
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",1]
      - eq: ["content.data.flowDocs.0.docStatus",0]


- test:
    name: "流程参与人查询 -detail_V2"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - queryAccountId: $accountId_ckl
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.flowStatus",1]

- test:
    name: "非流程参与人查询 -detail_V2"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.flowStatus",1]


- test:
    name: "流程参与人查询 -simpleconfig"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - queryAccountId: $accountId_ckl
      - resourceShareId: ""
    api: api/share/simpleconfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList.0.name",查看]
      - eq: ["content.data.pageConfig.configList.0.code",see]
      - eq: ["content.data.pageConfig.configList.0.type",text]

- test:
    name: "非流程参与人查询 -simpleconfig"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - resourceShareId: ""
    api: api/share/simpleconfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[]]


- test:
    name: "流程参与人查询 -config"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - queryAccountId: $accountId_ckl
      - resourceShareId: ""
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]







