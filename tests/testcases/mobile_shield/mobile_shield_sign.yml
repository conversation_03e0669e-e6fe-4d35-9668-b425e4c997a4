- config:
    name: "e签盾签署 - 拿到手机盾任务id"
    base_url: ${ENV(footstone_mobile_shield_url)}
    variables:
      - app_id1: ${ENV(mobile_shield_xuanyuan_appid)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - standardoid: ${ENV(standardoid)}

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 - 泗县深泉纯净水有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId


- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: "文件上传 - 个人借贷合同ckl.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同ckl.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]




- test:
    name: "创建e签盾签署流程 -  createWithSignDocs"
    variables:
      - autoArchive: false
      - businessScene: "创建e签盾流程 -createWithSignDocs "
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":2}
      - contractValidity: "*************"
      - initiatorAccountId: $accountId_ckl
      - createWay: "normal"
      - payerAccountId: null
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/mobile-shield/createWithSignDocs.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.flowId

- test:
    name: 添加个人代企业签署区
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'2', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId

#开启e签盾签署流程
- test:
    name: 开启流程
    variables:
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



#获取可用印章列表
- test:
    name: 轩辕实名：获取签署印章列表
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - accountId: $accountId_ckl
      - app_id: $app_id1
      - loginAccountId: $standardoid
      - authorizerId: $sixian_organize_id

    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]
    extract:
      - sealid: content.data.officialSeals.0.organSeals.0.sealId


      #查询该签署人是否需要意愿
      #- test:
      #    name: 获取实名地址场景：轩辕实名：获取个人实名地址
      #    variables:
      #      - flowId: $flowId1
      #      - accountId: $accountId_ckl
      #      - app_id: $app_id1
      #      #      - scene: 1
      #      - saleSchemaId: 14
      #    api: api/mobile-shield/identifyUrl.yml
      #    validate:
#      - eq: ["content.code",0]
#      - eq: ["content.data.type", "2"]
#      - eq: ["content.data.url", "null"]



#签署
- test:
    name: 签署
    variables:
      - accountId: $standardoid
      - flowId: $flowId1
      - posBean: {addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $sixian_organize_id, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $sealid, signfieldId: $signfieldId, signerOperatorAuthorizerId: $sixian_organize_id, signerOperatorId: $standardoid}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.signfieldSignResult.0.optResult", 0]
    extract:
      - serviceId: content.data.serviceId
      - mobileShieldTaskId: content.data.mobileShieldTaskId


      #- test:
      #    name: 查询异步签署结果
      #    api: api/mobile-shield/requsetResult.yml
      #    variables:
      #      #      - app_id: $app_id
      #      - service_Id: $serviceId
      #    validate:
#      - eq: ["content.code",0]
#      - eq: ["content.message", 成功]
#    extract:
#      - mobileShieldTaskId: ontent.data.signfieldSignResult.mobileShieldTaskId

- test:
    name: "查询手机盾任务"
    variables:
      - flowId: $flowId1
      - mobileShieldTaskId: $mobileShieldTaskId
    api: api/mobile-shield/signflows_queryMobileShieldResult.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "任务正在执行中，请稍后"]


      #- test:
      #    name: "查询手机盾签署任务logid"
      #    variables:
      #      - accountId: $standardoid
      #      - taskId: $mobileShieldTaskId
      #    api: api/mobile-shield/mobileShield_signTask.yml
      #    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", "成功"]
#    extract:
#      - serviceId: content.data.hashList.0.signlogId




















