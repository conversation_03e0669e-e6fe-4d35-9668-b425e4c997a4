#def: createWithSignDocs($autoArchive, $businessScene, $configInfo, $contractValidity, $createWay,$docs, $extend, $initiatorAccountId, $payerAccountId, $signValidity)
request:
  url: ${ENV(footstone_api_url)}/v1/signflows/createWithSignDocs
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    autoInitiate: true
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    createWay: $createWay
    docs: $docs
    extend: $extend
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity

# 创建流程并添加文档, 模板创建流程情况下支持自动发起
