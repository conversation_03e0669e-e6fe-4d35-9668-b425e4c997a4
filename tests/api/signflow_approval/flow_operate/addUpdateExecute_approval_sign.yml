#def: addUpdateExecute_approval_sign($flowId,$addSignfields,$updateSignfields,$signfieldIds,$approvalPolicy,$accountId,$needSignWilling,$dingUser,$securityCode)
request:
  url: ${ENV(item_url)}/v1/signflows/$flowId/signfields/add-Update-Execute
  method: PUT
  headers:
    X-Tsign-Client-Id: "PC_SIMPLE"
    X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
  json:
    {
      "thirdPartyUserId":"zy20190601301",
      "name":"曾艳",
      "idNumber":"362324199301130629",
      "idType":"CRED_PSN_CH_IDCARD",
      "mobile":"***********"
    }

