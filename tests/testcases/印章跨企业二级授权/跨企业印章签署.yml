- config:
    name: 跨企业印章签署
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - file_small: ${ENV(file_id_in_app_id_tengqing_SaaS_with_willing)}
      - file_onlyread: ${ENV(bigfile30M_id_in_app_id_tengqing_SaaS_with_willing)}
      - liangxianhong_stan: ${ENV(orgdy_legal_oid)}
      - yuzan_stan: ${ENV(oid_wang_tsign)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - ckl_saas_oid: ${ENV(standardoid)}
      - ganning_saas_oid: ${ENV(gn_oid_tsign)}
      - sealId_sx_acrossEnterprise: ${ENV(sealId_sx_acrossEnterprise)}
      - sealId_zl: ${ENV(sealId_zl)}
      - sealId_gn: ${ENV(sealId_gn)}
      - sealId_dy: ${ENV(sealId_dy_approval)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}

#跨企业印章签署：yuzan是泗县管理员可以直接签署-ckl不存在二级授权走审批-甘宁存在二级授权直接签署
- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: fhg123456ws
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建甘宁账号"
    variables:
      - thirdPartyUserId: z111zzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId


- test:
    name: "创建签署人王瑶济账号"
    variables:
      - thirdPartyUserId: yuzan20191r5222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_yuzan: content.data.accountId


- test:
    name: "创建机构-泗县深泉纯净水有限公司"
    variables:
      - thirdPartyUserId: sixian2041132
      - creator: $accountId_yuzan
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId




- test:
    name: "创建流程-yuzan是泗县管理员可以直接签署-ckl不存在二级授权走审批-甘宁存在二级授权直接签署"
    variables:
      - extend:
      - autoArchive: False
      - businessScene: 创建流程-yuzan是泗县管理员可以直接签署-ckl不存在二级授权走审批-甘宁存在二级授权直接签署
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '' }
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 添加流程文档 - 1个小文件&2个大文件
    variables:
      - doc1: { encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0 }
      - doc2: { encryption: 0,fileId: $file_onlyread, fileName: "个人借贷合同2.pdf", source: 0 }
      - docs: [ $doc1,$doc2 ]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]


- test:
    name: "添加企业签署-ckl"
    variables:
      - flowId: $sign_flowId1
      - signfields: [ { "fileId": $file_small,"signerAccountId": $accountId_ckl,"actorIndentityType": "2","authorizedAccountId": $sixian_organize_id,"assignedPosbean": true,"order": 1,"posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 400 },"sealType": null,"sealId": "","signType": 1,"sealIds": [ ] } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_ckl: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]


- test:
    name: "添加企业签署-yuzan"
    variables:
      - flowId: $sign_flowId1
      - signfields: [ { "fileId": $file_small,"signerAccountId": $accountId_yuzan,"actorIndentityType": "2","authorizedAccountId": $sixian_organize_id,"assignedPosbean": true,"order": 1,"posBean": { "addSignTime": true,"posPage": "1","posX": 200,"posY": 400 },"sealType": null,"sealId": "","signType": 1,"sealIds": [ ] } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_yuzan: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: "添加企业签署-ganning"
    variables:
      - flowId: $sign_flowId1
      - signfields: [ { "fileId": $file_small,"signerAccountId": $accountId_gan,"actorIndentityType": "2","authorizedAccountId": $sixian_organize_id,"assignedPosbean": true,"order": 1,"posBean": { "addSignTime": true,"posPage": "1","posX": 200,"posY": 200 },"sealType": null,"sealId": "","signType": 1,"sealIds": [ ] } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_ganning: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]


- test:
    name: 开启流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询流程签署区使用印章-签署区不存在完成签署的签署区-流程未归档
    variables:
      - json: {
        "flowId": $sign_flowId1 ,
        "signDocs": [
          ""
        ]
      }
    api: api/RPC/queryFinishFlowParticipants.yml
    validate:
      - eq: [ "status_code", 503 ]
      - eq: [ "content.success", false ]
      - contains: [ "content.message", "流程未完结" ]

#执行签署



- test:
    name: 执行签署-yuzan-提示未做意愿认证
    variables:
      - flowId: $sign_flowId1
      - accountId: $yuzan_stan
      - posBean: { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 }
      - updateSignfield1: { "fileId": $file_small,"signerAccountId": $yuzan_stan,"actorIndentityType": "2","authorizedAccountId": $account_id_sx_tsign,"assignedPosbean": true,"order": 1,"posBean": $posBean,"sealType": "","sealId": $sealId_sx_acrossEnterprise,"crossEnterpriseSeal": true,"signType": 1,"signfieldId": $signfieldId_yuzan }
      - addSignfields: [ ]
      - updateSignfields: [ $updateSignfield1 ]
      - signfieldIds: [ ]
      - dingUser: { }
      - approvalPolicy: 0
      - operator_id: $yuzan_stan
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - contains: [ "content.message","用户未做意愿认证" ]




- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $yuzan_stan
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.bizId",0 ]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $yuzan_stan
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 执行签署-yuzan-印章无跨企业授权
    variables:
      - flowId: $sign_flowId1
      - accountId: $yuzan_stan
      - posBean: { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 }
      - updateSignfield1: { "fileId": $file_small,"signerAccountId": $yuzan_stan,"actorIndentityType": "2","authorizedAccountId": $account_id_sx_tsign,"assignedPosbean": true,"order": 1,"posBean": $posBean,"sealType": "","sealId": $sealId_dy,"crossEnterpriseSeal": true,"signType": 1,"signfieldId": $signfieldId_yuzan }
      - addSignfields: [ ]
      - updateSignfields: [ $updateSignfield1 ]
      - signfieldIds: [ ]
      - dingUser: { }
      - approvalPolicy: 0
      - operator_id: $yuzan_stan
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - contains: [ "content.message","成功" ]
      - eq: [ "content.data.signfieldSignResult.0.optResult", 1 ]
      - contains: [ "content.data.signfieldSignResult.0.failedReason", "印章和签署主体不匹配" ]

- test:
    name: 执行签署-yuzan-签署完成
    variables:
      - flowId: $sign_flowId1
      - accountId: $yuzan_stan
      - posBean: { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 }
      - updateSignfield1: { "fileId": $file_small,"signerAccountId": $yuzan_stan,"actorIndentityType": "2","authorizedAccountId": $account_id_sx_tsign,"assignedPosbean": true,"order": 1,"posBean": $posBean,"sealType": "","sealId": $sealId_sx_acrossEnterprise,"crossEnterpriseSeal": true,"signType": 1,"signfieldId": $signfieldId_yuzan }
      - addSignfields: [ ]
      - updateSignfields: [ $updateSignfield1 ]
      - signfieldIds: [ ]
      - dingUser: { }
      - approvalPolicy: 0
      - operator_id: $yuzan_stan
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - contains: [ "content.message","成功" ]
      - eq: [ "content.data.signfieldSignResult.0.optResult", 0 ]


- test:
    name: 执行签署-ganning-提示未做意愿认证
    variables:
      - flowId: $sign_flowId1
      - accountId: $ganning_saas_oid
      - posBean: { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 }
      - updateSignfield1: { "fileId": $file_small,"signerAccountId": $ganning_saas_oid,"actorIndentityType": "2","authorizedAccountId": $account_id_sx_tsign,"assignedPosbean": true,"order": 1,"posBean": $posBean,"sealType": "","sealId": $sealId_sx_acrossEnterprise,"crossEnterpriseSeal": true,"signType": 1,"signfieldId": $signfieldId_ganning }
      - addSignfields: [ ]
      - updateSignfields: [ $updateSignfield1 ]
      - signfieldIds: [ ]
      - dingUser: { }
      - approvalPolicy: 0
      - operator_id: $ganning_saas_oid
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - contains: [ "content.message","用户未做意愿认证" ]


- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $ganning_saas_oid
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.bizId",0 ]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $ganning_saas_oid
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]



- test:
    name: 执行签署-ganning-签署完成
    variables:
      - flowId: $sign_flowId1
      - accountId: $ganning_saas_oid
      - posBean: { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 }
      - updateSignfield1: { "fileId": $file_small,"signerAccountId": $ganning_saas_oid,"actorIndentityType": "2","authorizedAccountId": $account_id_sx_tsign,"assignedPosbean": true,"order": 1,"posBean": $posBean,"sealType": "","sealId": $sealId_sx_acrossEnterprise,"crossEnterpriseSeal": true,"signType": 1,"signfieldId": $signfieldId_ganning }
      - addSignfields: [ ]
      - updateSignfields: [ $updateSignfield1 ]
      - signfieldIds: [ ]
      - dingUser: { }
      - approvalPolicy: 0
      - operator_id: $ganning_saas_oid
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - contains: [ "content.message","成功" ]
      - eq: [ "content.data.signfieldSignResult.0.optResult", 0 ]

- test:
    name: 执行签署-ckl-直接发起用印审批
    variables:
      - flowId: $sign_flowId1
      - accountId: $ckl_saas_oid
      - posBean: { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 }
      - updateSignfield1: { "fileId": $file_small,"signerAccountId": $ckl_saas_oid,"actorIndentityType": "2","authorizedAccountId": "8fbe5926547e40149978fb8c5a448394","assignedPosbean": true,"order": 1,"posBean": $posBean,"sealType": "","sealId": $sealId_sx_acrossEnterprise,"crossEnterpriseSeal": true,"signType": 1,"signfieldId": $signfieldId_ckl }
      - addSignfields: [ ]
      - updateSignfields: [ $updateSignfield1 ]
      - signfieldIds: [ ]
      - dingUser: { }
      - approvalPolicy: 0
      - operator_id: $ckl_saas_oid
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - contains: [ "content.message","成功" ]
    extract:
      - approveFlowId: content.data.signfieldSignResult.0.approveFlowId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}





- test:
    name: 同意审批-获取审批人意愿地址
    api: api/signflow/signflows/identifyUrlWillWithJson.yml
    variables:
      - accountId: $yuzan_stan
      - approvalId: $approveFlowId
      - json: {
        "bizCtxIds":
          [
            $sign_flowId1     #flowid
          ],
        "bizType": "",
        "redirectUrl": "www.baidu.com",
        "clientType": "PC",
        "wukongOid": $yuzan_stan,
        "app_id": $app_id,
        "space": $yuzan_stan,
        "scene": "2",
        "willTypeHided": "5"
      }

    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 意愿认证-短信验证码发送
    variables:
      - accountId: $yuzan_stan
      - bizId: $bizId
      - approvalId: $approveFlowId
      - bizType: "SIGN"
      - sendType: "SMS"
    api: api/realname/realname/willCreateCodeAuth.yml
    extract:
      - willAuthId3: content.data.willAuthId
    #    teardown_hooks:
    #      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authCode: 123456
      - accountId: $yuzan_stan
      - willAuthId: $willAuthId3
      - bizId: $bizId
      - bizType: "SIGN"
    api: api/realname/realname/checkVerCodeModel.yml
    #    teardown_hooks:
    #      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]


- test:
    name: 同意审批
    variables:
      - approvalId: $approveFlowId
      - accountId: $yuzan_stan
      - securityCode: ""
    api: api/signflow_approval/approval/agreeApproval.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查询流程签署区使用印章-签署区全部完全签署&未归档
    variables:
      - json: {
        "flowId": $sign_flowId1 ,
        "signDocs": [
          ""
        ]
      }
    api: api/RPC/queryFinishFlowParticipants.yml
    validate:
      - eq: [ "status_code", 503 ]
      - eq: [ "content.success", false ]
      - contains: [ "content.message", "流程未完结" ]


- test:
    name: 归档流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [ status_code, 200 ]
      - contains: [ "content.message","成功" ]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询流程签署区使用印章-签署区全部完全签署&已归档
    variables:
      - json: {
        "flowId": $sign_flowId1 ,
        "signDocs": [
          ""
        ]
      }
    api: api/RPC/queryFinishFlowParticipants.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "执行成功" ]
      - eq: [ "content.data.participantList.0.signerAuthorizedAccountName", "泗县深泉纯净水有限公司" ]
      - eq: [ "content.data.participantList.0.sealAuthorizedAccountName", "esigntest-合规化企业A" ]


- test:
    name: 查询流程签署区使用印章-签署区全部完全签署&已归档&文档不存在
    variables:
      - json: {
        "flowId": $sign_flowId1 ,
        "signDocs": [
          "11"
        ]
      }
    api: api/RPC/queryFinishFlowParticipants.yml
    validate:
      - eq: [ "status_code", 503 ]
      - contains: [ "content.message","不属于流程" ]


- test:
    name: 查询流程签署区使用印章-签署区全部完全签署&已归档&指定多签署文档-有签署区
    variables:
      - json: {
        "flowId": $sign_flowId1 ,
        "signDocs": [
          $file_small,$file_onlyread
        ]
      }
    api: api/RPC/queryFinishFlowParticipants.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "执行成功" ]
      - eq: [ "content.data.participantList.0.signerAuthorizedAccountName", "泗县深泉纯净水有限公司" ]
      - eq: [ "content.data.participantList.0.sealAuthorizedAccountName", "esigntest-合规化企业A" ]


- test:
    name: 查询流程签署区使用印章-签署区全部完全签署&已归档&指定单个签署文档-有签署区
    variables:
      - json: {
        "flowId": $sign_flowId1 ,
        "signDocs": [
          $file_small
        ]
      }
    api: api/RPC/queryFinishFlowParticipants.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "执行成功" ]
      - eq: [ "content.data.participantList.0.signerAuthorizedAccountName", "泗县深泉纯净水有限公司" ]
      - eq: [ "content.data.participantList.0.sealAuthorizedAccountName", "esigntest-合规化企业A" ]



- test:
    name: 查询流程签署区使用印章-签署区全部完全签署&已归档&指定单个签署文档-无签署区
    variables:
      - json: {
        "flowId": $sign_flowId1 ,
        "signDocs": [
          $file_onlyread
        ]
      }
    api: api/RPC/queryFinishFlowParticipants.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message", "执行成功" ]
      - len_eq: [ "content.data.participantList", 0 ]

