config:
  name: 多文档并发测试场景-批量签署20份文件
  variables:
    email_1:
    mobile_1: 13260881366
    idType_person: CRED_PSN_CH_IDCARD
    thirdPartyUserId_person_1: autotest${get_randomNo()}
    thirdPartyUserId_person_2: autotest1${get_randomNo()}
    name_person_1: 王瑶济
    idNumber_person_1: 320721199305232634
    app_id: ${ENV(X-Tsign-Open-App-Id)}
    path_pdf: data/个人借贷合同.pdf
    path_pdf2: data/劳动合同书.pdf
    path_pdf3: data/模板劳动合同0001.pdf
    path_pdf4: data/销售合同006.pdf
    path_pdf5: data/three_page.pdf
    mobileNew: $mobile_1

testcases:

  多文档并发测试场景-批量签署20份文件:
    testcase: testcases/signflow_scene/sign_20docs_createflow.yml
#    签署登陆:
#       testcase: testcases/signflow_scene/userLogin.yml
#
#    意愿认证:
#       testcase: testcases/signflow_scene/willing.yml
#       variables:
#      flowId: $flowId
#      personOid_realname: $personOid_realname

#    name: 签署
#    variables:
#       personOid: $personOid_realname
#       flowId: $flowId
#    testcase: testcases/signflow_scene/sign_20docs_sign.yml
