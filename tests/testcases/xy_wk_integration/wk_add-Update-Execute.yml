- config:
    name: "悟空轩辕整合"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_in_wukong_ckl)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(mobile_shield_wukong_appid)}
      - path_pdf1: data/个人借贷合同.pdf
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3' }

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "文件直传创建一个无电子签名的文件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", "成功" ]

#V3接口创建签署流程，不取appid配置数据，流程强制签署人、签署主体实名意愿
- test:
    name: V3_createFlowOneStep - "个人签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 轩辕悟空整合-个人1的个人签，个人1代企业A签，个人2代企业B签",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: "" },
                     signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/xy_wk_integration/V3_createFlowOneStep.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId1
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId1_signerAccount: content.data.signfields.0.signerAccountId
      - flowId1_signfieldId: content.data.signfields.0.signfieldId



- test:
    name: 查询签署人印章
    variables:
      - accountId: $flowId1_signerAccount
    api: api/seals/seals/select_p_seals.yml
    extract:
      - flowId1_signerAccount_seal: content.data.seals.0.sealId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: 签署 - 签署失败，签署人信息校验失败
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId1
      - posBean: { addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0 }
      - addSignfields: [ ]
      - updateSignfield: { authorizedAccountId: $flowId1_signerAccount, extendFieldDatas: { }, posBean: $posBean, sealFileKey: '', sealId: $flowId1_signerAccount_seal, signfieldId: $flowId1_signfieldId, signerOperatorAuthorizerId: $accountId_ckl, signerOperatorId: $accountId_ckl }
      - updateSignfields: [ $updateSignfield ]
      - signfieldIds: [ ]
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1437157 ]
      - contains: [ "content.message", "签署人信息校验失败" ]


- test:
    name: 签署 - v1签署接口签署-签署失败，提示用户未做意愿认证
    variables:
      - accountId: $flowId1_signerAccount
      - flowId: $flowId1
      - posBean: { addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0 }
      - addSignfields: [ ]
      - updateSignfield: { authorizedAccountId: $flowId1_signerAccount, extendFieldDatas: { }, posBean: $posBean, sealFileKey: '', sealId: $flowId1_signerAccount_seal, signfieldId: $flowId1_signfieldId, signerOperatorAuthorizerId: $flowId1_signerAccount, signerOperatorId: $flowId1_signerAccount }
      - updateSignfields: [ $updateSignfield ]
      - signfieldIds: [ ]
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1436201 ]
      - eq: [ "content.message", "用户未做意愿认证" ]

- test:
    name: 签署 - v3签署接口签署-签署失败，提示用户未做意愿认证
    variables:
      - accountId: $flowId1_signerAccount
      - flowId: $flowId1
      - posBean: { addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0 }
      - addSignfields: [ ]
      - updateSignfield: { authorizedAccountId: $flowId1_signerAccount, extendFieldDatas: { }, posBean: $posBean, sealFileKey: '', sealId: $flowId1_signerAccount_seal, signfieldId: $flowId1_signfieldId, signerOperatorAuthorizerId: $flowId1_signerAccount, signerOperatorId: $flowId1_signerAccount }
      - updateSignfields: [ $updateSignfield ]
      - signfieldIds: [ ]
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1436201 ]
      - eq: [ "content.message", "用户未做意愿认证" ]


#意愿认证
- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $flowId1_signerAccount
      - flowId: $flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.bizId",0 ]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $flowId1_signerAccount
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 签署 - 用户做完意愿认证，签署成功
    variables:
      - accountId: $flowId1_signerAccount
      - flowId: $flowId1
      - posBean: { addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0 }
      - addSignfields: [ ]
      - updateSignfield: { authorizedAccountId: $flowId1_signerAccount, extendFieldDatas: { }, posBean: $posBean, sealFileKey: '', sealId: $flowId1_signerAccount_seal, signfieldId: $flowId1_signfieldId, signerOperatorAuthorizerId: $flowId1_signerAccount, signerOperatorId: $flowId1_signerAccount }
      - updateSignfields: [ $updateSignfield ]
      - signfieldIds: [ ]
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfieldSignResult.0.optResult", 0 ]



#验签
- test:
    name: 查询流程文档 - 签署完成后的流程文档的filekey
    variables:
      - flowId: $flowId1
      - step: 0
    api: api/iterate_cases/queryFlowInfoWithDocEvi.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - flowId1_signedFilekey: content.data.attachmentList.0.fileKey


- test:
    name: 根据filekey获取签署完成的fileId
    variables:
      - accountId: ""
      - fileName: 个人借贷合同.pdf
      - fileKey: $flowId1_signedFilekey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - flowId1_signedFileId: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 签署完成流程文档验签
    api: api/signflow/signflows/verifySignature.yml
    variables:
      - fileId: $flowId1_signedFileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - str_eq: [ "content.data.signatures.0.signature.validate",true ]
      - str_eq: [ "content.data.signatures.0.signature.modify",false ]
#验签结束


#V2接口创建签署流程，取appid配置数据，流程不会强制签署人、签署主体实名意愿
- test:
    name: V2_createFlowOneStep - "个人签"
    variables:
      - doc: { fileId: $fileId1, fileName: "个人借贷合同.pdf" }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 轩辕悟空整合-个人1的个人签，个人1代企业A签，个人2代企业B签",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: "" },
                     signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId2
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId2_signfieldId: content.data.signfields.0.signfieldId


- test:
    name: 查询个人印章列表-如果已有印章返回印章id
    variables:
      - app_id: $app_id
      - accountId: $accountId_ckl
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealIdPer1: content.data.seals.0.sealId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: 查询时间流信息-签署中
    api: api/v3/signflows/flowId/timeStream.yml
    variables:
      - flowId: $flowId2
      - params: queryAccountId=$accountId_ckl
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - str_eq: [ "content.data.signers.0.operateType",None ]

- test:
    name: 查询时间流信息-非流程参与人
    api: api/v3/signflows/flowId/timeStream.yml
    variables:
      - flowId: $flowId2
      - params: queryAccountId=${ENV(accountId_in_SaaS)}
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 签署 - 签署成功，用户不需要做意愿认证
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId2
      - posBean: { addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0 }
      - addSignfields: [ ]
      - updateSignfield: { authorizedAccountId: $accountId_ckl, extendFieldDatas: { }, posBean: $posBean, sealFileKey: '', sealId: $sealIdPer1, signfieldId: $flowId2_signfieldId, signerOperatorAuthorizerId: $accountId_ckl, signerOperatorId: $accountId_ckl }
      - updateSignfields: [ $updateSignfield ]
      - signfieldIds: [ ]
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.signfieldSignResult.0.optResult", 0 ]


#验签
- test:
    name: 查询流程文档 - 签署完成后的流程文档的filekey
    variables:
      - flowId: $flowId2
      - step: 0
    api: api/iterate_cases/queryFlowInfoWithDocEvi.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - flowId2_signedFilekey: content.data.attachmentList.0.fileKey


- test:
    name: 根据filekey获取签署完成的fileId
    variables:
      - accountId: ""
      - fileName: 个人借贷合同.pdf
      - fileKey: $flowId2_signedFilekey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - flowId2_signedFileId: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 签署完成流程文档验签
    api: api/signflow/signflows/verifySignature.yml
    variables:
      - fileId: $flowId2_signedFileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - str_eq: [ "content.data.signatures.0.signature.validate",true ]
      - str_eq: [ "content.data.signatures.0.signature.modify",false ]
#验签结束

- test:
    name: 查询时间流信息-已归档
    api: api/v3/signflows/flowId/timeStream.yml
    variables:
      - flowId: $flowId2
      - params: queryAccountId=$accountId_ckl
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.signers.0.operateType",4 ]