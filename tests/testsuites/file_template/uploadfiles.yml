- config:
    name: 上传文件测试
    parameters:
      - contentMd5-contentType-fileName-file_path:
          - ["${get_file_base64_md5($path_pdf)}", "application/pdf", "test.pdf", $path_pdf]
          - ["${get_file_base64_md5($path_jpg)}", "image/jpeg", "psb.jpg", $path_jpg]
          - ["${get_file_base64_md5($path_doc)}", "application/octet-stream", "account.docx", $path_doc]
    variables:
      - fileSize: "1111"
      - pageNo: 1
      - pageSize: 100
      - path_pdf: "data/test.pdf"
      - path_jpg: "data/psb.jpg"
      - path_doc: "data/account.docx"
    request:
      base_url: ${ENV(footstone_api_url)}
      headers:
        Content-Type: "application/json"
- test:
    name: 上传文件
    variables:
      contentMd5: $contentMd5
      contentType: $contentType
      fileName: $fileName
      fileSize: $fileSize
      pageNo: $pageNo
      pageSize: $pageSize
      file_path: $file_path
    testcase: testcases/file_template/uploadfile_action.yml

