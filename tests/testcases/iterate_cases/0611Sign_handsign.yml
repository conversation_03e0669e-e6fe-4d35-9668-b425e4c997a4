- config:
    name: "0611多人多主体测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''

- test:
    name: "xuanyuan_realname下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建黄华南账号"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: tianyi20191111411321213
      - name: 黄华南
      - idNumber: 330328199308301118
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - tianyi_accountId: content.data.accountId

- test:
    name: "文件直传创建一个文件"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: "创建签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds字段
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $standard_sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_open_sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "个人-玉瓒"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "法人-草谷"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"3","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":200,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "经办人-天一"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$tianyi_accountId,"actorIndentityType":"4","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":300,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "企业-泗县深泉纯净水有限公司"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":300},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 2人1企业签署-获取签署地址
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - urlType: 0
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - organizeId: $sixian_organize_id
    api: api/iterate_cases/getShowUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]