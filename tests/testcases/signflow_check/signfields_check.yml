- config:
    name: 流程增删改查校验
    base_url: ${ENV(base_url)}
    variables:
      - orgName: 东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: 13344445555
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - personOid1: 4468fe9323bc4bfeaf5f659af14b6754
      - personOid2: 824708b5195546eab28565ebb0203a1e
      - personOid3: 310913bf68594f8bb7c457215e0b4831
      - orgOid1: 4d4352cd0de849ab9daa507397086bf1
      - fileId1: 37492a3be0c64282b834a48f77db3c89
      - m1: "19922222222"
      - m2: "19800000000"
      - e1: <EMAIL>
      - e2: <EMAIL>
      - pOid1: ${getOid($m1)}
      - pOid2: ${getOid($m2)}
      - pOid3: ${getOid($e1)}
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:
      - m3: "***********"
      - pOid_m: ${getOid($m3)}
      - tPUserId1: autotest1${get_randomNo()}
      - appid_xuanyuan: ${ENV(appId_xuanyuan)}
      - m4: "***********"
      - pOid_m1: ${getOid($m4)}
      - client_id: pc

- test:
    name: 数据准备：创建流程
    variables:
      - autoArchive: False
      - businessScene: 多人多文档
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
      - extend: {}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询企业
    variables:
      - name: esigntest东营伟信建筑安装工程有限公司
    api: api/user/organizations/get_organizations_by_name.yml
    extract:
      - orgOid2: content.data.items.0.ouid


- test:
    name: 查询企业印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_c_seals.yml
    variables:
      - orgId: $orgOid2
    extract:
      - o1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $pOid_m
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $pOid_m
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 批量添加流程文档：文档2
    variables:
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc2]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 批量添加流程签署区：fileId不存在
    variables:
      - fileId: 34fdsfdfd
      - flowId: $sign_flowId
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId,$flowId,$posBean,1,$pOid_m,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'文档不存在']

- test:
    name: 批量添加流程签署区：fileId存在，但不在流程中
    variables:
      - flowId: $sign_flowId
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$pOid_m,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","文档不存在"]

- test:
    name: 批量添加流程签署区：flowId不存在
    variables:
      - flowId: 235454dfd
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,1,$pOid_m,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",流程不存在]

- test:
    name: 批量添加流程签署区：签署人账号不存在
    variables:
      - flowId: $sign_flowId
      - signerAccountId: refd343rds
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,1,$signerAccountId,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'当前签署流程指定账号不存在/已注销（账号：refd343rds），请检查后重试']
      - eq: ["content.code",1435324]

- test:
    name: 批量添加流程签署区：自动执行，签约主体=空，报错
    variables:
      - flowId: $sign_flowId
      - signerAccountId: $pOid_m
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,1,$fileId2,$flowId,$posBean,1,$signerAccountId,,$p1_sealId,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","签署区设置自动执行，签署主体不允许为空"]

- test:
    name: 批量添加流程签署区：签约主体类别=个人，签约主体=其他个人，不一致后端处理成签署主体=签署人
    variables:
      - flowId: $sign_flowId
      - signerAccountId: $pOid_m
      - authorizedAccountId: $personOid2
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,1,$signerAccountId,$authorizedAccountId,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    extract:
      - signfieldId1: content.data.signfieldIds.0

- test:
    name: 批量添加流程签署区：查询-当签署人和签署主体不一致，则签署主体=签署人
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $sign_flowId
      - accountId:
      - signfieldIds: $signfieldId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signfields.0.signerAccountId",$pOid_m]
      - eq: ["content.data.signfields.0.authorizedAccountId",$pOid_m]

- test:
    name: 批量添加流程签署区：签约主体类别=个人，签约人=企业，报错
    variables:
      - flowId: $sign_flowId
      - signerAccountId: $orgOid2
      - authorizedAccountId: ''
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,1,$signerAccountId,$authorizedAccountId,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","手动签署的签署人必须是个人"]

- test:
    name: 批量添加流程签署区：签约主体类别=个人，签约主体=企业，报错
    variables:
      - flowId: $sign_flowId
      - signerAccountId: $pOid_m
      - authorizedAccountId: $orgOid2
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,1,$signerAccountId,$authorizedAccountId,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    extract:
      - signfieldId1: content.data.signfieldIds.0

- test:
    name: 批量添加流程签署区：查询-当签署人和签署主体不一致，则签署主体=签署人
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $sign_flowId
      - accountId:
      - signfieldIds: $signfieldId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signfields.0.signerAccountId",$pOid_m]
      - eq: ["content.data.signfields.0.authorizedAccountId",$pOid_m]

- test:
    name: 批量添加流程签署区：签约主体类别=企业，签约主体=空，报错
    variables:
      - flowId: $sign_flowId
      - signerAccountId: $pOid_m
      - authorizedAccountId: ''
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(1,0,$fileId2,$flowId,$posBean,1,$signerAccountId,$authorizedAccountId,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","缺少签署主体信息"]

- test:
    name: 批量添加流程签署区：签约主体类别=企业，签约主体=个人，报错
    variables:
      - flowId: $sign_flowId
      - signerAccountId: $pOid_m
      - authorizedAccountId: $pOid_m
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(1,0,$fileId2,$flowId,$posBean,1,$signerAccountId,$authorizedAccountId,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","签署主体不是企业"]

- test:
    name: 批量添加流程签署区：签约主体类别=2-仅企业，签约主体类别=个人，签约主体=个人
    variables:
      - flowId: $sign_flowId
      - signerAccountId: $pOid_m
      - authorizedAccountId: $orgOid2
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(2,0,$fileId2,$flowId,$posBean,1,$signerAccountId,$authorizedAccountId,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: 批量添加流程签署区：assignedItem，为空，默认false，允许删除该签署区
    variables:
      - flowId: $sign_flowId
      - signfield: {"actorIndentityType": 0, "assignedPosbean": false, "assignedSeal": false, "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId,  "posBean": {}, "sealFileKey": "", "sealId": "", "sealType": 1, "signType": 0}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: 批量删除流程签署区：成功
    api: api/signflow/signfields/signfieldsDelete.yml
    variables:
      - flowId: $sign_flowId
      - signfieldId: $signfieldId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.deleteResults.0.optResult",0]
      - eq: ["content.data.deleteResults.0.failedReason",null]

- test:
    name: 批量添加流程签署区：assignedItem，false，允许删除该签署区
    variables:
      - flowId: $sign_flowId
      - signfield: {"actorIndentityType": 0, "assignedItem": false, "assignedPosbean": false, "assignedSeal": false, "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId,  "posBean": {}, "sealFileKey": "", "sealId": "", "sealType": 1, "signType": 0}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: 批量删除流程签署区：成功
    api: api/signflow/signfields/signfieldsDelete.yml
    variables:
      - flowId: $sign_flowId
      - signfieldId: $signfieldId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.deleteResults.0.optResult",0]
      - eq: ["content.data.deleteResults.0.failedReason",null]

      #- test:
      #    name: 批量添加流程签署区：assignedItem，true，不允许删除该签署区
      #    variables:
      #      - signfield: {"actorIndentityType": 0, "assignedItem": true, "assignedPosbean": false, "assignedSeal": false, "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId, "order": 0, "posBean": {}, "sealFileKey": "", "sealId": "", "sealType": 1, "signType": 0}
      #      - signfields: [$signfield]
      #    api: api/signflow/signfields/signfieldsCreate.yml
      #    extract:
      #      - signfieldId1: content.data.signfieldIds.0
      #    validate:
      #      - contains: ["content.message","成功"]
      #
      #- test:
      #    name: 批量删除流程签署区：失败——有bug
      #    api: api/signflow/signfields/signfieldsDelete.yml
      #    validate:
#      - eq: ["content.data.deleteResults.0.optResult",1]
#      - eq: ["content.data.deleteResults.0.failedReason",null]

- test:
    name: 批量添加流程签署区：assignedPosbean，为空，默认false，允许更新签署区
    variables:
      - flowId: $sign_flowId
      - signfield: {"actorIndentityType": 0,  "assignedPosbean": "", "assignedSeal": false, "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId,  "posBean": {}, "sealFileKey": "", "sealId": "", "sealType": 1, "signType": 0}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: 更新流程签署区：成功
    variables:
      - flowId: $sign_flowId
      - posBean1: {addSignTime: True, key: '', posPage: 1, posX: 300, posY: 200, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_update_data($pOid_m, , $posBean1, ,,$signfieldId1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.updateResults.0.optResult",0]
      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 更新流程签署区-增加签署时间：成功
    variables:
      - flowId: $sign_flowId
      - posBean1: {addSignTime: True, key: '', posPage: 1, posX: 300, posY: 200, qrcodeSign: True, width: 0,"signDateBean": {"fontName":"corpos","fontSize": 120,"format": "YYYY-MM-DD hh:mm:ss","posX": 0,"posY": 0}}
      - signfield1: ${gen_signfield_update_data($pOid_m, , $posBean1, ,,$signfieldId1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.updateResults.0.optResult",0]
      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 批量添加流程签署区：assignedPosbean，True,不允许更改位置
    variables:
      - flowId: $sign_flowId
      - signfield: {"actorIndentityType": 0, "assignedPosbean": true, "assignedSeal": false, "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId,  "posBean": {}, "sealFileKey": "", "sealId": "", "sealType": 1, "signType": 0}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

      #- test:
      #    name: 更新流程签署区：失败——有bug
      #    variables:
      #      - posBean1 : {addSignTime: True, key: '', posPage: 1, posX: 300, posY: 200, qrcodeSign: True, width: 0}
      #      - signfield1: ${gen_signfield_update_data($pOid_m, , $posBean1, ,,$signfieldId1)}
      #      - signfields: [$signfield1]
      #    api: api/signflow/signfields/signfieldsUpdate.yml
      #    validate:
#      - eq: ["content.data.updateResults.0.optResult",1]
#      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 批量添加流程签署区：assignedPosbean，False,允许更改位置
    variables:
      - flowId: $sign_flowId
      - signfield: {"actorIndentityType": 0, "assignedPosbean": false, "assignedSeal": false, "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId,  "posBean": {}, "sealFileKey": "", "sealId": "", "sealType": 1, "signType": 0}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: 更新流程签署区：成功
    variables:
      - flowId: $sign_flowId
      - posBean1: {addSignTime: True, key: '', posPage: 1, posX: 300, posY: 200, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_update_data($pOid_m, , $posBean1, ,,$signfieldId1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.updateResults.0.optResult",0]
      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 批量添加流程签署区：assignedSeal，为空，默认false,允许更新印章
    variables:
      - flowId: $sign_flowId
      - signfield: {"actorIndentityType": 0, "assignedPosbean": false, "assignedSeal": "", "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId,  "posBean": {}, "sealFileKey": "", "sealId": "", "sealType": 1, "signType": 0}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: 更新流程签署区：更新印章成功
    variables:
      - flowId: $sign_flowId
      - posBean: {}
      - signfield1: ${gen_signfield_update_data($pOid_m, , $posBean, ,$p1_sealId, $signfieldId1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.updateResults.0.optResult",0]
      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 批量添加流程签署区：assignedSeal，True,不允许更新印章
    variables:
      - flowId: $sign_flowId
      - signfield: {"actorIndentityType": 0,  "assignedPosbean": false, "assignedSeal": true, "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId,  "posBean": {}, "sealFileKey": "", "sealId": $p1_sealId, "sealType": 1, "signType": 0}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

      #- test:
      #    name: 更新流程签署区：更新印章失败——有bug
      #    variables:
      #      - posBean: {}
      #      - signfield1: ${gen_signfield_update_data($pOid_m, , $posBean, ,$p1_sealId, $signfieldId1)}
      #      - signfields: [$signfield1]
      #    api: api/signflow/signfields/signfieldsUpdate.yml
      #    validate:
#      - eq: ["content.data.updateResults.0.optResult",1]
#      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 批量添加流程签署区：assignedSeal，False,允许更新印章
    variables:
      - flowId: $sign_flowId
      - signfield: {"actorIndentityType": 0,  "assignedPosbean": false, "assignedSeal": false, "signerAccountId": $pOid_m, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId2, "flowId": $flowId,  "posBean": {}, "sealFileKey": "", "sealId": "", "sealType": 1, "signType": 0}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: 更新流程签署区：更新印章成功
    variables:
      - flowId: $sign_flowId
      - posBean: {}
      - signfield1: ${gen_signfield_update_data($pOid_m, , $posBean, ,$p1_sealId, $signfieldId1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.updateResults.0.optResult",0]
      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 批量添加流程签署区：signType签署类型，0-不限,posbean可以为空
    variables:
      - flowId: $sign_flowId
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,0,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]

- test:
    name: 批量添加流程签署区：signType签署类型，1-单页签署，page只能传1个，报错
    variables:
      - flowId: $sign_flowId
      - posBean1: {addSignTime: True, key: '', posPage: '1,2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean1,1,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","参数错误: 盖章位置超出文件范围，无法盖章"]

- test:
    name: 批量添加流程签署区：signType签署类型，1-单页签署，page,x,y缺少，报错
    variables:
      - flowId: $sign_flowId
      - posBean1: {"K":"V"}
      - signfield1: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean1,1,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","参数错误: 盖章位置超出文件范围，无法盖章"]

- test:
    name: 批量添加流程签署区：signType签署类型，1-单页签署，posPage缺少，报错
    variables:
      - flowId: $sign_flowId
      - posBean2: {key: '', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield2: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean2,1,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield2]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","参数错误: 盖章位置超出文件范围，无法盖章"]

- test:
    name: 批量添加流程签署区：signType签署类型，1-单页签署，posX缺少，报错
    variables:
      - flowId: $sign_flowId
      - posBean3: {addSignTime: True, key: '', posPage: '2', posY: 200, qrcodeSign: True, width: 0}
      - signfield3: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean3,1,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield3]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","参数错误: 盖章位置超出文件范围，无法盖章"]

- test:
    name: 批量添加流程签署区：signType签署类型，1-单页签署，posY缺少，报错
    variables:
      - flowId: $sign_flowId
      - posBean4: {addSignTime: True, key: '', posPage: '2', posX: 200, qrcodeSign: True, width: 0}
      - signfield4: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean4,1,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield4]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","参数错误: 盖章位置超出文件范围，无法盖章"]

- test:
    name: 批量添加流程签署区：signType签署类型，1-单页签署，page,x,y必填，成功
    variables:
      - flowId: $sign_flowId
      - posBean5: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield5: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean5,1,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield5]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 批量添加流程签署区：signType签署类型，2-骑缝签署，posY必填，成功
    variables:
      - flowId: $sign_flowId
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,2,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 批量添加流程签署区：signType签署类型，2-骑缝签署，posY缺少，报错
    variables:
      - flowId: $sign_flowId
      - posBean2: {addSignTime: True, key: '', posPage: '2', posX: 400, qrcodeSign: True, width: 0}
      - signfield2: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean2,2,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield2]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","骑缝签署：必须指定y"]

- test:
    name: 批量添加流程签署区：signType签署类型，为空，报错
    variables:
      - flowId: $sign_flowId
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","未定义的signType"]

- test:
    name: 批量添加流程签署区：不支持的fontName，报错
    variables:
      - flowId: $sign_flowId
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0,"signDateBean": {"fontName":"Microsoft YaHei Light","fontSize": 120,"format": "YYYY-MM-DD hh:mm:ss","posX": 0,"posY": 0}}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,2,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message","参数错误: 不支持的字体类型"]

- test:
    name: 批量添加流程签署区：不传签署时间的坐标y轴，报错
    variables:
      - flowId: $sign_flowId
      - posBean: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0,"signDateBean": {"fontName":"","fontSize": 120,"format": "YYYY-MM-DD hh-mm-ss","posX": 0}}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,2,$pOid_m,,$p1_sealId,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message","签章日期页码或坐标格式不正确"]