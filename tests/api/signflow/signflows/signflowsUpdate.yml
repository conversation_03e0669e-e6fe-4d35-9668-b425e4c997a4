#def: signflowsUpdate($flowId, $autoArchive, $businessScene, $configInfo, $contractValidity, $extend, $initiatorAccountId, $payerAccountId, $signValidity)
request:
  url:  ${ENV(item_url)}/v1/signflows/$flowId
  method: PUT
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    extend: $extend
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity
validate:
  - eq: [status_code, 200]

#删除流程
