- config:
    name: "20201015迭代接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/劳动合同书12.pdf
      - group: ''
      - app_Id1: ${ENV(wukong_no_realname)}
      - app_Id2: ${ENV(xuanyuan_realname)}

- test:
    name: "wukong_no_realname下创建签署人王瑶济账号"
    variables:
      - app_id: $app_Id1
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建发起人梁贤红账号"
    variables:
      - app_id: $app_Id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "文件直传创建一个文件,当前项目下的appId"
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf2)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf2)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf2)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf2)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: "一步发起签署-autoInitiate不传"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"docs":[{"fileId":$fileId1,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId1,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "一步发起签署-autoInitiate传空字符串"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"docs":[{"fileId":$fileId1,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":"","businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId1,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "一步发起签署-autoInitiate传null"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"docs":[{"fileId":$fileId1,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":null,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId1,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "一步发起签署-autoInitiate传true"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"docs":[{"fileId":$fileId1,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId1,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "一个列表-发起签署前-批量参与人二要素校验 uniqueId不传"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王瑶济"}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "一个列表-发起签署前-批量参与人二要素校验 uniqueId传null"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王瑶济","uniqueId":null}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "一个列表-发起签署前-批量参与人二要素校验 uniqueId传空字符串"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王瑶济","uniqueId":""}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435201]
      - contains: ["content.message","用户中心查询账户失败:"]

- test:
    name: "一个列表-发起签署前-批量参与人二要素校验 uniqueId传一个手机号"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王瑶济","uniqueId":"***********"}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "一个列表两条数据-发起签署前-批量参与人二要素校验 一条数据不传uniqueId字段"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王瑶济"},{"name":"王瑶济","uniqueId":"***********"}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "/v1/signflows/{flowId}/executeUrl  扩展字段无initiatorAuthorizedAccountIds"
    variables:
      - app_id: ${ENV(exceptAppId)}
      - flowId: ${ENV(exceptionFlowId)}
      - accountId: ${ENV(exceptionAccountId)}
      - urlType: 0
      - multiSubject: true
      - organizeId:
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "/v2/signflows/{flowId}/executeUrl  扩展字段无initiatorAuthorizedAccountIds"
    variables:
      - app_id: ${ENV(exceptAppId)}
      - flowId: ${ENV(exceptionFlowId)}
      - accountId: ${ENV(exceptionAccountId)}
      - urlType: 0
      - multiSubject: true
      - organizeId:
      - compressContext: true
    api: api/iterate_cases/v2_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "/v1/signflows/{flowId}/getShowUrl  扩展字段无initiatorAuthorizedAccountIds"
    variables:
      - app_id: ${ENV(exceptAppId)}
      - flowId: ${ENV(exceptionFlowId)}
      - platform: 5
      - operatorId: ${ENV(exceptionAccountId)}
      - scenario: 0
    api: api/iterate_cases/v1_getShowUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "login接口context为undefined"
    variables:
      - context: undefined
    api: api/iterate_cases/login.yml
    validate:
      - eq: [status_code, 200]

#- test:
#    name: "context为None"
#    variables:
#        - context:
#    api: api/iterate_cases/login.yml
#    validate:

- test:
    name: "login接口context为空字符串"
    variables:
      - context: ""
    api: api/iterate_cases/login.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: "wukong_no_realname下创建签署人"
    variables:
      - app_id: $app_Id1
      - thirdPartyUserId: '**********'
      - name: ''
      - idNumber: ''
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ''
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - new_account_id: content.data.accountId

- test:
    name: "一步发起签署-autoInitiate传true"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"docs":[{"fileId":$fileId1,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$new_account_id,"authorizedAccountId":$new_account_id},"signfields":[{"autoExecute":false,"fileId":$fileId1,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 注销刚建立的账号
    variables:
      - app_id: $app_Id1
      - accountId: $new_account_id
    api: api/user/account_third/xy_del_account.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: "查询可用的印章列表-账号已注销无法查询"
    variables:
      - app_id: $app_Id1
      - flowId: $flowId
      - accountId: $new_account_id
      - authorizerId: $new_account_id
      - signerAccountId: $new_account_id
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435325]
      - contains: ["content.message",'当前流程指定账号异常（账号：${new_account_id}），请联系发起方撤回重发']