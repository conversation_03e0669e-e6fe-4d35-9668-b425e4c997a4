- config:
    name: "签署前后端性能优化二期测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US
      - app_Id1: ${ENV(wukong_forcewill)}
      - random_16: ${get_randomNo_16()}
      - random_32: ${get_randomNo_32()}
      - random_letter: ${random_letter()}
      - random_str: ${random_str()}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - extend: {}
      - pOid_m: ${getOid($m3)}
      - m1: "19922222222"
      - m2: "19800000000"
      - e1: <EMAIL>
      - e2: <EMAIL>
      - pOid1: ${getOid($m1)}
      - pOid2: ${getOid($m2)}
      - pOid3: ${getOid($e1)}
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:
      - m3: "***********"

- test:
    name: "创建签署人账号 - 王瑶济"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_1
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId: content.data.accountId

- test:
    name: "创建王瑶济个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - accountId: $accountId
      - color: "RED"
      - height: 2
      - alias: "王瑶济的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 - 泗县深泉纯净水有限公司"
    variables:
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId


- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: "文件上传 - 个人借贷合同ckl.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同ckl.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}


#上传附件
- test:
    name: "上传附件 - 附件2.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf4)}
      - contentType: application/pdf
      - fileName: 附件2.pdf
      - fileSize: ${get_file_size($path_pdf4)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId4: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf4)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf4)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(8)}



#查询签署中的流程
- test:
    name: 一步到位创建流程 - 1个个人签名域、1个企业签名域
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "个人借贷合同ckl.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1,$doc2]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $sixian_organize_id}, signfields: [  { autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 },{signOrder: 1,signerAccount: {  signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId2,   posBean: { posPage: 1,  posX: 220,   posY: 300  }, signType: 0,   width: 150 }],  thirdOrderNo: 779 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId2
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "查询签署中的流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId2
      - queryAccountId: $accountId_ckl
      - authorizerIds: $accountId_ckl
    api: api/signflow/flowDocuments/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",1]



#查询未开启的流程
- test:
    name: 创建流程（不开启）
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $sixian_organize_id}, signfields: [  { autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]

- test:
    name: "查询签署流程 - 未开启流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId3
      - queryAccountId: $accountId
      - authorizerIds: $sixian_organize_id
    api: api/signflow/flowDocuments/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",0]

#查询撤回的流程
- test:
    name: 创建流程
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $sixian_organize_id}, signfields: [  { autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flowId13: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId13
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 撤销流程
    api: api/signflow/signflows/signflowsRevoke.yml
    variables:
      - flowId: $sign_flowId13
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "查询签署流程 - 已撤销流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId13
      - queryAccountId: $accountId
      - authorizerIds: $sixian_organize_id
    api: api/signflow/flowDocuments/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",3]


#查询拒签的流程
- test:
    name: 数据准备-流程草稿状态：创建流程和文档
    variables:
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - autoArchive: False
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - sign_flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 数据准备-流程草稿状态：添加流程签署区
    variables:
      - flowId: $sign_flowId4
      - posBean: {}
      - autoExecute: 0
      - signfield1: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$accountId,,,)} #个人签
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId6: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 拒签流程
    variables:
      - flowId: $sign_flowId4
      - refuseReason: '拒签不需要理由No Reason'
      - signfieldId: $signfieldId6
    api: api/signflow/signfields/signfieldsRefuse.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "查询签署流程 - 已拒签流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId4
      - queryAccountId: $accountId
      - authorizerIds: $accountId
    api: api/signflow/flowDocuments/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",7]

#查询已完成的流程
- test:
    name: "查询签署流程 - 已完成流程"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(finish_flowid)}
      - queryAccountId: ${ENV(finish_accountid)}
      - authorizerIds: ${ENV(finish_authorizerId)}
    api: api/signflow/flowDocuments/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]

#查询已过期的流程
- test:
    name: "查询签署流程 - 已过期流程"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(expire_flowid)}
      - queryAccountId: ${ENV(expire_accountid)}
      - authorizerIds: ${ENV(expire_authorizerId)}
    api: api/signflow/flowDocuments/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",5]



#查询已删除的流程
- test:
    name: 数据准备-流程草稿状态：创建流程和文档
    variables:
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - autoArchive: False
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - sign_flowId7: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 数据准备-流程草稿状态：添加流程签署区
    variables:
      - flowId: $sign_flowId7
      - posBean: {}
      - autoExecute: 0
      - signfield1: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$accountId,,,)} #个人签
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId7: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]



- test:
    name: "删除流程"
    api: api/signflow/signflows/signflowsDelete.yml
    variables:
      - flowId: $sign_flowId7
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询签署流程 - 已删除的流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId7
      - queryAccountId: $accountId
      - authorizerIds: $accountId
    api: api/signflow/flowDocuments/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",6]





