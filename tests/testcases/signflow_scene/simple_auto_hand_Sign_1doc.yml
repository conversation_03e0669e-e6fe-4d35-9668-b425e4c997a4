- config:
    name: 简化版单文档场景：对接平台自动签署-用户自动签署-用户手动签署
    base_url: ${ENV(footstone_api_url)}
    variables:
      - client_id: pc
- test:
    name: 简化版单文档场景：创建流程
    variables:
      - autoArchive: False
      - businessScene: 简化版单文档场景
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加签署区拆分场景-单文档：添加流程文档
    variables:
      - flowId: $flowId3
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：查询平台印章
    api: api/seals/seals/select_plat_seals.yml
    extract:
      - plat_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版单文档场景：对接平台自动签署，单页签署
    variables:
      - flowId: $flowId3
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：对个人账户1静默授权
    variables:
      - accountId: $personOid1
      - type: silent
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版单文档场景：用户自动签署,单页签署
    variables:
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1, 'order':1,  'posBean':$posBean, 'sealId':$p1_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：添加用户手动签署
    variables:
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: { 'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版单文档场景：更新签署区
    variables:
      - posBean4: {'posPage':'1','posX':300, 'posY':600}
      - signfield: ${gen_signfield_update_data($personOid2, , $posBean4, ,$p2_sealId,$signfieldId2)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版单文档场景：执行流程签署区
    variables:
      - signfieldIds: [$signfieldId2]
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 简化版单文档场景：查询签署区-签署完成
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
    extract:
      - signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId0, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId2, $expectedKey,4)}",True]

- test:
    name: 简化版单文档场景：归档
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：查询流程-已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - client_id: pc
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 简化版单文档场景：获取签署文档
    variables:
      - urlType: 0
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 取消签署授权:个人账户1
    variables:
      - accountId: $personOid1
      - type: silent
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [status_code, 200]
