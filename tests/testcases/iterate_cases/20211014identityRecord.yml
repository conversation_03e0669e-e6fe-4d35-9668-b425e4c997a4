- config:
    name: "指定意愿分步/一步发起，该appId意愿方式为支付宝刷脸+短信+密码，报错请看是否被修改"
    base_url: ${ENV(base_url)}
    variables:
      - SaaS_app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - PaaS_app_id: ${ENV(appId_wukong)}
      - path_pdf: data/个人借贷合同.pdf
      - fileId: ${ENV(fileId)}
      - group: ''
      - xuanyuan_sixian: ${ENV(xuanyuan_sixian)}

- test:
    name: "创建SaaS的王瑶济账号"
    variables:
      - app_id: $SaaS_app_id
      - thirdPartyUserId: **************
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_wang: content.data.accountId

- test:
    name:  一步发起SaaS的流程
    variables:
      - app_id: $SaaS_app_id
      - json: {"docs":[{"fileId":"$fileId","fileName":"不重要的文件名.pdf"}],"flowInfo":{"flowConfigItemBean":{"buttonConfig":{"propButton":"1,2,15"}},"autoArchive":true,"autoInitiate":true,"businessScene":"不重要的任务名","contractRemind":65,"initiatorAccountId":"$accountId_wang","initiatorAuthorizedAccountId":"$accountId_wang","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["SIGN_PWD","CODE_SMS"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$accountId_wang","authorizedAccountId":"$accountId_wang","noticeType":"1","willTypes":["SIGN_PWD"]},"signfields":[{"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - len_gt: [content.data.flowId, 0]

- test:
    name:  获取SaaS的认证方式-不支持
    variables:
      - app_id: $SaaS_app_id
      - accountId: $accountId_wang
      - flowId: 1
    api: api/realname/identityRecord.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437193]
      - eq: ["content.message",当前appid不支持查询认证信息功能]

- test:
    name: "创建PaaS的王瑶济账号"
    variables:
      - app_id: $PaaS_app_id
      - thirdPartyUserId: **************
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_wang_PaaS: content.data.accountId

- test:
    name:  一步发起PaaS的流程
    variables:
      - app_id: $PaaS_app_id
      - json: {"docs":[{"fileId":"$fileId","fileName":"不重要的文件名.pdf"}],"flowInfo":{"flowConfigItemBean":{"buttonConfig":{"propButton":"1,2,15"}},"autoArchive":true,"autoInitiate":true,"businessScene":"不重要的任务名","contractRemind":65,"initiatorAccountId":"$accountId_wang_PaaS","initiatorAuthorizedAccountId":"$accountId_wang_PaaS","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["SIGN_PWD","CODE_SMS"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$accountId_wang_PaaS","authorizedAccountId":"$accountId_wang_PaaS","noticeType":"1","willTypes":["SIGN_PWD"]},"signfields":[{"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - len_gt: [content.data.flowId, 0]

- test:
    name:  获取PaaS的认证方式-实名
    variables:
      - app_id: $PaaS_app_id
      - accountId: $accountId_wang_PaaS
      - flowId: $sign_flowId
    api: api/realname/identityRecord.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.realNameRecord.0.authType",INDIVIDUAL_ARTIFICIAL]
      - eq: ["content.data.realNameRecord.0.status",1]
      - eq: ["content.data.realNameRecord.0.msg",成功]

- test:
    name:  获取PaaS的认证方式-app_id不匹配
    variables:
      - app_id: ${ENV(appId_sealPlatform_wukong)}
      - accountId: $accountId_wang_PaaS
      - flowId: $sign_flowId
    api: api/realname/identityRecord.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437194]
      - eq: ["content.message",当前flowid与appid不匹配]

- test:
    name:  获取PaaS的认证方式-签署人不匹配
    variables:
      - app_id: $PaaS_app_id
      - accountId: ${ENV(oid_liang_tsign)}
      - flowId: $sign_flowId
    api: api/realname/identityRecord.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437195]
      - eq: ["content.message",流程内查询不到签署人]

- test:
    name:  获取PaaS的认证方式-短信
    variables:
      - app_id: $PaaS_app_id
      - accountId: $accountId_wang_PaaS
      - flowId: ${ENV(flow_id_willing_by_code)}
    api: api/realname/identityRecord.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.willingRecord.0.authType",CODE_SMS]
      - eq: ["content.data.willingRecord.0.status",1]
      - eq: ["content.data.willingRecord.0.msg",验证成功]

- test:
    name:  获取PaaS的认证方式-多个意愿
    variables:
      - app_id: $PaaS_app_id
      - accountId: $accountId_wang_PaaS
      - flowId: ${ENV(flow_id_willing_by_multiple_codes)}
    api: api/realname/identityRecord.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - len_eq: ["content.data.willingRecord",4]
