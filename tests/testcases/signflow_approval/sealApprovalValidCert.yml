- config:
    name: 审批通过校验签署主体是否有有效证书
    ###def: one_businessSign()
    base_url: ${ENV(base_url)}
    variables:
      - flowId: c5276a59cc9547ca8650c0929ecce9b8
      - app_id: ${ENV(xuanyuan_realname)}
    "request":
      "base_url": ""
      "headers":
        "Content-Type": "application/json"

- test:
    name: 签署主体存在有效证书
    variables:
      - json: {
        "accountId": "",
        "hasHandWrite": true,
        "operatorId": "",
        "sealIds": []
      }
    api: api/sign-flow/sealApprovalValidCert.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.showTip", false]

