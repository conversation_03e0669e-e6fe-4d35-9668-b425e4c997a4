- config:
    name: 区块链无证书签
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)} #app_id已配置，具备发起区块链无证书签权限
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)} #app_id已配置，具备发起区块链无证书签权限
      - app_Id1: ${ENV(tengqing_PaaS_id_without_real_with_willingness)} #app_id已配置，具备发起区块链无证书签权限
      - account_id: ${ENV(account_id_without_gid_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_id: ${ENV(file_id_in_tengqing_PaaS_id_without_real_with_willingness)}
      - account_id_tengqing: ${ENV(account_id1_in_tengqing_PaaS_id_without_real_with_willingness)}
      - group: ${ENV(envCode)}
      - operator_id: ${ENV(account_id1_in_tengqing_PaaS_id_without_real_with_willingness)}
      - sealFileKey: ${ENV(sealFileKey)}

- test:
    name: 无gid 的用户可以发起流程
    variables:
      - json: {"signers":[{"signOrder":0,"signerAccount":{"noticeType":"2","signerAccountId":"$account_id"},"signfields":[{"assignedPosbean":true,"fileId":"$file_id","posBean":{"posPage":"1","posX":306,"posY":48},"signType":1,"width":200}],"thirdOrderNo":"1111"}],"docs":[{"bizFileId":"","encryption":0,"fileHash":"","fileId":"$file_id","fileName":"签署文件.pdf","filePassword":"","source":0}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署${get_randomNo()}","contractRemind":0,"flowConfigInfo":{},"flowConfigItemBean":{"buttonConfig":{"propButton":""}},"hashSign":false,"initiatorAccountId":"$account_id","remark":""}}
    api: api/signflow/signflows/ecloudflowsCreateFlow.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.flowId,32]
      - eq: [content.message, 成功]

- test:
    name: v3获取签署链接-历史接口无法获取
    variables:
      - accountId: $account_id
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code,1437192]
      - eq: [content.message, 当前流程为e签宝历史接口发起，请使用配套接口获取地址]

- test:
    name: v1获取签署链接
    variables:
      - accountId: $account_id
      - flowId: $sign_flowId
      - urlType: 2
      - organizeId: ''
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code,0]
      - contains: [content.data.url,signScenes=noWillingNoCertSign]
      - contains: [content.data.shortUrl, http]

- test:
    name: 获取config属性-processSignScenesType为2，showTaskDetail为false，showEdgeSignway为false，handDrawnWay为0
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $account_id
      - querySpaceAccountId: $account_id
      - batchSign: false
    api: api/iterate_cases/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.data.pageConfig.processSignScenesType, 2] #无意愿
      - eq: [content.data.pageConfig.showTaskDetail, false] #无任务信息
      - eq: [content.data.pageConfig.showEdgeSignway, false] #无骑缝章
      - eq: [content.data.allowedUploadAttachment, false] #无上传附件
      - eq: [content.data.pageConfig.handDrawnWay, '0'] #普通手绘

- test:
    name: 获取签名域信息
    variables:
      - flowId: $sign_flowId
      - signfieldIds: ''
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.message,成功]
      - eq: [content.code, 0]
    extract:
      - signfieldId: content.data.signfields.0.signfieldId

- test:
    name: 执行签署操作，不需要实名和意愿
    variables:
      - flowId: $sign_flowId
      - json: {"async":true,"accountId":"$account_id","updateSignfields":[{"sealId":"","sealFileKey":"$sealFileKey","signfieldId": $signfieldId,"actorIndentityType":0,"assignedItem":false,"assignedPosbean":true,"assignedSeal":false,"authorizedAccountId":"$account_id","autoExecute":false,"signDateBeanType":1,"extendFieldDatas":{},"fileId":"$file_id","order":1,"posBean":{"addSignTime":false,"posPage":"1","posX":500,"posY":500,"qrcodeSign":false,"width":500,"signDateBean":{"posPage":"1","fontName":"simsun","format":"yyyy年MM月dd日","fontSize":12,"posX":220,"posY":250},"sealType":1,"signType":1,"signerAccountId":"$account_id"}}],"approvalPolicy":0,"dingUser":{}}
    api: api/signflow/v3/addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.message,成功]
      - eq: [content.code, 0]

- test:
    name: 抄送人为空字符串，发起失败
    variables:
      - json: {"signers":[{"signOrder":0,"signerAccount":{"noticeType":"2","signerAccountId":"$account_id"},"signfields":[{"assignedPosbean":true,"fileId":"$file_id","posBean":{"posPage":"1","posX":306,"posY":48},"signType":0,"width":200}],"thirdOrderNo":"1111"}],"copiers": [{copierAccountId: ''}], "docs":[{"bizFileId":"","encryption":0,"fileHash":"","fileId":"$file_id","fileName":"签署文件.pdf","filePassword":"","source":0}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署${get_randomNo()}","contractRemind":0,"flowConfigInfo":{},"flowConfigItemBean":{"buttonConfig":{"propButton":""}},"hashSign":false,"initiatorAccountId":"$account_id","remark":""}}
    api: api/signflow/signflows/ecloudflowsCreateFlow.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1437206]
      - eq: [content.message, 参与人账号信息不能为空]

- test:
    name: 未传抄送人，发起成功
    variables:
      - json: {"signers":[{"signOrder":0,"signerAccount":{"noticeType":"2","signerAccountId":"$account_id"},"signfields":[{"assignedPosbean":true,"fileId":"$file_id","posBean":{"posPage":"1","posX":306,"posY":48},"signType":0,"width":200}],"thirdOrderNo":"1111"}],"copiers": [], "docs":[{"bizFileId":"","encryption":0,"fileHash":"","fileId":"$file_id","fileName":"签署文件.pdf","filePassword":"","source":0}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署${get_randomNo()}","contractRemind":0,"flowConfigInfo":{},"flowConfigItemBean":{"buttonConfig":{"propButton":""}},"hashSign":false,"initiatorAccountId":"$account_id","remark":""}}
    api: api/signflow/signflows/ecloudflowsCreateFlow.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.flowId,32]
      - eq: [content.message, 成功]

- test:
    name: 普通有gid的用户可以发起流程
    variables:
      - json: {"signers":[{"signOrder":0,"signerAccount":{"noticeType":"2","signerAccountId":"$account_id_tengqing"},"signfields":[{"assignedPosbean":true,"fileId":"$file_id","posBean":{"posPage":"1","posX":306,"posY":48},"signType":0,"width":200}],"thirdOrderNo":"1111"}],"docs":[{"bizFileId":"","encryption":0,"fileHash":"","fileId":"$file_id","fileName":"签署文件.pdf","filePassword":"","source":0}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署${get_randomNo()}","contractRemind":0,"flowConfigInfo":{},"flowConfigItemBean":{"buttonConfig":{"propButton":""}},"hashSign":false,"initiatorAccountId":"$account_id_tengqing","remark":""}}
    api: api/signflow/signflows/ecloudflowsCreateFlow.yml
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.flowId,32]
      - eq: [content.message, 成功]
    setup_hooks:
      - ${sleep_N_secs(40)}

- test:
    name: 查询上链信息，存在两条记录，分别为开启流程和签名
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/ecloudflowsDeposits.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code,0]
      - eq: [content.message,成功]
      - eq: [content.code, 0]
      - len_eq: [content.data.recordInfo, 2]

- test:
    name:  旧接口一步发起
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"不重要的文件名.pdf"}],"flowInfo":{"flowConfigItemBean":{"buttonConfig":{"propButton":"1,2,15"}},"autoArchive":true,"autoInitiate":true,"businessScene":"不重要的任务名","contractRemind":65,"initiatorAccountId":"$account_id","initiatorAuthorizedAccountId":"$account_id","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"2","redirectUrl":"","signPlatform":"","willTypes":["SIGN_PWD","CODE_SMS"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":"2","willTypes":["SIGN_PWD"]},"signfields":[{"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","handDrawnWay": "0,1", "posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - len_gt: [content.data.flowId, 0]

- test:
    name: 获取config属性-processSignScenesType为1，showTaskDetail为false，showEdgeSignway为true，handDrawnWay为0,1
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $account_id
      - querySpaceAccountId: $account_id
      - batchSign: false
    api: api/iterate_cases/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.data.pageConfig.processSignScenesType, 1] #意愿
      - eq: [content.data.pageConfig.showTaskDetail, false]
      - eq: [content.data.pageConfig.showEdgeSignway, true]
      #无此字段      - eq: [content.data.pageConfig.allowedUploadAttachment, false]
      - eq: [content.data.pageConfig.handDrawnWay, '0,1']

