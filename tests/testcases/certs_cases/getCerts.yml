- config:
    name: "查询收费证书接口校验"
    variables:
      - id_No: '331082199211223080'

      - Name: 梁贤红
      - app_id1: ${ENV(appid_cert)}
      - app_id2: ${ENV(appid_NO_cert)}
      - id_Type: CRED_PSN_CH_IDCARD
      - certid_expired: ${ENV(certid_expired)}
      - certid_revoke: ${ENV(certid_revoke)}
    request:
      base_url: "${ENV(footstone_api_url)}"

        #- test:
        #    name: "证书id必传，不传报错"
        #    api: api/user/cert-service/getCerts.yml
        #    variables:
        #      - app_id: $app_id1
        #      - certId: ''
        #    validate:
#        - eq: ["content.code",0]
#        - eq: ["content.message", "成功"]

- test:
    name: "证书id不存在，报错"
    api: api/user/cert-service/getCerts.yml
    variables:
      - app_id: $app_id1
      - certId: '1111111'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书不存在"]

- test:
    name: "制证1"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId1: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]

- test:
    name: "appid未传"
    api: api/user/cert-service/getCerts.yml
    variables:
      - app_id: ''
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "参数错误: 应用id不能为空"]

- test:
    name: "appid不一致"
    api: api/user/cert-service/getCerts.yml
    variables:
      - app_id: $app_id2
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书不在此appId下"]
- test:
    name: "吊销成功"
    api: api/user/cert-service/revokeCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "证书已吊销"
    api: api/user/cert-service/getCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.idType", "$id_Type"]
      - eq: ["content.data.name", "$Name"]
      - eq: ["content.data.idNumber", "$id_No"]
          - ne: ["content.data.txHash", None]
          - ne: ["content.data.contentHash", None]
      - eq: ["content.data.status",2]

- test:
    name: "证书已过期"
    api: api/user/cert-service/getCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certid_expired
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.name", "测试"]
      - eq: ["content.data.idNumber", "$id_No"]
          - ne: ["content.data.txHash", None]
          - ne: ["content.data.contentHash", None]
      - eq: ["content.data.status",3]



