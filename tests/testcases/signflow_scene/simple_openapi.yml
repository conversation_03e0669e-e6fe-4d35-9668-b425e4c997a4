- config:
    name: 轩辕api冒烟场景-简化api有改动部分
    base_url: ${ENV(footstone_api_url)}
    variables:
      - orgName: esigntest东乡族自治县梅里美商贸有限公司测试
      - legalName: esigntest沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: ***********
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - initiatorAccountId:
      - client_id: pc

- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: ${get_idNo()}
      - name: esigntest${get_randomNo()}
    api: api/user/accounts/create_accounts.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    variables:
      - idNo: ${get_idNo()}
      - name: esigntest${get_randomNo()}
    api: api/user/accounts/create_accounts.yml
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: 130202199104016781
      - name: esigntest-测试用户1
      - thirdPartyUserId: esigntest-13020219w5t9104016781
    api: api/user/account_third/create_account_appid.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230124200712193556
      - name: esigntest-测试用户2
      - thirdPartyUserId: esigntest-23012443234c22vbnn1219
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: "创建组织：个人账户2创建组织"
    variables:
      - accountId: $personOid2
      - orgType: CRED_ORG_CODE
      - orgCode: ********-X
      - legalIdNo:
      - legalName:
      - name: $orgName
    api: api/user/organizations/create_organizations.yml
    extract:
      - orgOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: "用户1创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章1
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "用户2创建个人模版印章"
    variables:
      - accountId: $personOid2
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章2
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建企业模板印章"
    variables:
      - orgId: $orgOid1
      - color: RED
      - height: 150
      - width: 150
      - alias: 企业模板印章
      - type: TEMPLATE_ROUND
      - central: STAR
      - hText: 上弦文
      - qText: 下弦文
    api: api/seals/seals/create_c_template_seal_appid.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: 获取文件信息
    variables:
      - fileId: ${get_file_id($app_id,$path_pdf)}
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]




- test:
    name: 轩辕api冒烟场景-签署授权:个人账户2-简化版api
    variables:
      - accountId: $personOid2
      - deadline:
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕api冒烟场景-查询授权状态
    variables:
      - accountId: $personOid2
    api: api/signflow/signAuth/signAuth_get.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data",True]

- test:
    name: 轩辕api冒烟场景-取消签署授权:个人账户2-简化版api
    variables:
      - accountId: $personOid2
      - type: silent
    api: api/signflow/simple_openapi/signAuth_delete_openapi.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 轩辕api冒烟场景-创建流程:只填必填项-简化版api
    variables:
      - autoArchive: False
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId:
    api: api/signflow/simple_openapi/signflowsCreateForOpenApi.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 轩辕api冒烟场景-添加流程文档：单文档-简化版api
    variables:
      - flowId: $flowId1
      - doc1: {'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕api冒烟场景-开启流程-简化版api
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕api冒烟场景-撤销流程-简化版api
    api: api/signflow/signflows/signflowsRevoke.yml
    variables:
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕api冒烟场景-创建流程-简化版api
    variables:
      - autoArchive: False
      - businessScene: 单人单文档
      - initiatorAccountId: $personOid1
    api: api/signflow/simple_openapi/signflowsCreateForOpenApi.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 轩辕api冒烟场景-添加流程文档：单文档-简化版api
    variables:
      - flowId: $flowId2
      - doc1: {'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕api冒烟场景-添加流程签署区：签署人账号-个人账户1、手动签署、签约主体-个人、不指定签署区
    variables:
      - flowId: $flowId2
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId1,$flowId2,$posBean,0,$personOid1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕api冒烟场景-添加对接平台自动盖章签署区
    variables:
      - flowId: $flowId2
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 200, posY: 600}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId1,$flowId2,$posBean,0,$personOid1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 轩辕api冒烟场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕api冒烟场景-查询流程
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId2
      - signfieldIds:
      - accountId: $personOid1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["${isContainKeyForApi($content_data,extend)}",False]
      - eq: ["${isContainKeyForApi($content_data,signerNames)}",False]
      - eq: ["${isContainKeyForApi($content_data,waitingSignerNames)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,billType)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,saleSchemaId)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,realNameCert)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,organRealnameTypes)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,personRealnameTypes)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,willingness)}",False]

- test:
    name: 轩辕api冒烟场景-查询个人印章列表：使用查询到的印章进行签署
    variables:
      - accountId: $personOid1
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 轩辕api冒烟场景-查询签署区-简化版api
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId2
      - signfieldIds: $signfieldId0
      - accountId: $personOid1
    extract:
      - content_data: content.data.signfields.0
      - content_data_posBean: content.data.signfields.0.posBean
    validate:
      - eq: [status_code, 200]
      - eq: ["${isContainKeyForApi($content_data,extendFieldDatas)}",False]
      - eq: ["${isContainKeyForApi($content_data,rushsignTime)}",False]
      - eq: ["${isContainKeyForApi($content_data,canExecute)}",False]
      - eq: ["${isContainKeyForApi($content_data,noticeUrl)}",False]
      - eq: ["${isContainKeyForApi($content_data,approvalFlowId)}",False]
      - eq: ["${isContainKeyForApi($content_data,sealFileKey)}",True]
      - eq: ["${isContainKeyForApi($content_data_posBean,addSignTime)}",False]
      - eq: ["${isContainKeyForApi($content_data_posBean,qrcodeSign)}",False]
      - eq: ["${isContainKeyForApi($content_data_posBean,width)}",True]

#- test:
#    name: 轩辕api冒烟场景-更新流程签署区：-简化版api
#    variables:
#      - posBean : {key: '', posPage: '1', posX: 400, posY: 200}
#      - signfield: ${gen_signfield_update_data_simple_api($personOid1, , $posBean,$sealId,$signfieldId0)}
#      - signfields: [$signfield]
#    api: api/signflow/signfields/signfieldsUpdate.yml
#    validate:

- test:
    name: 轩辕api冒烟场景-批量执行流程签署区
    variables:
      - flowId: $flowId2
      - signfieldIds: [$signfieldId0]
      - accountId: $personOid1
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 轩辕api冒烟场景-归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕api冒烟场景-查询流程：已归档
    setup_hooks:
      - ${sleep_N_secs(5)}
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

#- test:
#    name: 添加签署区拆分场景-单文档：对接平台自动签署-用户自动签署-用户手动签署
#    testcase: testcases/signflow_scene/simple_auto_hand_Sign_1doc.yml





- test:
    name: 简化版单文档场景：创建流程
    variables:
      - autoArchive: False
      - businessScene: 简化版单文档场景
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加签署区拆分场景-单文档：添加流程文档
    variables:
      - flowId: $flowId3
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：查询平台印章
    api: api/seals/seals/select_plat_seals.yml
    extract:
      - plat_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版单文档场景：对接平台自动签署，单页签署
    variables:
      - flowId: $flowId3
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    setup_hooks:
      - ${sleep_N_secs(5)}
    name: 简化版单文档场景：对个人账户1静默授权
    variables:
      - accountId: $personOid1
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版单文档场景：用户自动签署,单页签署
    variables:
      - flowId: $flowId3
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1, 'order':1,  'posBean':$posBean, 'sealId':$p1_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    setup_hooks:
      - ${sleep_N_secs(5)}
    name: 简化版单文档场景：添加用户手动签署
    variables:
      - flowId: $flowId3
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: { 'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid2
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版单文档场景：更新签署区
    variables:
      - flowId: $flowId3
      - posBean4: {'posPage':'1','posX':300, 'posY':600}
      - signfield: ${gen_signfield_update_data($personOid2, , $posBean4, ,$p2_sealId,$signfieldId2)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版单文档场景：执行流程签署区
    variables:
      - flowId: $flowId3
      - signfieldIds: [$signfieldId2]
      - accountId: $personOid2
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 简化版单文档场景：查询签署区-签署完成
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId3
      - conditionKey: "signfieldId"
      - accountId:
      - expectedKey: "status"
      - signfieldIds: ""
    extract:
      - signfields1: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($signfields1, $conditionKey,$signfieldId0, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields1, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields1, $conditionKey,$signfieldId2, $expectedKey,4)}",True]

- test:
    name: 简化版单文档场景：归档
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版单文档场景：查询流程-已归档
    setup_hooks:
      - ${sleep_N_secs(5)}
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 简化版单文档场景：获取签署文档
    variables:
      - flowId: $flowId3
      - accountId: $personOid1
      - organizeId:
      - urlType: 0
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 取消签署授权:个人账户1
    variables:
      - accountId: $personOid1
      - type: silent
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [status_code, 200]




#- test:
#    name: 添加签署区拆分场景-多文档：对接平台自动签署-用户自动签署-用户手动签署
#    testcase: testcases/signflow_scene/simple_auto_hand_Sign_Ndocs.yml



- test:
    name: 简化版多文档场景：创建流程
    variables:
      - autoArchive: False
      - businessScene: 添加签署区拆分场景
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 简化版多文档场景：添加流程文档
    variables:
      - flowId: $flowId4
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1,$doc2]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：查询平台印章
    api: api/seals/seals/select_plat_seals.yml
    extract:
      - plat_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版多文档场景：对接平台自动签署
    variables:
      - flowId: $flowId4
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield1: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':2}
      - signfield2: { 'fileId':$fileId2, 'order':1,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':2}
      - signfields: [$signfield1,$signfield2]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：对企业账号静默授权
    variables:
      - accountId: $orgOid1
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询个人印章列表-多文档：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $orgOid1
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版多文档场景：用户自动签署,骑缝签署
    variables:
      - flowId: $flowId4
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield3: {'fileId':$fileId1,'authorizedAccountId':$orgOid1, 'order':1,  'posBean':$posBean, 'sealId':$o_sealId,'signType':2}
      - signfield4: {'fileId':$fileId2,'authorizedAccountId':$orgOid1, 'order':1,  'posBean':$posBean, 'sealId':$o_sealId,'signType':2}
      - signfields: [$signfield3,$signfield4]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId3: content.data.signfieldBeans.0.signfieldId
      - signfieldId4: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: 简化版多文档场景：添加用户手动签署
    variables:
      - flowId: $flowId4
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield5: {'fileId':$fileId1, 'signerAccountId':$personOid2,'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':2}
      - signfield6: {'fileId':$fileId2, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':2}
      - signfields: [$signfield5,$signfield6]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId5: content.data.signfieldBeans.0.signfieldId
      - signfieldId6: content.data.signfieldBeans.1.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid2
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: 简化版多文档场景：更新签署区
    variables:
      - flowId: $flowId4
      - posBean4: {'posPage':'1','posX':300, 'posY':600}
      - signfield5: ${gen_signfield_update_data($personOid2, , $posBean4, ,$p2_sealId,$signfieldId5)}
      - signfield6: ${gen_signfield_update_data($personOid2, , $posBean4, ,$p2_sealId,$signfieldId6)}
      - signfields: [$signfield5,$signfield6]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: 简化版多文档场景：执行流程签署区
    variables:
      - flowId: $flowId4
      - signfieldIds: [$signfieldId5,$signfieldId6]
      - accountId: $personOid2
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]
      - eq: ["content.data.executeResults.1.optResult",0]

- test:
    name: 简化版多文档场景：查询签署区-签署完成
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
      - accountId:
      - signfieldIds: ""
      - flowId: $flowId4
    extract:
      - s_signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId2, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId3, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId4, $expectedKey,4)}",True]

- test:
    name: 简化版多文档场景：归档
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：查询流程-已归档
    setup_hooks:
      - ${sleep_N_secs(5)}
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 简化版多文档场景：获取签署文档
    variables:
      - flowId: $flowId4
      - accountId: $personOid1
      - organizeId:
      - urlType: 0
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 取消签署授权:企业账户
    variables:
      - accountId: $orgOid1
      - type: silent
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [status_code, 200]


#- test:
#    name: 添加签署区拆分场景-顺序签
#    testcase: testcases/signflow_scene/simple_auto_hand_Sign_order.yml


- test:
    name: 数据准备：对个人账户1静默授权
    variables:
      - accountId: $personOid1
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 数据准备：查询平台印章
    api: api/seals/seals/select_plat_seals.yml
    extract:
      - plat_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name:  数据准备：查询个人1印章
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name:  数据准备：查询个人2印章
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid2
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name:  数据准备：查询企业印章
    api: api/seals/seals/select_c_seals.yml
    variables:
      - orgId: $orgOid1
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版顺序签场景：创建流程
    variables:
      - autoArchive: False
      - businessScene: 添加签署区拆分场景
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId5: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 简化版顺序签场景：添加流程文档
    variables:
      - flowId: $flowId5
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：对接平台自动签署，顺序1
    variables:
      - flowId: $flowId5
      - posBean: {'posPage':'1','posX':100, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：添加用户2手动签署，顺序2
    variables:
      - flowId: $flowId5
      - posBean: {'posPage':'1','posX':200, 'posY':600}
      - signfield: { 'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':2,  'posBean':$posBean, 'sealType':'','signType':1}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：添加用户1自动签署，顺序3
    variables:
      - flowId: $flowId5
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1,'authorizedAccountId':$personOid1, 'order':3,  'posBean':$posBean, 'sealId':$p1_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId3: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：添加企业1手动签署，顺序4
    variables:
      - flowId: $flowId5
      - posBean: {'posPage':'1-2','posX':400, 'posY':600}
      - signfield: { 'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'1', 'authorizedAccountId':$orgOid1,'assignedPosbean':True, 'order':4,  'posBean':$posBean, 'sealType':'','signType':2}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId4: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(8)}
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：查询签署区-顺序1自动签完成，顺序2签署中
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
      - flowId: $flowId5
      - accountId:
      - signfieldIds: ""
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - s_signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId2, $expectedKey,1)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId3, $expectedKey,0)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId4, $expectedKey,0)}",True]

- test:
    name: 简化版顺序签场景：更新签署区
    variables:
      - flowId: $flowId5
      - posBean4: {'posPage':'1','posX':300, 'posY':600}
      - signfield: ${gen_signfield_update_data($personOid2,,$posBean4,,$p2_sealId,$signfieldId2)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 添加签署区拆分场景：执行流程签署区
    teardown_hooks:
      - ${hook_sleep_n_secs(12)}
    variables:
      - flowId: $flowId5
      - signfieldIds: [$signfieldId2]
      - accountId: $personOid2
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 简化版顺序签场景：查询签署区-顺序1自动签完成，顺序2手动签署完成，顺序3自动签署完成，顺序4签署中
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
      - flowId: $flowId5
      - accountId:
      - signfieldIds: ""
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - s_signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId2, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId3, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId4, $expectedKey,1)}",True]

- test:
    name: 简化版顺序签场景：更新签署区
    variables:
      - flowId: $flowId5
      - posBean4: {'posPage':'1','posX':300, 'posY':600}
      - signfield: ${gen_signfield_update_data($orgOid1, , $posBean4, ,$o_sealId,$signfieldId4)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 添加签署区拆分场景：执行流程签署区
    variables:
      - signfieldIds: [$signfieldId4]
      - flowId: $flowId5
      - accountId: $personOid2
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 添加签署区拆分场景：查询签署区-全部签署完成
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
      - flowId: $flowId5
      - accountId:
      - signfieldIds: ""
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - s_signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId2, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId3, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId4, $expectedKey,4)}",True]

- test:
    name: 简化版顺序签场景：对接平台自动签署，顺序5
    variables:
      - flowId: $flowId5
      - posBean: {'posPage':'1','posX':100, 'posY':500}
      - signfield: { 'fileId':$fileId1, 'order':5,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId5: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: 添加签署区拆分场景：归档
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加签署区拆分场景：查询流程-已归档
    setup_hooks:
      - ${sleep_N_secs(5)}
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 添加签署区拆分场景：获取签署文档
    variables:
      - flowId: $flowId5
      - accountId: $personOid1
      - organizeId:
      - urlType: 0
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]