#def: addUpdateExecute_sign($flowId,$addSignfields,$updateSignfields,$signfieldIds,$approvalPolicy,$dingUser,$accountId)
request:
  url: /v3/signflows/$flowId/signfields/add-Update-Execute
  method: PUT
  headers: ${get_headers($app_id,$operator_id)}
  json:
    async: true
    accountId: $accountId
    addSignfields: $addSignfields
    updateSignfields: $updateSignfields
    approvalPolicy: $approvalPolicy
    dingUser: $dingUser

# 异步添加或更新签署区并执行签署
