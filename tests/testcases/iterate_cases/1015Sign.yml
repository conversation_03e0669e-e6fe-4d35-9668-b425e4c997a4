- config:
    name: "1015迭代测试用例-ckl-关联gid部分"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - group:
      - m1: 13260881366
      - pOid1: ${getOid($m1)}
      - language: zh-US
      - app_Id1: ${ENV(wukong_forcewill)}
      - random_16: ${get_randomNo_16()}
      - random_32: ${get_randomNo_32()}
      - random_letter: ${random_letter()}
      - random_str: ${random_str()}
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}
      - client_id: pc

- test:
    name: "创建签署人账号"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_1
      - name: 陈凯丽
      - idNumber:
      - idType:
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


      #- test:
      #    name: 创建accountId_ckl对应标准签appid下oid的个人模板印章1
      #    variables:
      #      #      - app_id: $app_id
      #      - accountId: $standardoid
      #      - color: BLACK
      #      - height: 120
      #      - width: 120
      #      - alias: p_黑色印章
      #      - type: BORDERLESS
      #    api: api/seals/seals/create_p_template_seal.yml
      #    extract:
      #      - sealIdPer1: content.data.sealId
      #    validate:
#      - eq: ["content.code",0]


- test:
    name: 查询个人印章列表-如果已有印章返回印章id
    variables:
      - app_id: $app_id
      - accountId: $standardoid
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealIdPer1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]





- test:
    name: createFlowOneStep -售卖方案为轩辕  &  自动签署 & 非平台自动签 不支持自动签署
    variables:
      - app_id: $app_id
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "不支持自动签署"]
#创建流程1，再用/v1/signflows/{flowId}/documents接口添加文档
- test:
    name: 创建流程
    variables:
      - app_id: $app_id
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate_appid.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#流程添加个人签名域
- test:
    name: "个人-陈凯丽"
    variables:
      - app_id: $app_id
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]




#开启签署流程
- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id
      - flowId: $sign_flowId1
    api: api/iterate_cases/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

#查询流程签署人的gid信息
- test:
    name: "查询accountId_ckl的gid以及实名信息 - 没有gid，未实名"
    variables:
      - accountId: $accountId_ckl
      - app_id: $app_id
    api: api/user/accounts/get_accounts.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.base.ouid",$accountId_ckl]
      - str_eq: ["content.data.base.guid",None]
      - eq: ["content.data.baseRealname.status",false]

#意愿认证
- test:
    name: 意愿认证-发送验证码
    api: api/realname/realname/sendSms.yml
    variables:
      - accountId: $standardoid
      - app_id: $app_id
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $standardoid
      - bizId: $bizId
      - app_id: $app_id
    api: api/realname/realname/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#流程执行签署
- test:
    name: 签署
    variables:
      - accountId: $standardoid
      - flowId: $sign_flowId1
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $accountId_ckl, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $sealIdPer1, signfieldId: $signfieldId, signerOperatorAuthorizerId: $standardoid, signerOperatorId: $standardoid}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.signfieldSignResult.0.optResult", 0]


- test:
    name: 归档流程
    variables:
      - app_id: $app_id
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: 查询流程
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - app_id: $app_id
      - flowId: $sign_flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]


#查询流程签署人的gid信息
- test:
    name: "查询accountId_ckl的gid以及实名信息 - 有gid，未实名"
    variables:
      - accountId: $accountId_ckl
      - app_id: $app_id
    api: api/user/accounts/get_accounts.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.base.ouid",$accountId_ckl]
      - eq: ["content.data.base.guid",$standardgid]
      - eq: ["content.data.baseRealname.status",false]






































      #一步发起流程
      #- test:
      #    name: 一步发起
      #    variables:
      #        - doc : {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      #        - attachments: []
      #        - docs: [$doc]
      #        - copiers: []
      #        - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      #        - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
      #    api: api/signflow/signflows/createFlowOneStep.yml
      #    extract:
      #      - sign_flowId1: content.data.flowId
      #    validate:
      - eq: ["content.message",成功]




























