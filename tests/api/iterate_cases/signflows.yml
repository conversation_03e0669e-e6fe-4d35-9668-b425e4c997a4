# ******** 迭代，新增非必输项flowConfigItemBean 控制流程权限
request:
  url: /v1/signflows
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    extend: $extend
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity
    flowConfigItemBean: $flowConfigItemBean
validate:
  - eq: [status_code, 200]
