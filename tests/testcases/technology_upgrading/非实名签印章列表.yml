- config:
    name: "非实名印章列表--0528接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(wukong_no_realname)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "wukong_no_realname下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "使用caogu_accountId创建机构"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建签署流程"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - autoArchive: false
      - businessScene: 悟空非实名签校验add-update字段
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - orgId: $standard_sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_open_sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "个人-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "个人-添加成功个人签名域"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldIds0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]

- test:
    name: 添加或更新签署区并执行签署-报错 当前流程不是“签署中”的状态
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"addSignTime":true,"fieldType":1, "fileId":$fileId1,"signerAccountId":"eqianbao668800","actorIndentityType":"0","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200,"signDateBeanType":0},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","当前流程不是“签署中”的状态"]


- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - accountId: $yuzan_accountId
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 添加或更新签署区并执行签署-报错 不支持指定印章列表的任务类型
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield: {"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":True,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":$p1_sealId,"signType":1,"sealIds":[$org_sealId1],"signfieldId":$signfieldIds0}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","参数错误: 不支持指定印章列表的任务类型"]



- test:
    name: 添加或更新签署区并执行签署-报错 不支持的印章类型
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$cancellation_sealId]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","参数错误: 不支持的印章类型"]


- test:
    name: 添加或更新签署区并执行签署-报错  印章与签署主体不匹配
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  { "fileId": $fileId1,"signerAccountId": $yuzan_accountId,"actorIndentityType": "2","authorizedAccountId": $sixian_organize_id,"assignedPosbean": true,"order": 1,"posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": null,"sealId": "","signType": 1,"sealIds": [ $org_sealId1,$org_open_sealId1 ] }
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","印章和签署主体不匹配"]


- test:
    name: 添加或更新签署区并执行签署-报错 未定义的autoExecute
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"autoExecute": true,"autoExecuteType": 200,"fileId": $fileId1,"signerAccountId": $yuzan_accountId,"actorIndentityType": "2","authorizedAccountId": $sixian_organize_id,"assignedPosbean": true,"order": 1,"posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": null,"sealId": "","signType": 1,"sealIds": [ $org_sealId1,$org_open_sealId1 ] }
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","未定义的autoExecute"]

- test:
    name: 添加或更新签署区并执行签署-报错 签署主体不存在
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"autoExecute": true,"autoExecuteType": 0,"fileId": $fileId1,"signerAccountId": $yuzan_accountId,"actorIndentityType": "0","assignedPosbean": false,"order": 1,"posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": null,"sealId":$p1_sealId,"signType": 1,"sealIds": [ $org_sealId1,$org_open_sealId1 ] }
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","不支持指定印章列表的任务类型"]


- test:
    name: 添加或更新签署区并执行签署-报错 "签署人账号不存在
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"autoExecute": true,"autoExecuteType": 0,"fileId": $fileId1,"signerAccountId":'tiangu6688',"actorIndentityType": "0","assignedPosbean": false,"authorizedAccountId": $sixian_organize_id,"order": 1,"posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": null,"sealId":$p1_sealId,"signType": 1,"sealIds": [ $org_sealId1,$org_open_sealId1 ] }
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","签署人账号不存在"]

- test:
    name: 添加或更新签署区并执行签署-报错 签署人不是个人
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"signerAccountId":$sixian_organize_id,"actorIndentityType":"0","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","手动签署的签署人必须是个人"]




- test:
    name: 添加或更新签署区并执行签署-报错 签署主体不是企业
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$yuzan_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","签署主体不是企业"]

- test:
    name: 添加或更新签署区并执行签署-报错  signerAccountId不能为空
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","signerAccountId不能为空"]

- test:
    name: 添加或更新签署区并执行签署-报错  自动签署必须指定签署类型
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"autoExecute":true,"autoExecuteType":"0","signType":"0","fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","自动签署必须指定签署类型"]



- test:
    name: 添加或更新签署区并执行签署-报错  签署人账号不存在
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"signerAccountId":"eqianbao668800","actorIndentityType":"0","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","签署人账号不存在"]


- test:
    name: 添加或更新签署区并执行签署-报错 日期控件不支持再追加签署日期
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"addSignTime":true,"fieldType":1, "fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200,"signDateBeanType":0},"sealType":"","sealId":$org_open_sealId1,"signType":1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","日期控件不支持再追加签署日期"]


#-  test:
#    name: 添加或更新签署区并执行签署-自动签署-报错 未定义的actorIndentityType
#    variables:
#        -   app_id: ${ENV(wukong_no_realname)}
#        -   #        -   flowId: $sign_flowId
#        -   accountId: $yuzan_accountId
#        -   addSignfield:  {"autoExecute": true,"autoExecuteType": '0',"assignedPosbean":false,"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"10","authorizedAccountId":$sixian_organize_id,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
#        -   addSignfields: [$addSignfield]
#        -   updateSignfields: []
#        -   dingUser: {}
#        -   approvalPolicy: 0
#        -   signfieldIds: [$signfieldIds0]
#
#    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
#    validate:
#        -   eq: ["status_code", 200]
#        -   contains: ["content.message","未定义的actorIndentityType"]
#
#
#
##钉签
- test:
    name: 添加或更新签署区并执行签署-报错 钉签dingCorpId不能为空
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"signerAccountId":$sixian_organize_id,"actorIndentityType":"0","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {
        "dingCorpId": "",
        "dingIsvAppId": "",
        "dingUserId": "",
        "processInstanceId": ""
      }
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_dingqian.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","dingCorpId不能为空"]



- test:
    name: 添加或更新签署区并执行签署-报错 dingUserId不能为空
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"signerAccountId":$sixian_organize_id,"actorIndentityType":"0","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {
        "dingCorpId": "12345",
        "dingIsvAppId": "",
        "dingUserId": "",
        "processInstanceId": ""
      }
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_dingqian.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","dingUserId不能为空"]


- test:
    name: 添加或更新签署区并执行签署-报错 DingIsvAppId不能为空
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"signerAccountId":$sixian_organize_id,"actorIndentityType":"0","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {
        "dingCorpId": "12345",
        "dingIsvAppId": "",
        "dingUserId": "12345",
        "processInstanceId": ""
      }
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]

    api: api/signflow/aggregate_sign/addUpdateExecute_sign_dingqian.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","DingIsvAppId不能为空"]

- test:
    name: 添加或更新签署区并执行签署-报错 dingUser不能为空
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
      - accountId: $yuzan_accountId
      - addSignfield:  {"fileId":$fileId1,"signerAccountId":$sixian_organize_id,"actorIndentityType":"0","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":$org_open_sealId1,"signType":1,"sealIds":[$org_open_sealId1]}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - approvalPolicy: 0
      - signfieldIds: [$signfieldIds0]
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_dingqian.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","dingUser不能为空"]