- config:
    name: 手绘方式校验
    base_url: ${ENV(base_url)}
    variables:
      - tPUserId: autotest1212${get_randomNo()}
      - app_id: ${ENV(BZQ-App-Id)}
      - path_pdf: data/个人借贷合同.pdf
      - batchSign: false

- test:
    name: 使用第三方userId 创建账号
    variables:
      - json:
          {
            "name": 梁贤红,
            "thirdPartyUserId": $tPUserId,
            "idNumber":"331082199211223080",
            "idType":"CRED_PSN_CH_IDCARD",
            "mobile":"***********"
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - oId1: content.data.accountId
    output:
      - $oId1

- test:
    name: 创建流程
    variables:
      - autoArchive: true
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $oId1
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $oId1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 添加流程文档：单文档
    variables:
      - flowId: $flowId1
      - doc1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message", "成功"]

- test:
    name: 添加用户手动签署-未定义的handDrawnWay
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId, 'signerAccountId':$oId1, 'actorIndentityType':'0', 'authorizedAccountId':$oId1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1,'handDrawnWay':3}
      - signfields: [$signfield_file1]
    api: api/signflow/signfields/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", "参数错误: 未定义的handDrawnWay"]

- test:
    name: 添加用户手动签署-指定AI手绘
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId, 'signerAccountId':$oId1, 'actorIndentityType':'0', 'authorizedAccountId':$oId1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1,'handDrawnWay':1}
      - signfields: [$signfield_file1]
    api: api/signflow/signfields/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code,0]
      - contains: ["content.message", "成功"]

- test:
    name: 查询流程配置
    variables:
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code,0]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 流程未开启时查询签署人流程配置为appId配置
    variables:
      - flowId: $flowId1
      - queryAccountId: $oId1
      - querySpaceAccountId: $oId1
      - menuId: ''
      - scenario: ''
      - operator_id: $oId1
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code,0]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 流程开启
    variables:
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: ["content.message", 成功]

- test:
    name: 流程开启后查询签署人流程配置为流程配置
    variables:
      - flowId: $flowId1
      - queryAccountId: $oId1
      - querySpaceAccountId: $oId1
      - menuId: ''
      - scenario: ''
      - operator_id: $oId1
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code,0]
      - eq: ["content.data.pageConfig.handDrawnWay","1"]

- test:
    name: 创建流程
    variables:
      - autoArchive: true
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $oId1
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.message", 成功]

- test:
    name: 添加流程文档：单文档
    variables:
      - flowId: $flowId2
      - doc1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: 添加用户手动签署-指定普通手绘
    variables:
      - flowId: $flowId2
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId, 'signerAccountId':$oId1, 'actorIndentityType':'0', 'authorizedAccountId':$oId1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1,'handDrawnWay':0}
      - signfields: [$signfield_file1]
    api: api/signflow/signfields/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: 查询流程配置
    variables:
      - flowId: $flowId2
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 查询签署人流程配置
    variables:
      - flowId: $flowId2
      - queryAccountId: $oId1
      - querySpaceAccountId: $oId1
      - menuId: ''
      - scenario: ''
      - operator_id: $oId1
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 创建流程
    variables:
      - autoArchive: true
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $oId1
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.message", 成功]

- test:
    name: 添加流程文档：单文档
    variables:
      - flowId: $flowId3
      - doc1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: 添加用户手动签署-不指定手绘
    variables:
      - flowId: $flowId3
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId, 'signerAccountId':$oId1, 'actorIndentityType':'0', 'authorizedAccountId':$oId1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1,'handDrawnWay':null}
      - signfields: [$signfield_file1]
    api: api/signflow/signfields/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: 查询流程配置
    variables:
      - flowId: $flowId3
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 查询签署人流程配置
    variables:
      - flowId: $flowId3
      - queryAccountId: $oId1
      - querySpaceAccountId: $oId1
      - menuId: ''
      - scenario: ''
      - operator_id: $oId1
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 一步发起-未定义的handDrawnWay
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1, 'handDrawnWay':3,  width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", "参数错误: 未定义的handDrawnWay"]

- test:
    name: 一步发起--指定AI手绘
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1, 'handDrawnWay':1,  width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId11: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: [content.code, 0]

- test:
    name: 查询流程配置
    variables:
      - flowId: $flowId11
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 查询签署人流程配置
    variables:
      - flowId: $flowId11
      - queryAccountId: $oId1
      - querySpaceAccountId: $oId1
      - menuId: ''
      - scenario: ''
      - operator_id: $oId1
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","1"]

- test:
    name: 一步发起--指定普通手绘
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1, 'handDrawnWay':0,  width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId22: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code", 0]


- test:
    name: 查询流程配置
    variables:
      - flowId: $flowId22
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 查询签署人流程配置
    variables:
      - flowId: $flowId22
      - queryAccountId: $oId1
      - querySpaceAccountId: $oId1
      - menuId: ''
      - scenario: ''
      - operator_id: $oId1
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]
- test:
    name: 一步发起--不指定手绘
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1, 'handDrawnWay':'',  width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId33: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code", 0]

- test:
    name: 查询流程配置
    variables:
      - flowId: $flowId33
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 查询签署人流程配置
    variables:
      - flowId: $flowId33
      - queryAccountId: $oId1
      - querySpaceAccountId: $oId1
      - menuId: ''
      - scenario: ''
      - operator_id: $oId1
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: "一步到位创建流程-对内-指定ai手绘"
    variables:
      - app_id: $app_id
      - group: ''
      - json: {"accountId":$oId1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"指定手绘方式签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"http://172.20.62.157/simpleTools/notice/","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":0,"signerAccount":"","signerAccountId":$oId1,"signerAccountName":"","signerAuthorizerId":$oId1,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{ "handDrawnWay":1,"fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: 查询流程配置
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","0"]

- test:
    name: 查询签署人流程配置
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $oId1
      - querySpaceAccountId: $oId1
      - menuId: ''
      - scenario: ''
      - operator_id: $oId1
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]
      - eq: ["content.data.pageConfig.handDrawnWay","1"]

