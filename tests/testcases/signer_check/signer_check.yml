- config:
    name: 签署人查询信息、校验权限、信息反馈、补全信息
    base_url: ${ENV(footstone_api_url)}

- test:
    name: 个人空间创建流程
    variables:
      - autoArchive: true
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $personOid1
      - extend: {}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $personOid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: 添加流程文档：单文档
    variables:
      - doc1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加流程签署区
    variables:
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId,$flowId,$posBean,0,$personOid1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 校验签署权限-入参校验-签署人不存在
    variables:
      - flowId: $flowId
      - accountId: 1111111
      - operatorId: $personOid1
      - redirectUrl:
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 已被注销]


- test:
    name: 校验签署权限-入参校验-成功
    variables:
      - flowId: $flowId
      - accountId: $personOid1
      - operatorId: $personOid1
      - redirectUrl:
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]

- test:
    name: 信息反馈-入参校验-cardnoType不能为空
    variables:
      - flowId: $flowId
      - accountId: $personOid3
      #      - name: ab
      #      - cardNo: ************
      #      - operatorId:
      - json: {"name": "ab" ,"cardNo":************,"operatorId": $personOid3}
    api: api/signflow/flowSigners/feedbackSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", cardNoType不能为空]

- test:
    name: 信息反馈-入参校验-name不能为空
    variables:
      - flowId: $flowId
      - accountId: $personOid3
      - json: { "cardNo":************,"operatorId": $personOid3,"cardNoType":"CRED_PSN_CH_IDCARD"}
    api: api/signflow/flowSigners/feedbackSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", name不能为空]


- test:
    name: 信息反馈-入参校验-cardNo不能为空
    variables:
      - flowId: $flowId
      - accountId: $personOid3
      - json: { "operatorId": $personOid3,"cardNoType":"CRED_PSN_CH_IDCARD","name": 111}
    api: api/signflow/flowSigners/feedbackSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", cardNo不能为空]

- test:
    name: 信息反馈-入参校验-不支持的cardnotype
    variables:
      - flowId: $flowId
      - accountId: $personOid3
      - json: { "operatorId": $personOid3,"cardNoType":"ab","name": 111,"cardNo":$personOid3}
    api: api/signflow/flowSigners/feedbackSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 不支持的cardNoType]

- test:
    name: 信息反馈-入参校验-未做任何修改
    variables:
      - flowId: $flowId
      - accountId: $personOid1
      - json: { "operatorId": $personOid1,"cardNoType":"CRED_PSN_CH_IDCARD","name": "王瑶济","cardNo":320721199305232634}
    api: api/signflow/flowSigners/feedbackSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 未做任何修改，请再次确认信息]

- test:
    name: 信息反馈-入参校验-成功
    variables:
      - flowId: $flowId
      - accountId: $personOid1
      - json: { "operatorId": $personOid1,"cardNoType":"CRED_PSN_CH_IDCARD","name": "梁贤","cardNo":331082199211223080}
    api: api/signflow/flowSigners/feedbackSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]

- test:
    name: 信息反馈-入参校验-成功
    variables:
      - flowId: $flowId
      - accountId: $personOid1
      - json: { "operatorId": $personOid1,"cardNoType":"CRED_PSN_CH_IDCARD","name": "梁贤","cardNo":331082199211223080}
    api: api/signflow/flowSigners/feedbackSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 填写信息与上次反馈信息一致且已发送，不可重复提交]

- test:
    name: 补全信息-入参校验-cardnoType不能为空
    variables:
      - flowId: $flowId
      - accountId: $personOid3
      - json: {"name":"ab","cardNo":************,"operatorId":$personOid3}
    api: api/signflow/completedSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", cardNoType不能为空]

#- test:
#    name: 补全信息-入参校验-无效的证件号
#    variables:
#      - flowId: $flowId
#      - accountId: $personOid3
#      - json: {"cardNo":************,"operatorId":$personOid3,"cardNoType":"CRED_PSN_CH_IDCARD","name":"梁贤红"}
#      - message: ${ENV(message1)}
#    api: api/signflow/completedSignerInfo.yml
#    validate:
#      - eq: [status_code, 200]
#      - contains: ["content.message", $message]

- test:
    name: 补全信息-入参校验-name不能为空
    variables:
      - flowId: $flowId
      - accountId: $personOid3
      - json: {"cardNo":331082199211223080,"operatorId":$personOid3,"cardNoType":"CRED_PSN_CH_IDCARD"}
    api: api/signflow/completedSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 请填写姓名]

- test:
    name: 补全信息-入参校验-不支持的cardnotype
    variables:
      - flowId: $flowId
      - accountId: $personOid3
      - json: {"cardNo":$personOid3,"operatorId":$personOid3,"cardNoType":"ab","name":"111"}
    api: api/signflow/completedSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 不支持的cardNoType]

- test:
    name: 补全信息-入参校验-成功
    variables:
      - flowId: $flowId
      - accountId: $personOid1
      - json: {"cardNo":320721199305232634,"operatorId":$personOid1,"cardNoType":"CRED_PSN_CH_IDCARD","name":"王瑶济"}
    api: api/signflow/completedSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]


- test:
    name: 补全信息-入参校验-请填写证件号
    variables:
      - flowId: $flowId
      - accountId: $personOid2
      - json: {"cardNo":"","operatorId":$personOid2,"cardNoType":"CRED_PSN_CH_IDCARD","name":"梁贤"}
    api: api/signflow/completedSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 请填写证件号]



- test:
    name: 获取签署人信息-入参校验-签署人不存在
    variables:
      - flowId: $flowId
      - accountId: 111

    api: api/signflow/flowSigners/getSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 当前用户不是流程参与人]

- test:
    name: 获取签署人信息-入参校验-当前用户不是流程参与人
    variables:
      - flowId: $flowId
      - accountId: $personOid2

    api: api/signflow/flowSigners/getSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 当前用户不是流程参与人]

- test:
    name: 获取签署人信息-入参校验-成功
    variables:
      - flowId: $flowId
      - accountId: $personOid1

    api: api/signflow/flowSigners/getSignerInfo.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]

- test:
    name: 校验签署权限-入参校验-成功
    variables:
      - flowId: cc418f96cf1f4642bd060f5bc4bdb3cc
      - accountId: 74b3fe5e89f249da8f16b3d405fb1bb0
      - operatorId: 74b3fe5e89f249da8f16b3d405fb1bb0
      - redirectUrl:
      - json: {"operatorId": "74b3fe5e89f249da8f16b3d405fb1bb0","assignMobile":"***********", "redirectUrl": null}
    api: api/v1/signflows/flowId/accounts/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]