#def: signflowsCreateForOrganize($autoArchive, $businessScene, $configInfo, $contractValidity, $extend, $initiatorAccountId, $payerAccountId, $signValidity,$initiatorAuthorizedAccountId)
request:
  url: /v1/signflows
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    extend: $extend
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity
    initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId
validate:
  - eq: [status_code, 200]

#创建流程-不传configInfo
