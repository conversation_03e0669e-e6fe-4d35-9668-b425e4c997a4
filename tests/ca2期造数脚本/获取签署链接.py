#获取签署链接
import json

import requests

headers = {
 'X-Tsign-Open-App-Id': '**********',
 'X-Tsign-Open-Auth-Mode': 'simple',
 'Content-Type': 'application/json;charset=UTF-8'
}
url = 'http://in-test-openapi.tsign.cn/v3/sign-flow/{}/sign-url'.format("589dee295fb64dca95569573f29e64cb")
data = {
    "operator": {
        "psnId": "",
        "psnAccount": "***********"
    },
    "organization": {
        "orgId": "",
        "orgName": ""
    },
    "clientType": "",
    "appScheme": "",
    "needLogin": False,
    "redirectConfig": {
        "redirectDelayTime": 3,
        "redirectUrl": "https://www.alibaba.com"
    }
}
response = requests.post(url, headers=headers, data=json.dumps(data))


res = response.json()

print(res)
