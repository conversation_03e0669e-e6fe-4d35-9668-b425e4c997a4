- config:
    name: "flow-manager调用频次优化"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(gan_appid_download)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}
      - imagfileId: "5d134d1a5033458293a122bded604950"
      - client_id: pc

- test:
    name: "创建签署人账号 - gan"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "使用caogu_accountId创建机构"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId



- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: "8fbe5926547e40149978fb8c5a448394"
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - accountId: $yuzan_accountId
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 一步发起指定证书id用户手动签-成功
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']
      - eq: [ "${redis_val($sign_flow_one)}",null ]  #创建后redis里没有数据

- test:
    name: 查询流程详情
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",1]
      - eq: ["status_code", 200]
      - eq: [ "${redis_val($sign_flow_one)}",1 ]   #查询后redis里有数据  且为1


- test:
    name: 查询签署区
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200] #content.data.signfields.0.signerAccountId
      - eq: [ "${redis_val($sign_flow_one)}",1 ]   #查询后redis里有数据  且为1
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId

- test:
    name: 查询流程配置
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.data.signBillingWay",2]

- test:
    name: "根据flowId查询打包流程文档，返回下载地址"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/signflow/signflows/documentsPack.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.code",0]


- test:
    name: "查询详情，主要查询processId"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - operatorid: $yuzan_accountId
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
    extract:
      - processId: content.data.processId
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: "使用processId- documents/pack接口执行下载"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/signflow/signflows/processIdDocumentsPack.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.code",0]

- test:
    name: 添加或更新签署区并执行签署
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - accountId: "3986d7544d9848f186ee9c5dcd342de1"
      - sealId: $p1_sealId
      - addSignfield:  {signfieldId: $signfieldId0,"fileId":$fileId1,"signerAccountId":"3986d7544d9848f186ee9c5dcd342de1","actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "第一个用户手动签，第一个用户手动签"},"sealType":"","sealId":$org_sealId1,"signType":1,signerOperatorId: "3986d7544d9848f186ee9c5dcd342de1",signerOperatorAuthorizerId: "8fbe5926547e40149978fb8c5a448394"}
      - addSignfields: []
      - updateSignfields: [$addSignfield]
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: []
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.message","成功"]
      - eq: [ "${redis_val($sign_flow_one)}",1 ]   #查询后redis里有数据  且为1


- test:
    name: 归档流程
    variables:
      - app_id: $app_id
      - flowId: $sign_flow_one
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: [ "${redis_val($sign_flow_one)}",null ]   #归档后会删除缓存
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 查询流程详情
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]
      - eq: ["status_code", 200]
      - eq: [ "${redis_val($sign_flow_one)}",2 ]   #查询后redis里有数据  且为1

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]