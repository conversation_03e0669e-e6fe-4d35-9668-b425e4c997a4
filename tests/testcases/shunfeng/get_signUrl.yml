- config:
    name: "顺丰获取签署详情页地址"
    base_url: ${ENV(shunfeng_url)}
    variables:
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}
      - fileId1: ${get_file_id($app_id,$path_pdf)}

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}



- test:
    name: "创建抄送人王瑶济账号"
    variables:
      - thirdPartyUserId: we1915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_yuzan: content.data.accountId

- test:
    name: "创建发起人梁贤红账号"
    variables:
      - thirdPartyUserId: caogu2111411321ert213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_caogu: content.data.accountId

- test:
    name: "创建非流程参与人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: irteeru456789543527
      - name: ""
      - idNumber: ""
      - idType: ""
      - email:
      - mobile: ""
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_fc: content.data.accountId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201211
      - creator: $accountId_caogu
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId




- test:
    name: 创建签署流程
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: [{copierAccountId: $accountId_yuzan,copierIdentityAccountId: "", copierIdentityAccountType: 0}]
      - flowInfo: { autoArchive: true, autoInitiate: true,initiatorAccountId: $accountId_caogu, initiatorAuthorizedAccountId: "", businessScene: " 顺丰-获取中间页签署地址接口支持获取详情页地址",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $dongying_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 },{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 200,   posY: 200  }, signType: 2,   width: 150 }],  thirdOrderNo: 667 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - sign_flowId1: content.data.flowId



- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType不传，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType不传，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType不传，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType不传，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType不传，发起人获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_caogu
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType不传，发起人获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_caogu
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]



- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType不传，抄送人获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_yuzan
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType不传，抄送人获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_yuzan
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType不传，传入的accountId不是流程参与人"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_fc
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437114]
      - eq: ["content.message", "当前用户不是流程参与人, 无权查看流程"]
      - eq: ["content.data",null]


- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType不传，传入的accountId不是流程参与人"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_fc
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437114]
      - eq: ["content.message", "当前用户不是流程参与人, 无权查看流程"]
      - eq: ["content.data",null]




- test:
    name: "V1获取PC详情页链接 - urlType传2，clientType传PC，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取PC详情页链接 - urlType传2，clientType传PC，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取PC详情页链接 - urlType传2，clientType传PC，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取PC详情页链接 - urlType传2，clientType传PC，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]




- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType传H5，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType传H5，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType传H5，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType传H5，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]



- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType传web，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType传web，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType传web，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取H5详情页链接 - urlType传2，clientType传web，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]



- test:
    name: "V1获取预览页链接 - urlType传1，clientType传web，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取预览页链接 - urlType传1，clientType传web，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取预览页链接 - urlType传1，clientType传web，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取预览页链接 - urlType传1，clientType传web，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取预览页链接 - urlType传1，clientType传H5，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取预览页链接 - urlType传1，clientType传H5，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取预览页链接 - urlType传1，clientType传H5，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取预览页链接 - urlType传1，clientType传H5，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取预览页链接 - urlType传1，clientType传PC，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取预览页链接 - urlType传1，clientType传PC，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取预览页链接 - urlType传1，clientType传PC，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取预览页链接 - urlType传1，clientType传PC，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取预览页链接 - urlType传1，clientType不传，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取预览页链接 - urlType传1，clientType不传，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取预览页链接 - urlType传1，clientType不传，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取预览页链接 - urlType传1，clientType不传，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 1
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]




- test:
    name: "V1获取签署页链接 - urlType传0，clientType传web，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取签署页链接 - urlType传0，clientType传web，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取签署链接 - urlType传0，clientType传web，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取签署页链接 - urlType传0，clientType传web，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: web
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取签署页链接 - urlType传0，clientType传H5，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取签署页链接 - urlType传0，clientType传H5，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取签署页链接 - urlType传0，clientType传H5，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取签署页链接 - urlType传0，clientType传H5，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: H5
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取签署页链接 - urlType传0，clientType传PC，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取签署页链接 - urlType传0，clientType传PC，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取签署页链接 - urlType传0，clientType传PC，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取签署页链接 - urlType传0，clientType传PC，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取签署页链接 - urlType传0，clientType不传，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: false
      - organizeId: $accountId_ckl
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取签署页链接 - urlType传0，clientType不传，获取单主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: false
      - organizeId: $dongying_organize_id
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]


- test:
    name: "V1获取签署页链接 - urlType传0，clientType不传，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

- test:
    name: "V2获取签署页链接 - urlType传0，clientType不传，获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 0
      - multiSubject: true
      - organizeId: ""
      - compressContext: true
      - clientType: ""
    api: api/flow-manager/V2_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.executeUrl.longUrl","context"]
      - contains: ["content.data.executeUrl.shortUrl","sign"]
      - len_gt: ["content.data.executeUrl.shortUrl",20]

