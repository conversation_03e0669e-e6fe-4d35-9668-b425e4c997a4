- config:
    name: "accounts账号相关的各种操作"
    ###def: accounts_action($idNo, $idType, $name, $email,$mobile)
    request:
      "base_url":

- test:
    name: "创建一个新账号"
    api: api/user/accounts/create_accounts.yml   #无email/mobile
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "查询账号信息"
    api: api/user/accounts/get_accounts.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.prop.credentials.idno",$idNo]
      - eq: ["content.data.prop.properties.realnameMobile",'']

- test:
    name: "再次创建这个账号-验证幂等性"
    api: api/user/accounts/create_accounts.yml   #验证幂等性
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.accountId",$accountId]

- test:
    name: "更新账号信息"
    api: api/user/accounts/update_accounts.yml   #加email/mobile
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "查询账号信息-新增字段了"
    api: api/user/accounts/get_accounts.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.prop.credentials.idno",$idNo]
      - eq: ["content.data.prop.contacts.mobile",$mobile]
      - eq: ["content.data.prop.contacts.email",$email]

- test:
    name: "注销账号"
    api: api/user/accounts/delete_accounts.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "注销后查询不到账号"
    api: api/user/accounts/get_accounts.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","账号不存在"]
      - eq: ["content.data",null]

- test:
    name: "再次注销账号提示账号不存在"
    api: api/user/accounts/delete_accounts.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","账号不存在"]
      - eq: ["content.data",null]