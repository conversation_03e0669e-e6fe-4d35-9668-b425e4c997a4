- config:
    name: "e签盾流程创建-添加签名域"
    base_url: ${ENV(footstone_mobile_shield_url)}
    variables:
      - app_id1: ${ENV(mobile_shield_xuanyuan_appid)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 - 泗县深泉纯净水有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId


- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: "文件上传 - 个人借贷合同ckl.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同ckl.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]




#创建e签盾流程 - createAllAtOnce
- test:
    name: "创建e签盾签署流程"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":2},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.processFlows.0.flowId

#添加签名域1 - /v1/signflows/{{flowId}}/signfields/handSign
- test:
    name: 添加个人代企业签署区
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'2', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加个人签署区
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':100, 'posY':100}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'0', 'authorizedAccountId':$accountId_ckl,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]

- test:
    name: 添加法人签署区
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':200, 'posY':200}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'3', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]

- test:
    name: 添加经办人签署区
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':300}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'4', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]



#添加签名域2 - /v1/signflows/{flowId}/signfields/autoSign
- test:
    name: 添加个人代企业-用户自动签署区
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':400, 'posY':200}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'2', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
#考虑实名签署与用户自动签署冲突      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]

#添加签名域3 - /v1/signflows/{flowId}/signfields/platformSign
- test:
    name: 添加平台自动盖章签署区
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':260, 'posY':200}
      - signfield_file1: { 'fileId':$fileId1,  'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]



#创建现有流程 - createAllAtOnce
- test:
    name: "创建现有签署流程"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"现有签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId2: content.data.processFlows.0.flowId



#添加签名域1 - /v1/signflows/{{flowId}}/signfields/handSign
- test:
    name: 添加个人+个人代企业+法人+经办人签署区
    variables:
      - flowId: $flowId2
      - posBean1: {'posPage':'1','posX':100, 'posY':100}
      - signfield1_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'0', 'authorizedAccountId':$accountId_ckl,'assignedPosbean':True, 'order':1,  'posBean':$posBean1, 'sealType':'','sealId':"",'signType':1}
      - posBean2: {'posPage':'1','posX':200, 'posY':200}
      - signfield2_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'2', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean1, 'sealType':'','sealId':"",'signType':1}
      - posBean3: {'posPage':'1','posX':300, 'posY':300}
      - signfield3_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'3', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean1, 'sealType':'','sealId':"",'signType':1}
      - posBean4: {'posPage':'1','posX':400, 'posY':400}
      - signfield4_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'4', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean1, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield1_file1,$signfield2_file1,$signfield3_file1,$signfield4_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



#添加签名域2 - /v1/signflows/{flowId}/signfields/autoSign
- test:
    name: 添加个人代企业-用户自动签署区
    variables:
      - flowId: $flowId2
      - posBean: {'posPage':'1','posX':400, 'posY':200}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'2', 'authorizedAccountId':$sixian_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
#    validate:
#      - eq: ["content.code",0]
#      - eq: ["content.message", "成功"]


#添加签名域3 - /v1/signflows/{flowId}/signfields/platformSign
- test:
    name: 添加平台自动盖章签署区
    variables:
      - flowId: $flowId2
      - posBean: {'posPage':'1','posX':260, 'posY':200}
      - signfield_file1: { 'fileId':$fileId1,  'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]