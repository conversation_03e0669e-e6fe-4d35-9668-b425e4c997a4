config:
  name: 审批流-0930

testcases:
  二级审批:
    testcase: testcases/approval_flow/审批流-二级审批.yml
  审批流-拒绝:
    testcase: testcases/approval_flow/审批流-拒绝.yml
  #   审批流-混合审批:  去掉混合审批 全量都走新版审批 无老版审批
  #     testcase: testcases/approval_flow/审批流-混合审批.yml
  签署计费-重复审批:
    testcase: testcases/approval_flow/审批流-重复审批.yml

  签署计费-create-by-file-正常:
    testcase: testcases/签署计费改造/签署计费-create-by-file.yml

  签署计费-create-by-file异常:
    testcase: testcases/签署计费改造/签署计费-create-by-file异常.yml

  签署计费改造:
    testcase: testcases/签署计费改造/签署计费改造.yml

  大文件签署1:
    testcase: /testcases/大文件签署/大小文件-手动同步异步签署.yml

  大文件签署2:
    testcase: /testcases/大文件签署/大小文件-自动同步异步签署.yml

  appid隔离-create-by-file:
    testcase: testcases/签署计费改造/appid隔离-create-by-file.yml

  appid隔离-createFlowOneStep:
    testcase: testcases/签署计费改造/appid隔离-createFlowOneStep.yml

  appid隔离-正常-createFlowOneStep:
    testcase: testcases/签署计费改造/appid隔离-正常-createFlowOneStep.yml