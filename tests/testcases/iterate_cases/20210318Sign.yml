- config:
    name: 签署人校验权限
    variables:
      - mobile1: 18767104153
      - mobile2: 13260881366
      - email: <EMAIL>
      - personOid1: ${getOid($mobile1)}
      - personOid2: ${getOid($mobile2)}
      - personOid3: ${getOid($email)}
      - idType_person: CRED_PSN_CH_IDCARD
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_2: autotest2${get_randomNo()}
      - thirdPartyUserId_person_3: autotest3${get_randomNo()}
      - thirdPartyUserId_person_4: autotest4${get_randomNo()}
      - name_person_1: 陈凯丽
      - idNumber_person_1: 362323199201061068
      - app_id: ${ENV(BZQ-App-Id)}
      - path_pdf: data/个人借贷合同.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - fileId: ${get_file_id($app_id,$path_pdf)}
      - sleep: ${hook_sleep_n_secs(5)}
    base_url: ${ENV(footstone_api_url)}



- test:
    name: 一步发起-个人
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "日期控件",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $personOid1,authorizedAccountId: $personOid1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150,"fieldType": 1, "dateControlBean": {"fontSize": 12, "formatType": "YYYY年MM月dd日","fontType": "SimSun","shift": -1} }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 校验日期控件信息
    variables:
      - queryAccountId: $personOid1
      - operatorid: $personOid1
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.signDocs.0.signfields.0.fieldType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.dateControlBean.fontType", SimSun]
      - eq: ["content.data.signDocs.0.signfields.0.dateControlBean.fontSize", 12]
      - eq: ["content.data.signDocs.0.signfields.0.dateControlBean.shift", -1]


- test:
    name: 一步发起-日期控件日期格式错误
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "日期控件",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $personOid1,authorizedAccountId: $personOid1}, signfields: [ {  autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: { posPage: 1, posX: 0, posY: 0},sealType: "",signDateBeanType: "0",signDateBean: { fontSize: 20, posX: 0,posY: 0},signType: 1,width: 150,fieldType: 1,dateControlBean: {formatType: "YYYY-MM-dd日",fontType: "KaiTi"}}],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: formatType不在指定的范围内']

- test:
    name: 一步发起-日期控件不支持的字体
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "日期控件",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $personOid1,authorizedAccountId: $personOid1}, signfields: [ {  autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: { posPage: 1, posX: 0, posY: 0},sealType: "",signDateBeanType: "0",signDateBean: { fontSize: 20, posX: 0,posY: 0},signType: 1,width: 150,fieldType: 1,dateControlBean: {formatType: "YYYY-MM-dd",fontType: "AriaL"}}],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: fontType不在指定的范围内']


- test:
    name: 一步发起-日期控件加签署日期信息
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $personOid1,authorizedAccountId: $personOid1}, signfields: [ {  autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: { posPage: 1, posX: 0, posY: 0},sealType: "",signDateBeanType: "1",signDateBean: { fontSize: 20, posX: 0,posY: 0},signType: 1,width: 150,fieldType: 1,dateControlBean: {formatType: "YYYY年MM月dd日",fontType: "KaiTi"}}],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002 ]
      - contains: ["content.message",'参数错误: 日期控件不支持再追加签署日期']
