- config:
    name: 通过文件创建签署流程
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_id: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - account_id_2_tsign: ${ENV(account_id2_in_tsign)}
      - account_id_3_tsign: ${ENV(account_id3_in_tsign)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}
      - flowid_1: 3de7193503ac4e60ba1b9b8dc0426e08
      - phone_1: ${ENV(phone_1)}
      - phone_2: ${ENV(phone_2)}
      - phone_3: ${ENV(phone_3)}

- test:
    name: "无待签文档可以发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "attachments": [ { "fileId": "$file_id" } ],"copiers": [ { "copierPsnInfo": { "psnId": "$account_id_2_tsign" } } ],"signFlowConfig": { "autoFinish": true,"autoStart": false,"chargeConfig": { "chargeMode": 0 },"noticeConfig": { "noticeTypes": "1,2" },"notifyUrl": "$notifyUrl","signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signFlowInitiator": { "orgInitiator": { "orgId": $account_id_sx_tsign, "transactor": { "psnId": $account_id_1_tsign } } } }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 0 ]
      - len_eq: [ content.data.signFlowId, 32 ]

- test:
    name: "无签署区可以发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "attachments": [ { "fileId": "$file_id" } ],"copiers": [ { "copierPsnInfo": { "psnId": "$account_id_2_tsign" } } ],"docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": false,"chargeConfig": { "chargeMode": 0 },"noticeConfig": { "noticeTypes": "1,2" },"notifyUrl": "$notifyUrl","signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signFlowInitiator": { "orgInitiator": { "orgId": $account_id_sx_tsign, "transactor": { "psnId": $account_id_1_tsign } } } }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 0 ]
      - len_eq: [ content.data.signFlowId, 32 ]
    extract:
      - flowId: content.data.signFlowId

- test:
    name: "追加抄送人"
    api: api/v3/sign-flow/signFlowId/copiers.yml
    variables:
      signFlowId: $flowId
      json: { "copiers": [ { "copierPsnInfo": { "psnId": "$account_id_1_tsign" } } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 0 ]
      - contains: [ content.message, 成功 ]

- test:
    name: "追加抄送人-抄送人psnId、psnAccount不能同时传值或同时为空"
    api: api/v3/sign-flow/signFlowId/copiers.yml
    variables:
      signFlowId: $flowId
      json: { "copiers": [ { "copierPsnInfo": { "psnId": "$account_id_1_tsign","psnAccount": "***********" } } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, 抄送人psnId、psnAccount不能同时传值或同时为空 ]

- test:
    name: "删除抄送人"
    api: api/v3/sign-flow/signFlowId/copiers/delete.yml
    variables:
      signFlowId: $flowId
      json: { "copiers": [ { "copierPsnInfo": { "psnId": "$account_id_1_tsign" } } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 0 ]
      - contains: [ content.message, 成功 ]

- test:
    name: "删除抄送人-当前流程无此抄送方信息"
    api: api/v3/sign-flow/signFlowId/copiers/delete.yml
    variables:
      signFlowId: $flowId
      json: { "copiers": [ { "copierPsnInfo": { "psnId": "$account_id_1_tsign" } } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 1435011 ]
      - contains: [ content.message, 当前流程无此抄送方信息 ]

- test:
    name: "删除抄送人-抄送人psnId、psnAccount不能同时传值或同时为空"
    api: api/v3/sign-flow/signFlowId/copiers/delete.yml
    variables:
      signFlowId: $flowId
      json: { "copiers": [ { "copierPsnInfo": { "psnId": "$account_id_1_tsign","psnAccount": "***********" } } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 1435002 ]
      - contains: [ content.message, 抄送人psnId、psnAccount不能同时传值或同时为空 ]

- test:
    name: "自动开启的流程，无签署区不可发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "attachments": [ { "fileId": "$file_id" } ],"copiers": [ { "copierPsnInfo": { "psnId": "$account_id_2_tsign" } } ],"docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"chargeConfig": { "chargeMode": 0 },"noticeConfig": { "noticeTypes": "1,2" },"notifyUrl": "$notifyUrl","signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signFlowInitiator": { "orgInitiator": { "orgId": $account_id_sx_tsign, "transactor": { "psnId": $account_id_1_tsign } } } }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 1435011 ]
      - contains: [ content.message, 流程自动开启时，待签署文件docs和签署人信息signers不能为空 ]

- test:
    name: "自动开启的流程，无文档不可发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "attachments": [ { "fileId": "$file_id" } ],"copiers": [ { "copierPsnInfo": { "psnId": "$account_id_2_tsign" } } ],"signFlowConfig": { "autoFinish": true,"autoStart": true,"chargeConfig": { "chargeMode": 0 },"noticeConfig": { "noticeTypes": "1,2" },"notifyUrl": "$notifyUrl","signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true },"signFlowExpireTime": "${get_timestamp(10)}","signFlowTitle": "签署${get_timestamp()}" },"signFlowInitiator": { "orgInitiator": { "orgId": $account_id_sx_tsign, "transactor": { "psnId": $account_id_1_tsign } } } }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 1435011 ]
      - contains: [ content.message, 流程自动开启时，待签署文件docs和签署人信息signers不能为空 ]


- test:
    name: "异常：非当前app发起获取预览文件下载链接"
    api: api/sign-flow/preview-file-download-url.yml
    variables:
      - app_ids: ${ENV(appid_cert)}
      - signFlowId: $flowid_1
      - docFileId: $file_id
    validate:
      - eq: [ status_code, 200 ]
      #      - eq: [content.code, 1435011]
      - contains: [ content.message, "当前项目下，签署流程不存在" ]

- test:
    name: "异常：非api3.0发起获取预览文件下载链接"
    api: api/sign-flow/preview-file-download-url.yml
    variables:
      - app_ids: $app_id
      - signFlowId: $flowid_1
      - docFileId: $file_id
    validate:
      - eq: [ status_code, 200 ]
      #      - eq: [content.code, 1435011]
      - contains: [ content.message, "该接口仅能查询SaaS Api3.0发起的流程信息，请确认后重试" ]

- test:
    name: "异常：非签署中状态-获取预览文件下载链接"
    api: api/sign-flow/preview-file-download-url.yml
    variables:
      - app_ids: $app_id
      - signFlowId: $flowId
      - docFileId: $file_id
    validate:
      - eq: [ status_code, 200 ]
      #      - eq: [content.code, 1435011]
      - contains: [ content.message, "非签署中状态的签署流程不支持下载" ]

- test:
    name: "一步发起，所有信息为登录凭证"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"copiers":[{"copierPsnInfo":{"psnAccount":"$phone_1"}},{"copierOrgInfo":{"orgName":"$name_dy"},"copierPsnInfo":{"psnAccount":"$phone_1"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"${notifyUrl}","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"所有信息为登录凭证"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnAccount":"$phone_3"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgName":"$name_dy","transactorInfo":{"psnAccount":"$phone_3"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "一步发起，抄送方同时存在机构和个人"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"copiers":[{"copierPsnInfo":{"psnAccount":"$phone_1"}},{"copierOrgInfo":{"orgId":"$name_dy"},"copierPsnInfo":{"psnAccount":"$phone_1"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"${notifyUrl}","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"所有信息为登录凭证"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnAccount":"$phone_3"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgName":"$name_dy","transactorInfo":{"psnAccount":"$phone_3"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, "个人抄送方、机构抄送方必须都传id或者账号"]


- test:
    name: "appId允许无指定企业签署经办人"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":false,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_dy_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code,  0]
      - contains: [content.message, "成功"]
    extract:
      - sign_flowId: content.data.signFlowId

- test:
    name: "添加企业签名域，不传经办人-可以追加"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $sign_flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_dy_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]