- config:
    name: 指定印章平台绑定证书的印章id自动签署-43M文件
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: "7876586760"
      - app_id: "7876586760"
      - path_pdf: data/43M.pdf
      - personOid1: "47e46a51057d45dabf13b62a00f920b3"
      - sealId1: "c060f155-f4ca-4488-9af6-327dad59d33a"
      - sealId2: "70682216-9ce4-4153-b8f5-30c45d3f3af4"
      - sealId3: "19064db1-48f9-411a-b14e-30c27530ba22" #未绑定证书
      - fileId1: 650c9819bd184e5d9a60d4bcb0c61d6d
      - sleep: ${hook_sleep_n_secs(3)}



#平台自动签
- test:
    name: createFlowOneStep - 平台自动签 - 指定绑定证书的印章Id
    variables:
      - doc: {encryption: 1,filePassword: "123456",fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc]
      - attachments: []
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 指定绑定证书的印章Id",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{"platformSign":true,"signOrder":1,"signfields":[{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":110},"sealId":$sealId1,"signType":1,"width":150},{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":210},"sealId":$sealId2,"signType":1,"width":150}],"thirdOrderNo":777}]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.flowId


#平台自动签
- test:
    name: createFlowOneStep - 平台自动签 - 指定绑定证书的印章Id
    variables:
      - doc: {encryption: 1,filePassword: "123456",fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc]
      - attachments: []
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 指定绑定证书的印章Id",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{"platformSign":true,"signOrder":1,"signfields":[{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":110},"sealId":$sealId3,"signType":1,"width":150},{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":210},"sealId":$sealId2,"signType":1,"width":150}],"thirdOrderNo":777}]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId2: content.data.flowId

#查询印章绑定的证书id
- test:
    name: 查询证书id1
    variables:
      - sealId: $sealId1
    api: api/seal-manager/getCertIdBySealId-api.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]
    extract:
      - certId1: content.certId

  #查询印章绑定的证书id
- test:
    name: 查询证书id2
    variables:
      - sealId: $sealId2
    api: api/seal-manager/getCertIdBySealId-api.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]
    extract:
      - certId2: content.certId

  #查询印章绑定的证书id
- test:
    name: 查询证书id3
    variables:
      - sealId: $sealId3
    api: api/seal-manager/getCertIdBySealId-api.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]
#      - eq:
#        - ${CertIsNull(content)}
#        - true

- test:
    name: 校验签署证书信息
    variables:
      #      - base_url: ${ENV(flow_manager_url)}
      - flowId: $flowId1
    api: api/flow-manager/queryFlowResourceModel-api.yml
    validate:
      - eq: [status_code, 200]
#      - eq:
        #        - ${getCertId(content)}
        #        - 200
      - contains: ["content.message", 成功]

- test:
    name: 校验签署证书信息
    variables:
      #      - base_url: ${ENV(flow_manager_url)}
      - flowId: $flowId2
    api: api/flow-manager/queryFlowResourceModel-api.yml
    validate:
      - eq: [status_code, 200]
#      - eq:
        #        - ${getCertId(content)}
        #        - 200
      - contains: ["content.message", 成功]