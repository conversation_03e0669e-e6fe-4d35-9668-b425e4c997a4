- config:
    name: "未添加签署区签署-模拟问题-来自于企业签署"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(wukong_no_realname)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}

- test:
    name: "wukong_no_realname下创建签署人王瑶济账号"
    variables:
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",********]
      - eq: ["content.message", "账号已存在"]

- test:
    name: "创建梁贤红账号"
    variables:
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",********]
      - eq: ["content.message", "账号已存在"]

- test:
    name: "创建机构 泗县深泉纯净水有限公司"
    variables:
      - thirdPartyUserId: si201913331141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",********]
      - eq: ["content.message", "机构已存在"]

- test:
    name: "创建机构  泗县梁贤红乐福超市"
    variables:
      - thirdPartyUserId: sin111113393311141132
      - creator: $caogu_accountId
      - idNumber: 92341324MA2NX3TR55
      - idType: CRED_ORG_USCC
      - name: 泗县梁贤红乐福超市
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixianchaoshi_organize_id: content.data.orgId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",********]
      - eq: ["content.message", "机构已存在"]

- test:
    name: "创建企业印章"
    variables:
      - orgId: $sixian_organize_id
      - alias: 印章1
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - orgId: $sixian_organize_id
      - alias: 印章2
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - orgId: $sixian_organize_id
      - alias: 印章11
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - orgId: $sixianchaoshi_organize_id
      - alias: 印章3
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - orgId: $sixianchaoshi_organize_id
      - alias: 印章4
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - orgId: $sixianchaoshi_organize_id
      - alias: 印章344
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业1的印章"
    teardown_hooks:
      - ${teardown_seals_chaoshi($response)}
    variables:
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - list1: list11
      - list2: list22
      - list3: list33
      - list4: list44
      - list5: list55
      - sealsId1: sealsId1
      - sealsId1_list: sealsId1_list
      - org_sealIds: org_sealIds
      - sealsId4: sealsId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业2的印章"
    teardown_hooks:
      - ${teardown_seals_chaoshi($response)}
    variables:
      - orgId: $sixianchaoshi_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - list11: list11
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询个人印章"
    variables:
      - accountId: $caogu_accountId
    api: api/seals/seals/select_p_seals.yml
    extract:
      - seal_id_psn: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建签署流程-非自动归档"
    variables:
      - autoArchive: false
      - businessScene: 悟空非实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加指定位置签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"0","authorizedAccountId":"$caogu_accountId","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":1,"signFinishNotice":true}]
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加自由签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"0","authorizedAccountId":"$caogu_accountId","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":0,"signFinishNotice":true}]
    extract:
      - signfield_id_0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "删除自由签名域"
    api: api/signflow/signfields/signfieldsDelete.yml
    variables:
      - flowId: $sign_flowId1
      - signfieldId: $signfield_id_0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行自由签名域-当前用户不支持自由签署， 不能追加新的签署区
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - addSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"0","authorizedAccountId":$caogu_accountId,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$seal_id_psn,"signType":1}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"0","authorizedAccountId":$caogu_accountId,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$seal_id_psn,"signType":1,"signfieldId":"$signfieldId2"}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfield_id_0,$signfieldId2]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","当前用户不支持自由签署， 不能追加新的签署区"]

- test:
    name: "删除指定位置签名域"
    api: api/signflow/signfields/signfieldsDelete.yml
    variables:
      - flowId: $sign_flowId1
      - signfieldId: $signfieldId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 无签署区执行签署动作-当前用户无待签署任务
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - addSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"0","authorizedAccountId":$caogu_accountId,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealsId1,"signType":1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","当前用户无待签署任务"]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 归档流程-没有签署，无需归档
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","没有签署，无需归档"]

- test:
    name: "添加自由签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"2","authorizedAccountId":"$sixian_organize_id","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":0,"signFinishNotice":true}]
    extract:
      - signfield_id_0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行自由签名域-成功
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealsId1,"signType":1,"signfieldId":"$signfield_id_0"}
      - addSignfields: []
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: "重新添加指定位置签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"2","authorizedAccountId":"$sixian_organize_id","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":1,"signFinishNotice":true}]
    extract:
      - signfield_id_0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行指定位置签名域-成功
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealsId1,"signType":1,"signfieldId":"$signfield_id_0"}
      - addSignfields: []
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 归档流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]


- test:
    name: "创建签署流程-非自动归档"
    variables:
      - autoArchive: false
      - businessScene: 悟空非实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加自由签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"2","authorizedAccountId":"$sixian_organize_id","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":0,"signFinishNotice":true}]
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加指定位置签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"2","authorizedAccountId":"$sixian_organize_id","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":1,"signFinishNotice":true}]
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "删除指定位置签名域"
    api: api/signflow/signfields/signfieldsDelete.yml
    variables:
      - flowId: $sign_flowId1
      - signfieldId: $signfieldId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行指定位置签名域-签署任务已被撤销
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealsId1,"signType":1,"signfieldId":"$signfieldId2","signerOperatorId":$caogu_accountId}
      - addSignfields: []
      - updateSignfields: [$updateSignfield]
      - signfieldIds: [$signfieldId1]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","签署任务已被撤销"]

- test:
    name: 执行自由签名域-成功
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealsId1,"signType":1,"signfieldId":"$signfieldId1"}
      - addSignfields: []
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 归档流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name: "创建签署流程-自动归档"
    variables:
      - autoArchive: true
      - businessScene: 自动归档流程
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加指定位置签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"2","authorizedAccountId":"$sixian_organize_id","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":1,"signFinishNotice":true}]
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "删除指定位置签名域"
    api: api/signflow/signfields/signfieldsDelete.yml
    variables:
      - flowId: $sign_flowId1
      - signfieldId: $signfieldId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 无签署区执行签署动作-当前用户无待签署任务
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - addSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealsId1,"signType":1,"signerOperatorId":$caogu_accountId}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","当前用户无待签署任务"]
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}

- test:
    name: 归档流程-没有签署，无需归档
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","没有签署，无需归档"]

- test:
    name: "创建签署流程-自动归档"
    variables:
      - autoArchive: true
      - businessScene: 悟空非实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加自由签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"2","authorizedAccountId":"$sixian_organize_id","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":0,"signFinishNotice":true}]
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加指定位置签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${sign_flowId1}
      - signfields: [{"fileId":"${fileId1}","signerAccountId":"$caogu_accountId","actorIndentityType":"2","authorizedAccountId":"$sixian_organize_id","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":1,"signFinishNotice":true}]
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "删除指定位置签名域"
    api: api/signflow/signfields/signfieldsDelete.yml
    variables:
      - flowId: $sign_flowId1
      - signfieldId: $signfieldId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行指定位置签名域-签署任务已被撤销
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealsId1,"signType":1,"signfieldId":"$signfieldId2","signerOperatorId":$caogu_accountId}
      - addSignfields: []
      - updateSignfields: [$updateSignfield]
      - signfieldIds: [$signfieldId1]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","签署任务已被撤销"]

- test:
    name: 执行自由签名域-成功
    variables:
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$caogu_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealsId1,"signType":1,"signfieldId":"$signfieldId1"}
      - addSignfields: []
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 归档流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","流程已归档"]