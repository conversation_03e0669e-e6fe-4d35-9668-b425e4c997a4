config:
  name: 融合层签署场景测试
  variables:
    - autoArchive: False
    - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
    - contractValidity: ''
    - signValidity: ''
    - extend: {}
    - sealId_p1_type: 1
    - sealId_p2_type: 1
    - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
    - personOid3: 310913bf68594f8bb7c457215e0b4831
    - path_pdf: data/个人借贷合同.pdf
    - dingUser: {}

testcases:
  融合层签署场景测试:
    testcase: testcases/aggregate/aggregate_sign_action.yml

  融合层异步签署场景测试:
    testcase: testcases/aggregate/aggregate_sign_action_async.yml

