- config:
    name: "发起签署页面开放"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - group: ${ENV(envCode)}
      - fileId1: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - fileId2: ${ENV(file_id_2_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - psn_id_1: ${ENV(account_id1_in_tsign)}
      - psn_id_2: ${ENV(account_id2_in_tsign)}
      - psn_id_3: ${ENV(account_id3_in_tsign)}
      - deleted_psn: ${ENV(deleted_psn_tsign)}
      - deleted_org: ${ENV(deleted_org_tsign)}
      - org_id_1: ${ENV(orgid_dy)}
      - org_id_2: ${ENV(orgid_sx)}
      - phone_1: ${ENV(phone_1)}
      - phone_2: ${ENV(phone_2)}
      - phone_3: ${ENV(phone_3)}

- test:
    name: 获取发起流程页面链接-仅有流程名称
    variables:
      - json: {"signFlowConfig":{"signFlowTitle":"签署流程"}}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-流程名称为空
    variables:
      - json: {"signFlowConfig":{"signFlowTitle":""}}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",signFlowTitle不能为空]
      - eq: ["content.code",1435002,]

- test:
    name: 获取发起流程页面链接-空对象
    variables:
      - json: {}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",signFlowConfig不能为空]
      - eq: ["content.code",1435002,]

- test:
    name: 获取发起流程页面链接-空对象
    variables:
      - json: {}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",signFlowConfig不能为空]
      - eq: ["content.code",1435002,]

- test:
    name: 获取发起流程页面链接-仅传了orgId未传transactor
    variables:
      - json: {"signFlowConfig":{"autoFinish":true,"autoStart":true,"signFlowTitle":"签署"},"signFlowInitiator":{"orgInitiator":{"orgId":"${org_id_1}"}}}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",机构发起方orgId与transactor不能为空]
      - eq: ["content.code",1435002,]

- test:
    name: 获取发起流程页面链接-传了相同主体相同顺序
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'同一种签署方类型signerType下，不允许存在相同签署顺序的相同签署方']
      - eq: ["content.code",1435002,]

- test:
    name: 获取发起流程页面链接-法人签
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-法人+法人签
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签+企业签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-平台自动签-传了orgId
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-存在签署人但没有签署区
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signerType":2},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":2},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-平台自动签-orgId为空字符串
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":""},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-平台自动签-orgId为null
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":null},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-平台自动签-不传orgId
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-平台自动签-orgSignerInfo为null
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":null,"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-平台自动签-不传orgSignerInfo
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]


- test:
    name: 获取发起流程页面链接-企业手动签-只传了orgId
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",非自动签签署机构签署方经办人transactorInfo不能为空]
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-企业手动签-角色和签署人信息都为空字符串
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":""},"signerRole":"","signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署角色signerRole与具体用户信息必须传入其中一种]
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-企业手动签-orgId和signerRole都为null
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":null},"signerRole":null,"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署角色signerRole与具体用户信息必须传入其中一种]
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-企业手动签-不传orgId和signerRole
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署角色signerRole与具体用户信息必须传入其中一种]
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-企业手动签-orgSignerInfo和signerRole为null
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":null,"signerRole":null,"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署角色signerRole与具体用户信息必须传入其中一种]
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-企业手动签-不传orgSignerInfo和signerRole
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署角色signerRole与具体用户信息必须传入其中一种]
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-uneditableFields传了非枚举值
    variables:
      - json: {"initiatePageConfig":{"uneditableFields":["1111111111","docs"]},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_2}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'uneditableFields无对应枚举值']
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-uneditableFields传了docs但未传签署文件
    variables:
      - json: {"initiatePageConfig":{"uneditableFields":["docs"]},"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_2}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'未传入签署文档，不允许传签名域']
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-uneditableFields传了signers但是未传签署人信息
    variables:
      - json: {"initiatePageConfig":{"uneditableFields":["signers"]},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"}}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'签署流程相关信息中未传入signers参数，不可将其设置为不可编辑内容']
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-uneditableFields传了signers和docs，但未传签署文件
    variables:
      - json: {"initiatePageConfig":{"uneditableFields":["signers","docs"]},"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_2}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'未传入签署文档，不允许传签名域']
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-信息完整，同时传了签署人信息和签署角色
    variables:
      - json: {"initiatePageConfig":{"uneditableFields":["docs","signFlowTitle","signFlowExpireTime","attachments","signers","copiers",'signFields']},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_1}"},"signerRole":"甲方","signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_2}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-信息完整，uneditableFields所有枚举值都传
    variables:
      - json: {"initiatePageConfig":{"uneditableFields":["docs","signFlowTitle","signFlowExpireTime","attachments","signers","copiers",'signFields']},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_2}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-指定签署方存在无签名域，uneditableFields传signFields
    variables:
      - json: {"initiatePageConfig":{"uneditableFields":['signFields']},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${psn_id_2}"},"signConfig":{"signOrder":1},"signFields":[],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'签署流程相关信息中有签署方未传入signFields参数，不可将其设置为不可编辑内容']
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-信息完整，无签署方，传入signFields
    variables:
      - json: {"initiatePageConfig":{"uneditableFields":['signFields']},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"相同主体相同顺序"},"signers":[]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'签署流程相关信息中未传入signers参数，不可将signFields设置为不可编辑内容']
      - eq: ["content.code",1435002]

- test:
    name: 获取发起流程页面链接-所有信息为登录凭证
    variables:
      - json: {"attachments":[{"fileId":"${fileId1}","fileName":""}],"copiers":[{"copierOrgInfo":{"orgName":"${ENV(name_dy)}"},"copierPsnInfo":{"psnAccount":"${phone_1}"}}],"docs":[{"fileId":"${fileId1}"}],"initiatePageConfig":{"customBizNum":"","initiateButtons":"1,2","uneditableFields":["signFlowTitle","docs","signFlowExpireTime","attachments","copiers","signers"]},"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"barrierCode":"","chargeMode":0,"orderType":""},"noticeConfig":{"examineNotice":false,"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowTitle":"所有信息为登录凭证"},"signers":[{"noticeConfig":{"noticeTypes":""},"orgSignerInfo":{"orgName":"${ENV(xuanyuan_sixian_name)}","transactorInfo":{"psnAccount":"${phone_2}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1},{"psnSignerInfo":{"psnAccount":"${phone_3}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-所有信息为id
    variables:
      - json: {"attachments":[{"fileId":"${fileId1}","fileName":""}],"copiers":[{"copierOrgInfo":{"orgId":"${ENV(orgid_dy)}"},"copierPsnInfo":{"psnId":"${psn_id_1}"}}],"docs":[{"fileId":"${fileId1}"}],"initiatePageConfig":{"customBizNum":"","initiateButtons":"1,2","uneditableFields":["signFlowTitle","docs","signFlowExpireTime","attachments","copiers","signers"]},"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"barrierCode":"","chargeMode":0,"orderType":""},"noticeConfig":{"examineNotice":false,"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowTitle":"所有信息为id"},"signFlowInitiator":{"orgInitiator":{"orgId":"${ENV(orgid_dy)}","transactor":{"psnId":"${psn_id_2}"}}},"signers":[{"noticeConfig":{"noticeTypes":""},"orgSignerInfo":{"orgId":"${ENV(orgid_dy)}","transactorInfo":{"psnId":"${psn_id_2}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1},{"psnSignerInfo":{"psnId":"${psn_id_3}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: orgSealType传1，2，3发起失败
    variables:
      - json: {"initiatePageConfig":{"orgSealType":"1,2,3,4"},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",orgSealType无对应枚举值]
      - eq: ["content.code",1435002]

- test:
    name: orgSealBizTypes枚举值排除发票章，发起失败
    variables:
      - json: {"initiatePageConfig":{"orgSealType":"1"},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"INVOICE","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",orgSealBizTypes无对应枚举值]
      - eq: ["content.code",1435002]


- test:
    name: orgSealType传1发起成功
    variables:
      - json: {"initiatePageConfig":{"orgSealType":"1"},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: orgSealType传2发起成功
    variables:
      - json: {"initiatePageConfig":{"orgSealType":"2"},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: orgSealType传1,2,1发起成功
    variables:
      - json: {"initiatePageConfig":{"orgSealType":"1,2,1"},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: orgSealType传 ,1发起成功
    variables:
      - json: {"initiatePageConfig":{"orgSealType":" ,1"},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: 指定企业签签署人账号已注销
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"指定签署人账号已注销"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo": {"psnId":"${deleted_psn}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'当前签署流程指定账号不存在/已注销（账号：${deleted_psn}），请检查后重试']
      - eq: ["content.code",1435324]

- test:
    name: 指定企业签签署主体账号已注销
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"指定签署主体账号已注销"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${deleted_org}","transactorInfo": {"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'当前签署流程指定账号不存在/已注销（账号：${deleted_org}），请检查后重试']
      - eq: ["content.code",1435324]

- test:
    name: 指定个人签签署人账号已注销
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"指定签署主体账号已注销"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${deleted_psn}"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":0}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'当前签署流程指定账号不存在/已注销（账号：${deleted_psn}），请检查后重试']
      - eq: ["content.code",1435324]




- test:
    name: 获取发起流程页面链接-正常添加合同备注
    variables:
      - json: {"signFlowConfig":{"signFlowTitle":"签署流程"},"signFlowInitiator": {"initialRemarks":["dhnjdfhgjdfhg测试","gjhhgjgbh","dfjhu遇到个月的姑父有低谷"]}}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]


- test:
    name: 获取发起流程页面链接-添加合同备注-超过3条-报错
    variables:
      - json: {"signFlowConfig":{"signFlowTitle":"签署流程"},"signFlowInitiator": {"initialRemarks":["大富大贵","dhnjdfhgjdfhg测试","gjhhgjgbh","dfjhu遇到个月的姑父有低谷"]}}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message","合同备注不能超过3条"]
      - eq: ["content.code",147335]


- test:
    name: 获取发起流程页面链接-添加合同备注-超过300字-报错
    variables:
      - json: {"signFlowConfig":{"signFlowTitle":"签署流程"},"signFlowInitiator": {"initialRemarks":["大富大贵","dhnjdfhgjdfhg测试","发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海发的金海1"]}}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message","单条合同备注不能超过300个字符"]
      - eq: ["content.code",147336]



- test:
    name: 获取发起流程页面链接-指定独立签署日期-签署区为手动签署，独立签署日期autoSign =true
    variables:
      - json: {"docs":[{"fileId":"${fileId1}","fileName":"小白花假装霸道总裁女友协议.pdf"}],"signFlowConfig":{"signFlowTitle":"页面发起-1","autoFinish":true,"autoStart":true},"signers":[{"orgSignerInfo":{"orgId":"${org_id_1}","transactorInfo":{"psnId":"${psn_id_1}"}},"signerType":1,"signFields":[{"signFieldType":"0","fileId":"${fileId1}","normalSignFieldConfig":{"signFieldStyle":1,"autoSign":false,"freeMode":false,"signFieldPosition":{"positionPage":"1","positionX":"150","positionY":"250"}}},{"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":true,"fontSize":13,"dateFormat":"yyyy年MM月dd日","signDatePositionPage":1,"signDatePositionX":"150","signDatePositionY":"200"}}]}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","独立签署日期和普通签署区自动签类型需保持相同"]
      - ne: ["content.code",0]

- test:
    name: 获取发起流程页面链接-指定独立签署日期-签署区为自动签署，独立签署日期autoSign =false
    variables:
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_1}"},"signConfig":{"signOrder":1},"signFields":[{"fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0},{"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":false,"fontSize":13,"dateFormat":"yyyy年MM月dd日","signDatePositionPage":1,"signDatePositionX":"150","signDatePositionY":"200"}}],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","独立签署日期和普通签署区自动签类型需保持相同"]
      - ne: ["content.code",0]

- test:
    name: 获取发起流程页面链接-指定独立签署日期-参数校验：dateFormat，传入非法值
    variables:
      - json: {"docs":[{"fileId":"${fileId1}","fileName":"小白花假装霸道总裁女友协议.pdf"}],"signFlowConfig":{"signFlowTitle":"页面发起-1","autoFinish":true,"autoStart":true},"signers":[{"orgSignerInfo":{"orgId":"${org_id_1}","transactorInfo":{"psnId":"${psn_id_1}"}},"signerType":1,"signFields":[{"signFieldType":"0","fileId":"${fileId1}","normalSignFieldConfig":{"signFieldStyle":1,"autoSign":false,"freeMode":false,"signFieldPosition":{"positionPage":"1","positionX":"150","positionY":"250"}}},{"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":false,"fontSize":13,"dateFormat": "yyyy-MM-dd ss","signDatePositionPage":1,"signDatePositionX":"150","signDatePositionY":"200"}}]}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","不支持该日期格式"]
      - ne: ["content.code",0]

- test:
    name: 获取发起流程页面链接-指定独立签署日期, 参数校验：signDatePositionPage不传
    variables:
      - json: {"docs":[{"fileId":"${fileId1}","fileName":"小白花假装霸道总裁女友协议.pdf"}],"signFlowConfig":{"signFlowTitle":"页面发起-1","autoFinish":true,"autoStart":true},"signers":[{"orgSignerInfo":{"orgId":"${org_id_1}","transactorInfo":{"psnId":"${psn_id_1}"}},"signerType":1,"signFields":[{"signFieldType":"0","fileId":"${fileId1}","normalSignFieldConfig":{"signFieldStyle":1,"autoSign":false,"freeMode":false,"signFieldPosition":{"positionPage":"1","positionX":"150","positionY":"250"}}},{"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":false,"fontSize":13,"dateFormat": "yyyy年MM月dd日","signDatePositionPage_1":1,"signDatePositionX":"150","signDatePositionY":"200"}}]}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","signDatePositionPage不能为空"]
      - ne: ["content.code",0]

- test:
    name: 获取发起流程页面链接-指定独立签署日期, 参数校验：signDatePositionX不传
    variables:
      - json: {"docs":[{"fileId":"${fileId1}","fileName":"小白花假装霸道总裁女友协议.pdf"}],"signFlowConfig":{"signFlowTitle":"页面发起-1","autoFinish":true,"autoStart":true},"signers":[{"orgSignerInfo":{"orgId":"${org_id_1}","transactorInfo":{"psnId":"${psn_id_1}"}},"signerType":1,"signFields":[{"signFieldType":"0","fileId":"${fileId1}","normalSignFieldConfig":{"signFieldStyle":1,"autoSign":false,"freeMode":false,"signFieldPosition":{"positionPage":"1","positionX":"150","positionY":"250"}}},{"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":false,"fontSize":13,"dateFormat": "yyyy年MM月dd日","signDatePositionPage":1,"signDatePositionX-1":"150","signDatePositionY":"200"}}]}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","signDatePositionX不能为空"]
      - ne: ["content.code",0]

- test:
    name: 获取发起流程页面链接-指定独立签署日期, 参数校验：signDatePositionY不传
    variables:
      - json: {"docs":[{"fileId":"${fileId1}","fileName":"小白花假装霸道总裁女友协议.pdf"}],"signFlowConfig":{"signFlowTitle":"页面发起-1","autoFinish":true,"autoStart":true},"signers":[{"orgSignerInfo":{"orgId":"${org_id_1}","transactorInfo":{"psnId":"${psn_id_1}"}},"signerType":1,"signFields":[{"signFieldType":"0","fileId":"${fileId1}","normalSignFieldConfig":{"signFieldStyle":1,"autoSign":false,"freeMode":false,"signFieldPosition":{"positionPage":"1","positionX":"150","positionY":"250"}}},{"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":false,"fontSize":13,"dateFormat": "yyyy年MM月dd日","signDatePositionPage":1,"signDatePositionX":"150","signDatePositionY_1":"200"}}]}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","signDatePositionY不能为空"]
      - ne: ["content.code",0]

- test:
    name: 获取发起流程页面链接-指定独立签署日期-手动签-获取链接成功
    variables:
      - json: {"initiatePageConfig":{"orgSealType":"1"},"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"法人签"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_2}","transactorInfo":{"psnId":"${psn_id_1}"}},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0},{"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":false,"fontSize":13,"dateFormat":"yyyy年MM月dd日","signDatePositionPage":1,"signDatePositionX":"150","signDatePositionY":"200"}}],"signerType":2}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'成功']
      - eq: ["content.code",0]

- test:
    name: 获取发起流程页面链接-平台自动签-传独立日期控件
    variables:
      - dateSignField: {"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":true,"fontSize":13,"dateFormat":"yyyy年MM月dd日","signDatePositionPage":1,"signDatePositionX":"150","signDatePositionY":"200"}}
      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0},$dateSignField],"signerType":1}]}
    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

#- test:
#    name: 获取发起流程页面链接-平台自动签-不传独立日期控件
#    variables:
#      - dateSignField: {"signFieldType":"2","fileId":"${fileId1}","dateSignFieldConfig":{"autoSign":true,"fontSize":13,"dateFormat":"yyyy年MM月dd日","signDatePositionPage":1,"signDatePositionX":"150","signDatePositionY":"200"}}
#      - json: {"docs":[{"fileId":"${fileId1}"}],"signFlowConfig":{"signFlowTitle":"企业自动签","autoFinish":true},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${fileId1}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":100,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}
#    api: api/signflow/v3/sign-flow/sign-flow-initiate-url/by-file.yml
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.message",成功]
#      - eq: ["content.code",0]