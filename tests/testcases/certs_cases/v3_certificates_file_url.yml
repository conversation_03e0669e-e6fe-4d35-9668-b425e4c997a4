- config:
    name: "V3API证书文件下载URL接口基本功能验证"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id)}
      - valid_psn_id: "509acabf5f784507abe98db6f93628c2"
      - invalid_psn_id: "invalid_psn_id_test"
      - empty_psn_id: ""


- test:
    name: "正常场景-有效psnId获取证书文件下载URL"
    api: api/v3/cert/certificates-file-url.yml
    variables:
      - app_id: $app_id
      - json: {"psnId": "$valid_psn_id"}
    extract:
      - fileDownloadUrl: content.data.fileDownloadUrl
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.success", true]
      - type_match: ["content.data.fileDownloadUrl", str]
      - len_gt: ["content.data.fileDownloadUrl", 0]
      - contains: ["content.data.fileDownloadUrl", "http"]

- test:
    name: "异常场景-psnId参数缺失"
    api: api/v3/cert/certificates-file-url.yml
    variables:
      - app_id: $app_id
      - json: {}
    validate:
      - eq: ["status_code", 200]
      - ne: ["content.code", 0]
      - type_match: ["content.message", str]
      - eq: ["content.success", false]

- test:
    name: "异常场景-psnId为空字符串"
    api: api/v3/cert/certificates-file-url.yml
    variables:
      - app_id: $app_id
      - json: {"psnId": "$empty_psn_id"}
    validate:
      - eq: ["status_code", 200]
      - ne: ["content.code", 0]
      - type_match: ["content.message", str]
      - eq: ["content.success", false]

- test:
    name: "异常场景-无效的psnId"
    api: api/v3/cert/certificates-file-url.yml
    variables:
      - app_id: $app_id
      - json: {"psnId": "$invalid_psn_id"}
    validate:
      - eq: ["status_code", 200]
      - ne: ["content.code", 0]
      - type_match: ["content.message", str]
      - eq: ["content.success", false]

- test:
    name: "异常场景-psnId参数类型错误"
    api: api/v3/cert/certificates-file-url.yml
    variables:
      - app_id: $app_id
      - json: {"psnId": 123456}
    validate:
      - eq: ["status_code", 200]
      - ne: ["content.code", 0]
      - type_match: ["content.message", str]
      - eq: ["content.success", false]

- test:
    name: "异常场景-app_id缺失"
    api: api/v3/cert/certificates-file-url.yml
    variables:
      - app_id: ""
      - json: {"psnId": "$valid_psn_id"}
    validate:
      - eq: ["status_code", 200]
      - ne: ["content.code", 0]
      - type_match: ["content.message", str]
      - eq: ["content.success", false]

- test:
    name: "业务验证-验证下载URL可访问性"
    api: api/v3/cert/certificates-file-url.yml
    variables:
      - app_id: $app_id
      - json: {"psnId": "$valid_psn_id"}
    extract:
      - fileDownloadUrl: content.data.fileDownloadUrl
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.success", true]
      - type_match: ["content.data.fileDownloadUrl", str]
      - len_gt: ["content.data.fileDownloadUrl", 0]
      - contains: ["content.data.fileDownloadUrl", "http"]
      # 验证URL格式正确性
      - regex_match: ["content.data.fileDownloadUrl", "^https?://.*"]
