- config:
    name: "前程无忧二期 - createFlowOneStep接口发起允许不传入经办人（只针对企业主体签署区）"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id1: ${ENV(xuanyuan_appid)}
      - group: ''
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - path_pdf1: data/个人借贷合同.pdf
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}
      - dongying_transfer_receiver: ${ENV(dongying_transfer_receiver)}
      - geli_transfer_receiver: ${ENV(geli_transfer_receiver)}
      - group: ''

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId





- test:
    name: "创建机构 - esigntest测试书雪企业账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying2201211346wer
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId


- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 创建流程-顺序签-不同企业主体-不同签署区-appid配置流程开启时转交-但是企业未开启转交
    variables:
      - app_id: $app_id
      - extend:
      - autoArchive: False
      - businessScene: 创建流程-顺序签-不同企业主体-不同签署区-开启时转交
      - configInfo: $configInfo
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: "添加企业签署区 "
    variables:
      - app_id: $app_id
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1_2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "添加企业签署区"
    variables:
      - app_id: $app_id
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$geli_transfer_receiver,"actorIndentityType":"2","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1_3: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "添加法人签署区：需转交"
    variables:
      - app_id: $app_id
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"3","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":4,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1_4: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加经办人签署区"
    variables:
      - app_id: $app_id
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"4","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":3,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1_5: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加企业签署区"
    variables:
      - app_id: $app_id
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":3,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1_6: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加法人签署区"
    variables:
      - app_id: $app_id
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"3","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":5,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1_7: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "添加经办人签署区"
    variables:
      - app_id: $app_id
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"4","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1_8: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加企业签署区"
    variables:
      - app_id: $app_id
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1_9: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


#开启签署流程
- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}








- test:
    name: 查询签署区信息3
    variables:
      - flowId: $flowId1
      - signfieldIds: $signfieldId1_3
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $geli_transfer_receiver]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 2]


- test:
    name: 查询签署区信息4
    variables:
      - flowId: $flowId1
      - signfieldIds: $signfieldId1_4
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 3]

- test:
    name: 查询签署区信息5
    variables:
      - flowId: $flowId1
      - signfieldIds: $signfieldId1_5
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 4]



- test:
    name: 查询签署区信息6
    variables:
      - flowId: $flowId1
      - signfieldIds: $signfieldId1_6
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 2]


- test:
    name: 查询签署区信息7
    variables:
      - flowId: $flowId1
      - signfieldIds: $signfieldId1_7
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 3]


- test:
    name: 查询签署区信息8
    variables:
      - flowId: $flowId1
      - signfieldIds: $signfieldId1_8
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 4]



- test:
    name: 查询签署区信息9
    variables:
      - flowId: $flowId1
      - signfieldIds: $signfieldId1_9
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 2]



- test:
    name: 查询签署区信息2
    variables:
      - flowId: $flowId1
      - signfieldIds: $signfieldId1_2
    api: api/iterate_cases/Search_signfields_2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 2]























