- config:
    name: "0528接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(appId_xuanyuan)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: ${ENV(appId_xuanyuan)}
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建签署人账号 - gan"
    variables:
      - app_id: ${ENV(appId_xuanyuan)}
      - thirdPartyUserId: zzzzzzzzzzzzzzzzz
      - name: gannning
      - idNumber: 302323199201061067
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId

- test:
    name: "appId_xuanyuan下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(appId_xuanyuan)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(appId_xuanyuan)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "使用caogu_accountId创建机构"
    variables:
      - app_id: ${ENV(appId_xuanyuan)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId



- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(appId_xuanyuan)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#-   test:
#        teardown_hooks:
#        - ${teardown_seals($response)}
#        name: "查询实名组织下的企业印章"
#        variables:
#        -   app_id: ${ENV(appId_xuanyuan)}
#        -   #        -   orgId: $standard_sixian_organize_id
#        api: api/seals/seals/select_c_seals.yml
#        extract:
#        -   org_sealId1: org_sealId1
#        -   org_sealId2: org_sealId2
#        -   org_sealId3: org_sealId3
#        -   legal_sealId1: legal_sealId1
#        -   cancellation_sealId: cancellation
#        validate:
#        -   eq: ["content.code",0]
#        -   eq: ["content.message", "成功"]

- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(appId_xuanyuan)}
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_open_sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

      #-   test:
      #        name: "创建签署流程"
      #        variables:
      #        -   app_id: ${ENV(appId_xuanyuan)}
      #        -         #        -   autoArchive: false
      #        -   businessScene: 悟空非实名签校验add-update字段
      #        -   configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: ''}
      #        -   contractValidity: ""
      #        -   extend: {}
      #        -   initiatorAccountId: $accountId_gan
      #        -   payerAccountId: ""
      #        -   signValidity: ${get_timestamp(30)}
      #        -   initiatorAuthorizedAccountId: ""
      #        api: api/iterate_cases/signflowsCreate.yml
      #        extract:
      #        -   sign_flowId: content.data.flowId
      #        validate:
      #        -   len_gt: ["content.data.flowId",0]
      #        -   eq: ["status_code", 200]
      #
      #
      #-   test:
      #        name: "添加1份流程文档"
      #        variables:
      #        -   app_id: ${ENV(appId_xuanyuan)}
      #        -         #        -   docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      #        -   flowId: $sign_flowId
      #        api: api/iterate_cases/addFlowDoc.yml
      #        validate:
      #        -   eq: ["content.code",0]




      #-   test:
      #        name: "个人-添加成功个人签名域-指定签名域 可以添加位置信息"
      #        variables:
      #        -   app_id: ${ENV(appId_xuanyuan)}
      #        -         #        -   flowId: $sign_flowId
      #        -   signfields: [{"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "创建第一个手动签署区"},"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"3","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"sealType":null,"sealId":$legal_sealId1,"signType":0}]
      #        api: api/iterate_cases/signfieldsCreate_handSign.yml
      #        extract:
      #        - signfieldIds0: content.data.signfieldBeans.0.signfieldId
      #        validate:
      - eq: ["content.message", "成功"]



      #- test:
      #    name: "添加流程抄送人"
      #    variables:
      #        #        - app_id: ${ENV(appId_xuanyuan)}
      #        - flowId: $sign_flowId
      #        - recipients: [{"recipientAccountId": $accountId_gan,"recipientIdentityAccountId": $accountId_gan,"recipientIdentityAccountType":0,"roleType": 2}]
      #    api: api/mobile-shield/recipients.yml
      #    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
#
#
#-   test:
#        name: "开启签署流程"
#        variables:
#        -   app_id: ${ENV(appId_xuanyuan)}
#        -   #        -   flowId: $sign_flowId
#        api: api/signflow/signflows/signflowsStart.yml
#        validate:
#        -   eq: ["content.code",0]




#-  test:
#    name: 添加或更新签署区并执行签署
#    variables:
#        -   app_id: ${ENV(appId_xuanyuan)}
#        -   #        -   flowId: $sign_flowId
#        -   accountId: $yuzan_accountId
#        -   sealId: $p1_sealId
#        -   addSignfield:  {"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"3","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "用户手动签用户手动签用户手动签12345用户手动签1234578901234567890"},"sealType":"","sealId":$legal_sealId1,"signType":1}
#        -   addSignfields: [$addSignfield]
#        -   updateSignfields: []
#        -   dingUser: {}
#        -   approvalPolicy: 0
#        -   signfieldIds: []
#    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
#    validate:
#        -   eq: ["status_code", 200]
#        -   contains: ["content.message","成功"]
#
#
#



#- test:
#    name: 查询个人印章列表：使用查询到的印章进行签署
#    api: api/seals/seals/select_p_seals.yml
#    variables:
#        - app_id: ${ENV(appId_xuanyuan)}
#        -   #        - accountId: $yuzan_accountId
#    extract:
#      - p1_sealId: content.data.seals.0.sealId
#    validate:

#- test:
#    name: 查询个人印章列表：使用查询到的印章进行签署
#    api: api/seals/seals/select_p_seals.yml
#    variables:
#        - app_id: ${ENV(appId_xuanyuan)}
#        -   #        - accountId: $accountId_ckl
#    extract:
#      - p1_ckl: content.data.seals.0.sealId
#    validate:


- test:
    name: 一步发起指定证书id用户手动签-成功
    variables:
      - app_id: ${ENV(appId_xuanyuan)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 3,

            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 100,
                "posY": 100
              },
            "signType": 0,
            "signerRoleType": "3"
          }
          ],
        "thirdOrderNo": "xyz"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

#-  test:
#    name: 添加或更新签署区并执行签署
#    variables:
#        -   app_id: ${ENV(appId_xuanyuan)}
#        -   #        -   flowId: $sign_flow_one
#        -   accountId: $yuzan_accountId
#        -   sealId: $p1_sealId
#        -   addSignfield:  {"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"3","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100},"sealType":"","sealId":$legal_sealId1,"signType":1}
#        -   addSignfields: [$addSignfield]
#        -   updateSignfields: []
#        -   dingUser: {}
#        -   approvalPolicy: 0
#        -   signfieldIds: []
#    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
#    validate:
#        -   eq: ["status_code", 200]
#        -   contains: ["content.message","成功"]

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
