#获取意愿连接
request:
  url: /v2/signflows/$flowId/individual/realNameWill/$accountId/identifyUrl
  method: POST
  headers:  ${get_headers($app_id)}
  json:
    {
      accountId: $accountId,
      app_id: $app_id,
      appScheme: "",
      authType: "",
      billParams: {
        payerAccountGid: "",
        saleSchemaId: $saleSchemaId,
        scope: ""
      },
      bizContext: {},
      bizCtxIds: [$flowId],
      bizDecs: "",
      bizType: 1,
      configParams: {
        authType: "",
        indivUneditableInfo: []
      },
      contextInfo: {
        contextId: "",
        showResultPage: true
      },
      indivInfo: {
        bankCardNo: "",
        certNo: "",
        certType: "",
        mobileNo: "",
        name: "",
        nationality: ""
      },
      notifyUrl: "",
      origin: "",
      redirectUrl: "http://www.baiu.com?tsignDes=签署成功&tsignTypeSIGN&tsignCode=0",
      repeatIdentity: true,
      wukongOid: $accountId,
      scene: $scene
    }
validate:
  - eq: ["status_code",200]