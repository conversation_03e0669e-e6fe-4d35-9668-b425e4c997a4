- config:
    name: 多文档盖多个印章签署
    base_url: ${ENV(footstone_api_url)}
    variables:
      - client_id: pc
      - app_id: ${ENV(X-Tsign-Open-App-Id)}
      - mobile_1: ***********
      - idType_person: CRED_PSN_CH_IDCARD
      - name_person_1: 陈凯丽
      - idNumber_person_1: 362323199201061068
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - mobileNew: $mobile_1

- test:
    name: 创建个人账号1
    variables:
      - json:
          {
            "name": $name_person_1,
            "thirdPartyUserId": autotest-***********,
            "idType": $idType_person,
            "idNumber": $idNumber_person_1,
            "mobile": $mobile_1
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - personOid: content.data.accountId

- test:
    name: 查询个人账号1
    variables:
      - accountId: $personOid
    api: api/user/account_third/xy_get_info.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code", 200 ]
      - eq: [ "content.data.name",$name_person_1 ]

- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId:
    api: api/file_template/files/get_uploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同1.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同2.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同3.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId3: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同4.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId4: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同5.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId5: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]
- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同6.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId6: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同7.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId7: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同8.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId8: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同9.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId9: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同10.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId10: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同11.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId11: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同12.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId12: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同13.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId13: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同14.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId14: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同15.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId15: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同16.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId16: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同17.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId17: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同18.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId18: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同19.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId19: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同20.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId20: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 创建流程
    variables:
      - autoArchive: True
      - businessScene: 多文档盖多个印章
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '' }
      - extend: { }
      - initiatorAccountId: $personOid
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]
      - eq: [ "status_code", 200 ]

- test:
    name: 批量添加流程文档：20个文档
    variables:
      - flowId: $flowId1
      - doc1: { 'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0 }
      - doc2: { 'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0 }
      - doc3: { 'fileHash': '', 'fileId': $fileId3, 'fileName': 'fileThree', 'filePassword': '', 'encryption': 0 }
      - doc4: { 'fileHash': '', 'fileId': $fileId4, 'fileName': 'fileFour', 'filePassword': '', 'encryption': 0 }
      - doc5: { 'fileHash': '', 'fileId': $fileId5, 'fileName': 'fileFive', 'filePassword': '', 'encryption': 0 }
      - doc6: { 'fileHash': '', 'fileId': $fileId6, 'fileName': 'fileSix', 'filePassword': '', 'encryption': 0 }
      - doc7: { 'fileHash': '', 'fileId': $fileId7, 'fileName': 'fileSeven', 'filePassword': '', 'encryption': 0 }
      - doc8: { 'fileHash': '', 'fileId': $fileId8, 'fileName': 'fileEight', 'filePassword': '', 'encryption': 0 }
      - doc9: { 'fileHash': '', 'fileId': $fileId9, 'fileName': 'fileNine', 'filePassword': '', 'encryption': 0 }
      - doc10: { 'fileHash': '', 'fileId': $fileId10, 'fileName': 'fileTen', 'filePassword': '', 'encryption': 0 }
      - doc11: { 'fileHash': '', 'fileId': $fileId11, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0 }
      - doc12: { 'fileHash': '', 'fileId': $fileId12, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0 }
      - doc13: { 'fileHash': '', 'fileId': $fileId13, 'fileName': 'fileThree', 'filePassword': '', 'encryption': 0 }
      - doc14: { 'fileHash': '', 'fileId': $fileId14, 'fileName': 'fileFour', 'filePassword': '', 'encryption': 0 }
      - doc15: { 'fileHash': '', 'fileId': $fileId15, 'fileName': 'fileFive', 'filePassword': '', 'encryption': 0 }
      - doc16: { 'fileHash': '', 'fileId': $fileId16, 'fileName': 'fileSix', 'filePassword': '', 'encryption': 0 }
      - doc17: { 'fileHash': '', 'fileId': $fileId17, 'fileName': 'fileSeven', 'filePassword': '', 'encryption': 0 }
      - doc18: { 'fileHash': '', 'fileId': $fileId18, 'fileName': 'fileEight', 'filePassword': '', 'encryption': 0 }
      - doc19: { 'fileHash': '', 'fileId': $fileId19, 'fileName': 'fileNine', 'filePassword': '', 'encryption': 0 }
      - doc20: { 'fileHash': '', 'fileId': $fileId20, 'fileName': 'fileTen', 'filePassword': '', 'encryption': 0 }
      - docs: [ $doc1,$doc2,$doc3,$doc4,$doc5,$doc6,$doc7,$doc8,$doc9,$doc10,$doc11,$doc12,$doc13,$doc14,$doc15,$doc16,$doc17,$doc18,$doc19,$doc20 ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "status_code", 200 ]

- test:
    name: 批量添加流程签署区：20个文档，每个文档添加1个签署区
    variables:
      - posBean1: { }
      - flowId: $flowId1
      - signfield1: ${gen_signfield_data_V2(0,0,$fileId1,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield2: ${gen_signfield_data_V2(0,0,$fileId2,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield3: ${gen_signfield_data_V2(0,0,$fileId3,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield4: ${gen_signfield_data_V2(0,0,$fileId4,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield5: ${gen_signfield_data_V2(0,0,$fileId5,$flowId1,$posBean1,0,$personOid,,,)}

      - signfield6: ${gen_signfield_data_V2(0,0,$fileId6,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield7: ${gen_signfield_data_V2(0,0,$fileId7,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield8: ${gen_signfield_data_V2(0,0,$fileId8,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield9: ${gen_signfield_data_V2(0,0,$fileId9,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield10: ${gen_signfield_data_V2(0,0,$fileId10,$flowId1,$posBean1,0,$personOid,,,)}

      - signfield11: ${gen_signfield_data_V2(0,0,$fileId11,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield12: ${gen_signfield_data_V2(0,0,$fileId12,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield13: ${gen_signfield_data_V2(0,0,$fileId13,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield14: ${gen_signfield_data_V2(0,0,$fileId14,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield15: ${gen_signfield_data_V2(0,0,$fileId15,$flowId1,$posBean1,0,$personOid,,,)}

      - signfield16: ${gen_signfield_data_V2(0,0,$fileId16,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield17: ${gen_signfield_data_V2(0,0,$fileId17,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield18: ${gen_signfield_data_V2(0,0,$fileId18,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield19: ${gen_signfield_data_V2(0,0,$fileId19,$flowId1,$posBean1,0,$personOid,,,)}
      - signfield20: ${gen_signfield_data_V2(0,0,$fileId20,$flowId1,$posBean1,0,$personOid,,,)}

      - signfields: [ $signfield1,$signfield2,$signfield3,$signfield4,$signfield5,$signfield6,$signfield7,$signfield8,$signfield9,$signfield10,$signfield11,$signfield12,$signfield13,$signfield14,$signfield15,$signfield16,$signfield17,$signfield18,$signfield19,$signfield20 ]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
      - signfieldId2: content.data.signfieldIds.1
      - signfieldId3: content.data.signfieldIds.2
      - signfieldId4: content.data.signfieldIds.3
      - signfieldId5: content.data.signfieldIds.4
      - signfieldId6: content.data.signfieldIds.5
      - signfieldId7: content.data.signfieldIds.6
      - signfieldId8: content.data.signfieldIds.7
      - signfieldId9: content.data.signfieldIds.8
      - signfieldId10: content.data.signfieldIds.9
      - signfieldId11: content.data.signfieldIds.10
      - signfieldId12: content.data.signfieldIds.11
      - signfieldId13: content.data.signfieldIds.12
      - signfieldId14: content.data.signfieldIds.13
      - signfieldId15: content.data.signfieldIds.14
      - signfieldId16: content.data.signfieldIds.15
      - signfieldId17: content.data.signfieldIds.16
      - signfieldId18: content.data.signfieldIds.17
      - signfieldId19: content.data.signfieldIds.18
      - signfieldId20: content.data.signfieldIds.19
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "status_code", 200 ]

- test:
    name: 查询签署区
    variables:
      - flowId: $flowId1
      - accountId:
      - signfieldIds: $signfieldId1
    api: api/signflow/signfields/signfieldsSelect.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "status_code", 200 ]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId1
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "status_code", 200 ]

      #- test:
      #    name: 登录申请
      #    api: api/user/accounts/login_apply.yml
      #    variables:
      #      - path: "authCode"
      #      - mobileNew: $mobileNew
      #    validate:
      - eq: [ "content.code", 0 ]

      #- test:
      #    name: 登录验证
      #    setup_hooks:
      #      - ${sleep_N_secs(5)}
      #    api: api/user/accounts/commit.yml
      #    variables:
      #      - loginParams: {"endpoint": "PC","expireTime": 0}
      #      - credentials: "123456"
      #      - path: "authCode"
      #      - mobileNew: $mobileNew
      #    validate:
      - eq: [ "content.code", 0 ]


- test:
    name: 校验手机号/邮箱是否已经注册
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}
    api: api/user/accounts/get_checkMobileEmail.yml
    variables:
      - principal: $mobileNew
    extract:
      - personOid_realname: content.data.accountId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code", 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $personOid
      - flowId: $flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.bizId",0 ]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $personOid
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code", 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid
    extract:
      - p_sealId: content.data.seals.0.sealId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code", 200 ]

- test:
    name: 批量签署
    variables:
      - posBean: { addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0 }
      - addSignfield1: ${gen_signfield_data_V2(0,0,$fileId1,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield2: ${gen_signfield_data_V2(0,0,$fileId2,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield3: ${gen_signfield_data_V2(0,0,$fileId3,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield4: ${gen_signfield_data_V2(0,0,$fileId4,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield5: ${gen_signfield_data_V2(0,0,$fileId5,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield6: ${gen_signfield_data_V2(0,0,$fileId6,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield7: ${gen_signfield_data_V2(0,0,$fileId7,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield8: ${gen_signfield_data_V2(0,0,$fileId8,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield9: ${gen_signfield_data_V2(0,0,$fileId9,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield10: ${gen_signfield_data_V2(0,0,$fileId10,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield11: ${gen_signfield_data_V2(0,0,$fileId11,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield12: ${gen_signfield_data_V2(0,0,$fileId12,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield13: ${gen_signfield_data_V2(0,0,$fileId13,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield14: ${gen_signfield_data_V2(0,0,$fileId14,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield15: ${gen_signfield_data_V2(0,0,$fileId15,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield16: ${gen_signfield_data_V2(0,0,$fileId16,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield17: ${gen_signfield_data_V2(0,0,$fileId17,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield18: ${gen_signfield_data_V2(0,0,$fileId18,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield19: ${gen_signfield_data_V2(0,0,$fileId19,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield20: ${gen_signfield_data_V2(0,0,$fileId20,$flowId1,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfields: [ $addSignfield1,$addSignfield2,$addSignfield3,$addSignfield4,$addSignfield5,$addSignfield6,$addSignfield7,$addSignfield8,$addSignfield9,$addSignfield10,$addSignfield11,$addSignfield12,$addSignfield13,$addSignfield14,$addSignfield15,$addSignfield16,$addSignfield17,$addSignfield18,$addSignfield19,$addSignfield20 ]
      - updateSignfields: [ ]
      - signfieldIds: [ $signfieldId1,$signfieldId2,$signfieldId3,$signfieldId4,$signfieldId5,$signfieldId6,$signfieldId7,$signfieldId8,$signfieldId9,$signfieldId10,$signfieldId11,$signfieldId12,$signfieldId13,$signfieldId14,$signfieldId15,$signfieldId16,
                        $signfieldId17,$signfieldId18,$signfieldId19,$signfieldId20 ]
      - accountId: $personOid
      - dingUser: { }
      - approvalPolicy: 0
      - flowId: $flowId1
      - operator_id: $personOid
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "status_code", 200 ]
      #    teardown_hooks:
      #        - ${hook_sleep_n_secs(8)}

      #- test:
      #    name: 查询流程：已归档
      #    api: api/signflow/signflows/signflowsSelect.yml
      #    variables:
      #       - flowId: $flowId1
      #    validate:
#      - eq: ["content.data.flowStatus",2]
