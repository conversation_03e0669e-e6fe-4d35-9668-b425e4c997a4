- config:
    name: 创建模板相关各种操作
    ###def: template_action($contentMd5,$contentType,$fileName,$fileSize,$file_path,$fileKey, $templateName,$pageNo, $pageSize)
    request:
      base_url: "${ENV(footstone_api_url)}"
      headers:
        Content-Type: "application/json"


- test:
    name: 获取上传文件url
    api: api/file_template/files/get_uploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variable_binds:
      - binary: ${open_file($file_path)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 创建模板
    variables:
      - fileMimeType: application/pdf
    api: api/file_template/templates/create_templates.yml
    extract:
      - templateId: content.data.templateId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 获取模板列表
    api: api/file_template/templates/get_templates.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 添加模板结构化组件
    variables:
      - structComponent: [{"allowEdit": true,"context": {"ext": "","label": "结构化组件名称","limit": "","pos": {"page": 1,"x": 50,"y": 50},"required": true,"style": {"font": 2,"fontSize": 13,"height": 3,"textColor": "","width": 10}},"refId": "","type": 0}]
    api: api/file_template/templates/add_components.yml
    extract:
      - id: content.data.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 编辑模板结构化组件
    variables:
      - structComponent: [{"allowEdit": true,"context": {"ext": "","label": "编辑结构化组件名称","limit": "","pos": {"page": 2,"x": 40,"y": 40},"required": true,"style": {"font": 1,"fontSize": 12,"height": 15,"textColor": "","width": 15}},"id": $id,"refId": "","type": 0}]
    api: api/file_template/templates/add_components.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 删除模板结构化组件
    api: api/file_template/templates/delete_components.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 获取模板下载地址
    api: api/file_template/templates/get_template_downloadUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 获取模板设置页面地址
    api: api/file_template/templates/get_template_getSettingUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 删除模板
    api: api/file_template/templates/delete_templates.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]