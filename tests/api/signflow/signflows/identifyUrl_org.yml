request:
  url: /v2/signflows/$flowId/organ/auth/$orgId/identifyUrl
  method: POST
  headers: ${get_headers($app_Id1,None,None,None,$language)}
  json:
    {
      agentAccountId: $accountId,
      app_id: $app_id1,
      appScheme: "",
      authType: "",
      billParams: {
        payerAccountGid: "",
        saleSchemaId: $saleSchemaId,
        scope: ""
      },
      bizContext: { },
      bizCtxIds: [ $flowId ],
      bizDecs: "",
      bizType: 1,
      configParams: {
        authType: "",
        indivUneditableInfo: [ ]
      },
      contextInfo: {
        contextId: "",
        showResultPage: true
      },
      indivInfo: {
        bankCardNo: "",
        certNo: "",
        certType: "",
        mobileNo: "",
        name: "",
        nationality: ""
      },
      notifyUrl: "",
      origin: "",
      redirectUrl: "www.baidu.com",
      repeatIdentity: true,
      wukongOid: $accountId,
      scene: $scene
    }
validate:
  - eq: [ status_code, 200 ]
#获取多主体签署链接
