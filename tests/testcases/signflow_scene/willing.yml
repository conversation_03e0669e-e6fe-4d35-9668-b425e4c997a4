- config:
    name: 意愿
    ###def: willing($bzqAccountId,$flowId)

- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $personOid_realname
      - flowId: $flowId
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $personOid_realname
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]