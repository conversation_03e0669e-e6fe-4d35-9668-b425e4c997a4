- config:
    name: "绑定证书接口校验"
    variables:
      - id_No: '331082199211223080'
      - id_No2: '362324199301130629'
      - Name: 梁贤红
      - app_id1: ${ENV(appid_cert)}
      - app_id2: ${ENV(appid_NO_cert)}
      - id_Type: CRED_PSN_CH_IDCARD
      - mobile: ***********
      - Name2: 测试
      - thirdPartyUserId1: third1${get_randomString()}
      - thirdPartyUserId2: third2${get_randomString()}
      - thirdPartyUserId3: third3${get_randomString()}
      - thirdPartyUserId4: third4${get_randomString()}
      - name3: 曾艳
      - id_No3: 362324199301130629
      - certid_expired: ${ENV(certid_expired)}
    request:
      base_url: "${ENV(footstone_api_url)}"

- test:
    name: 创建账号1
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_id1
      - idNo: $id_No
      - mobile: $mobile
      - name: $Name
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid1: content.data.accountId

- test:
    name: 创建账号2与账号1不同姓名
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_id1
      - idNo: $id_No
      - mobile: $mobile
      - name: $Name2
      - thirdPartyUserId: $thirdPartyUserId2
    extract:
      - oid2: content.data.accountId

- test:
    name: 创建账号3与账号1不同证件号
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_id1
      - idNo: $id_No2
      - mobile: $mobile
      - name: $Name
      - thirdPartyUserId: $thirdPartyUserId3
    extract:
      - oid3: content.data.accountId

- test:
    name: 创建账号4与账号1同姓名、证件号
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_id1
      - idNo: $id_No
      - mobile: $mobile
      - name: $Name
      - thirdPartyUserId: $thirdPartyUserId4
    extract:
      - oid4: content.data.accountId

- test:
    name: "制证1"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId1: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]

- test:
    name: "制证2"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId2: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]

        #- test:
        #    name: "证书id必传，不传报错"
        #    api: api/user/cert-service/bindCerts.yml
        #    variables:
        #      - app_id: $app_id1
        #      - accountId: $oid1
        #      - certId: ''
        #    validate:
#      - eq: ["status.code",405]

- test:
    name: "绑定成功"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "重复绑定，成功"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]




- test:
    name: "oid换绑certid成功"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid1
      - certId: $certId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "certid多个oid成功"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid4
      - certId: $certId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "oid必传，不传报错"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: ''
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1000022]
      - eq: ["content.message", "缺少参数: ouid"]

- test:
    name: "证书id不存在，报错"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid1
      - certId: '111111'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书不存在"]

- test:
    name: "appid不传，报错"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: ''
      - accountId: $oid1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "参数错误: 应用id不能为空"]

- test:
    name: "appid不一致，报错"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id2
      - accountId: $oid1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书不在此appId下"]
- test:
    name: "姓名不一致，报错"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid2
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "绑定用户异常:信息不匹配,无法绑定"]

- test:
    name: "证件号不一致，报错"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid3
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "绑定用户异常:信息不匹配,无法绑定"]

- test:
    name: "吊销证书"
    api: api/user/cert-service/revokeCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid3
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "绑定已吊销证书，报错"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "绑定用户异常:非正常状态证书不支持绑定"]

- test:
    name: "绑定已过期证书，报错"
    api: api/user/cert-service/bindCerts.yml
    variables:
      - app_id: $app_id1
      - accountId: $oid1
      - certId: $certid_expired
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书已过期"]

