#流程附件

#查询附件
- api:
    def: attachmentsSelect($flowId)
    request:
      url: /v1/signflows/$flowId/attachments
      method: GET
      headers: ${get_headers($app_id)}
    validate:
      - eq: [status_code, 200]

#批量添加流程附件
- api:
    def: attachmentsCreate($flowId, $attachments)
    request:
      url: /v1/signflows/$flowId/attachments
      method: POST
      headers: ${get_headers($app_id)}
      json:
        attachments: $attachments
    validate:
      - eq: [status_code, 200]

#批量更新流程附件
- api:
    def: attachmentsUpdate($flowId, $attachments)
    request:
      url: /v1/signflows/$flowId/attachments
      method: PUT
      headers: ${get_headers($app_id)}
      json:
        attachments: $attachments
    validate:
      - eq: [status_code, 200]

#批量删除流程附件
- api:
    def: attachmentsDelete($flowId, $fileIds)
    request:
      url: /v1/signflows/$flowId/attachments?fileIds=$fileIds
      method: DELETE
      headers: ${get_headers($app_id)}
    validate:
      - eq: [status_code, 200]


#全量替换流程附件
- api:
    def: attachmentsReplace($flowId, $attachments)
    request:
      url: /v1/signflows/$flowId/attachments/createFromScratch
      method: POST
      headers: ${get_headers($app_id)}
      json:
        attachments: $attachments
    validate:
      - eq: [status_code, 200]
