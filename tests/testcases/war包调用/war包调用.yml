- config:
    name: "war包可以提交无签名域的签署动作"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - app_id1: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_id: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - account_id: ${ENV(tengqing_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - group: ${ENV(envCode)}

- test:
    name: "发起一个流程-无签名域"
    api: api/signflow/signflows/signflows.yml
    variables:
      - json: {"autoArchive": false,"businessScene": "无签名域war包调用签署接口${get_randomNo()}","configInfo": {},"createByApi": false,"hashSign": false}
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "上传待签文件"
    api: api/iterate_cases/addFlowDoc.yml
    variables:
      - docs: [{"encryption": 0,"fileId": "${file_id}"}]
      - flowId: ${flow_id}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启流程"
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: ${flow_id}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "发送短信验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - flowId: ${flow_id}
      - accountId: ${account_id}
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "校验短信验证码"
    api: api/iterate_cases/checkSms.yml
    variables:
      - authcode: '123456'
      - accountId: ${account_id}
      - bizId: $bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取印章列表"
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $account_id
    extract:
      - seal_id: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 调用war可以在无签名域的流程里提交签署动作
    variables:
      - flowId: ${flow_id}
      - accountId: $account_id
      - addSignfield:  {"actorIndentityType": 0,"assignedPosbean": true,"assignedSeal": true,"authorizedAccountId": "$account_id","autoExecute": false,"autoExecuteType": 0,"fieldType": 0,"fileId": "${file_id}","flowId": "${flow_id}","legalId": "","order": 1,"posBean": {"addSignTime": false,"posPage": "1","posX": 250.0,"posY": 250.0,"width": 159.0},"sealId": "${seal_id}","signFinishNotice": false,"signType": 1,"signWillingType": 0,"signerAccountId": "$account_id","synchExecute": false,"thirdCertId": false}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [addSignfield]
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: "发起一个流程-删除自由签名域"
    api: api/signflow/signflows/signflows.yml
    variables:
      - json: {"autoArchive": false,"businessScene": "无签名域war包调用签署接口${get_randomNo()}","configInfo": {},"createByApi": false,"hashSign": false}
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "上传待签文件"
    api: api/iterate_cases/addFlowDoc.yml
    variables:
      - docs: [{"encryption": 0,"fileId": "${file_id}"}]
      - flowId: ${flow_id}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加自由签名域"
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    variables:
      - flowId: ${flow_id}
      - signfields: [{"fileId":"${file_id}","signerAccountId":"$account_id","actorIndentityType":"0","authorizedAccountId":"$account_id","assignedPosbean":false,"signDateBeanType":"1","handDrawnWay":"1","fieldType":0,"order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealType":"0,1","sealId":"","signType":0,"signFinishNotice":true}]
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "删除自由签名域"
    api: api/signflow/signfields/signfieldsDelete.yml
    variables:
      - flowId: ${flow_id}
      - signfieldId: $signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启流程"
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: ${flow_id}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "发送短信验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - flowId: ${flow_id}
      - accountId: ${account_id}
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "校验短信验证码"
    api: api/iterate_cases/checkSms.yml
    variables:
      - authcode: '123456'
      - accountId: ${account_id}
      - bizId: $bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取印章列表"
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $account_id
    extract:
      - seal_id: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 调用war可以在无签名域的流程里提交签署动作
    variables:
      - flowId: ${flow_id}
      - accountId: $account_id
      - addSignfield:  {"actorIndentityType": 0,"assignedPosbean": true,"assignedSeal": true,"authorizedAccountId": "$account_id","autoExecute": false,"autoExecuteType": 0,"fieldType": 0,"fileId": "${file_id}","flowId": "${flow_id}","legalId": "","order": 1,"posBean": {"addSignTime": false,"posPage": "1","posX": 250.0,"posY": 250.0,"width": 159.0},"sealId": "${seal_id}","signFinishNotice": false,"signType": 1,"signWillingType": 0,"signerAccountId": "$account_id","synchExecute": false,"thirdCertId": false}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: [addSignfield]
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_war.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]