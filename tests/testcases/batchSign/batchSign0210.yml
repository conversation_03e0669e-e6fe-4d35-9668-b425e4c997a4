- config:
    name: "批量签署 ********** ba10b457b500080840d443adb200f2fe"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "**********"
      - xuanyuan_sixian: ${ENV(mobile_shield_wukong_appid_oid)}
      - xuanyuan_sixian_name: ${ENV(xuanyuan_sixian_name)}
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - group: ''
      - oid_linjue_tsign: "ebda3541171c44d4900447e857fae75a"  #linjue
      - phone_linjue: "***********"
      - mail_linjue: "<EMAIL>"
      - none_tsign_account_id: ${ENV(none_tsign_account_id)}
      - accountId_withRealName_withoutAuth: ${ENV(accountId_withRealName_withoutAuth)}
      - path_pdf1: data/16M.pdf
      - fileId: ${get_file_id($app_id,$path_pdf1)}

- test:
    name: 签署人传个人oid，签署主体传同一个oid-个人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: '', noticeType: '', redirectUrl: '', signPlatform: ''}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_linjue_tsign, initiatorAuthorizedAccountId: $oid_linjue_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_linjue_tsign", authorizedAccountId: "$oid_linjue_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 0, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: v3一步发起 多主体经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: '', noticeType: '', redirectUrl: '', signPlatform: ''}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_linjue_tsign, initiatorAuthorizedAccountId: $oid_linjue_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_linjue_tsign", authorizedAccountId: "$oid_linjue_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {
        "docs":  $docs,
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "v3一步发起 多主体经办人签",
          "flowConfigInfo": {
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": ""
            },
            "signConfig": {
              "redirectUrl": "",
              "signPlatform": ""
            }
          },
          "hashSign": false,
          "remark": "印章平台",
          "signValidity": *************
        },
        "signers": [
        {
          "signOrder": 1,
          "signerAccount": {
            "signerAccountId": "ebda3541171c44d4900447e857fae75a",
            "authorizedAccountId": "705789a4bc6545e7b9cdc4ebadafc177"
          },
          "signfields": [
          {
            "autoExecute": false,
            "actorIndentityType": 4,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "甲方",
              "posPage": "1",
              "posX": 200,
              "posY": 100
            },
            "signType": 1
          }
          ],
          "thirdOrderNo": "xyz"

        },
        {
          "signOrder": 1,
          "signerAccount": {
            "signerAccountId": "ebda3541171c44d4900447e857fae75a",
            "authorizedAccountId": "4b1027961b804dc58d13bd290b0a2cbc"
          },
          "signfields": [
          {
            "autoExecute": false,
            "actorIndentityType": 4,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 300,
              "posY": 100
            },
            "signType": 1
          }
          ],
          "thirdOrderNo": "xyz"
        }
        ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询签署区
    variables:
      - flowId: $sign_flowId
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId: content.data.signfields.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取流程列表
    variables:
      - json:
          {
            "signFlowStatus": [1,2],# 流程状态 必传 array int 1-签署中 2-已完成
            "signFlowStartTimeFrom": 1642665076000,    #签署流程发起时间开始 必传 long
            "signFlowStartTimeTo": 1650441076000, #签署流程发起时间结束 必传 long,
            "pageNum": 1, #页码 必传 int
            "pageSize": 10, # 每页数量 必传 int
            "signerInfos": [ #参与签署账号列表 非必填 array
            {
              "operatorId": "",    #签署账号  非必填
              "signerId": "",    # 签署主体账号  非必填
            }
            ]
          }
    api: api/signflow/batchSign0210/batchSignList.yml
    extract:
      - total: content.data.total
    validate:
      - eq: [status_code, 200]
      - gt: [ "content.data.total", 100 ]
      - contains: [ "content.message","成功" ]

- test:
    name:  获取批量签署url-报错
    variables:
      - json: {
        "operatorId": $oid_linjue_tsign,
        "redirectUrl": "",
        "signFlowIds": [
          $sign_flowId,$sign_flowId2,"c42352176a3f4015a122d020ae2e4d10","02d954d32d4543c0914f14d08187d2b3","71f29af130424867a933aef5e4598514","032bb28cf463450e80e393bf6cbadc07","5aa736bcff4f471e809c5c4d3752d695",
          "4d6355ec119146488ba6cbf0b8aa917a","79ec28aa7c2f4f838624fab2b9b3b685","2f7d7eb08707486589a49308e035c826","6802e05a293d4458864f25de5dd4a179"
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","超过10个"]


- test:
    name:  获取批量签署url
    variables:
      - json: {
        "operatorId": $oid_linjue_tsign,
        "redirectUrl": "",
        "signFlowIds": [
          $sign_flowId,$sign_flowId2,"02d954d32d4543c0914f14d08187d2b3","71f29af130424867a933aef5e4598514","032bb28cf463450e80e393bf6cbadc07","5aa736bcff4f471e809c5c4d3752d695",
          "4d6355ec119146488ba6cbf0b8aa917a","79ec28aa7c2f4f838624fab2b9b3b685","2f7d7eb08707486589a49308e035c826","6802e05a293d4458864f25de5dd4a179"
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
    teardown_hooks:
      - ${teardown_re_find($response)}
    extract:
      - batchSerialId: batchSerialId

- test:
    name: 获取批量签署分类列表
    variables:
      - batchSerialId: $batchSerialId
    api: api/signflow/batchSign0210/batchSortFlow.yml
    extract:
      - shortUrl: content.data.noSupportSigns
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.noSupportSigns.0.appId","**********"]


- test:
    name: 获取批量签中文档页数最小的文档信息及主体类型
    variables:
      - batchSerialId: $batchSerialId
    api: api/signflow/batchSign0210/getMinPage.yml
    extract:
      - shortUrl: content.data.actorIdentityTypes
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.filePage",43]
      - len_gt: ["content.data",0]


#未完成  设置签名域信息  需要修改参数 抓包修改
- test:
    name: 设置签名域信息
    variables:
      - batchSerialId: $batchSerialId
      - json:  {"signFieldBeans":[{"actorIdentityType":0,"posBean":{"addSignTime":false,"posPage":1,"posX":403.46713346593515,"posY":478.01298317628755,"qrcodeSign":false,"signDateBean":null,"signDateBeanType":0},"signType":1}]}
    api: api/signflow/batchSign0210/batchSignInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]



- test:
    name: 获取印章列表
    variables:
      - batchSerialId: $batchSerialId
    api: api/signflow/batchSign0210/batchGetSeals.yml
    extract:
      - shortUrl: content.data
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data",0]


- test:
    name: 提交签署
    variables:
      - batchSerialId: $batchSerialId
      - json: {"accountId":"ebda3541171c44d4900447e857fae75a","batchSerialId":$batchSerialId,"accountSealId":"b6c362fc-cc80-4783-b18a-6debfcc2273a","subjectSealBeans":[]}
    api: api/signflow/batchSign0210/batchSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.code",0]


- test:
    name: 获取签署结果
    variables:
      - batchSerialId: $batchSerialId
    api: api/signflow/batchSign0210/batchGetResult.yml
    #    extract:
    #      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data",0]




