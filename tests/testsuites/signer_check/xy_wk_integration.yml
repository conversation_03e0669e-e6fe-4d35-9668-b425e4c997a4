config:
  name: 轩辕悟空整合

testcases:

  一步发起流程:
    testcase: testcases/xy_wk_integration/createFlowOneStep.yml

  添加手动签名域:
    testcase: testcases/xy_wk_integration/handSign.yml

  查询/修改签名域:
    testcase: testcases/xy_wk_integration/signfield.yml

  分布发起流程:
    testcase: testcases/xy_wk_integration/signflows.yml

  一步发起新账号做实名认证:
    testcase: testcases/xy_wk_integration/一步发起新账号做实名认证.yml

  使用开发者账户发起0630:
    testcase: testcases/xy_wk_integration/使用开发者账户发起0630.yml

  悟空流程签署:
    testcase: testcases/xy_wk_integration/wk_add-Update-Execute.yml

  轩辕流程签署:
    testcase: testcases/xy_wk_integration/xy_add-Update-Execute.yml

  20220217迭代:
    testcase: testcases/iterate_cases/20220217.yml

  saas 3.0一期:
    testcase: testcases/saas_3.0/saas_3_0一期.yml

  saas 3.0二期:
    testcase: testcases/saas_3.0/saas_3_0二期.yml

  saas 3.0三期:
    testcase: testcases/saas_3.0/saas_3_0三期.yml

  api解约:
    testcase: testcases/合同解约/api解约.yml
