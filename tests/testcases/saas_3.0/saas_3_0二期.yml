- config:
    name: 通过文件创建签署流程
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_id: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - account_id_2_tsign: ${ENV(account_id2_in_tsign)}
      - account_id_3_tsign: ${ENV(account_id3_in_tsign)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}
      - file_id2: 8c5b4918a5a54a5191a5a0068160828b

- test:
    name: "查询关键字-仅逗号无法查询坐标"
    api: api/v3/file/fileId/keyword-positions.yml
    variables:
      - method: GET
      - fileId: $file_id
      - query: ?keywords=,,,
      - json: ""
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 未指定关键字]

- test:
    name: "查询关键字-正常查询，新增keywordPositions-GET"
    api: api/v3/file/fileId/keyword-positions.yml
    variables:
      - method: GET
      - fileId: $file_id
      - query: ?keywords=,其他项目,,
      - json: ""
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - len_gt: ["content.data", 0]
      - len_gt: ["content.data.keywordPositions", 0]
      - gt: ["content.data.keywordPositions.0.positions.0.coordinates.0.positionX", 0]
      - gt: ["content.data.keywordPositions.0.positions.0.coordinates.0.positionY", 0]

- test:
    name: "查询关键字-正常查询，新增keywordPositions-POST"
    api: api/v3/file/fileId/keyword-positions.yml
    variables:
      - method: POST
      - fileId: $file_id
      - query: ""
      - json: {"keywords":["","其他项目","",""]}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - len_gt: ["content.data", 0]
      - len_gt: ["content.data.keywordPositions", 0]
      - gt: ["content.data.keywordPositions.0.positions.0.coordinates.0.positionX", 0]
      - gt: ["content.data.keywordPositions.0.positions.0.coordinates.0.positionY", 0]

- test:
    name: "企业方发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"authConfig": {"audioVideoTemplateId": "1","orgAvailableAuthModes": ["ORG_BANK_TRANSFER"],"psnAvailableAuthModes": ["PSN_BANK4_AUTHCODE"], "willingnessAuthModes": ["VIDEO_WE_CHAT_VIDEO_DUAL"]},"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"$signFlowExpireTime","signFlowTitle":"签署1"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "异常：fileid与flowid不匹配获取预览文件下载链接"
    api: api/sign-flow/preview-file-download-url.yml
    variables:
          - app_ids: $app_id
          - signFlowId: $flowId
          - docFileId: $file_id2
    validate:
      - eq: [status_code, 200]
#      - eq: [content.code, 1435011]
      - contains: [content.message, "传入的fileId非当前流程中的签署文件"]

- test:
    name: "正常：获取预览文件下载链接"
    api: api/sign-flow/preview-file-download-url.yml
    variables:
          - app_ids: $app_id
          - signFlowId: $flowId
          - docFileId: $file_id
    validate:
      - eq: [status_code, 200]
#      - eq: [content.code, 1435011]
      - contains: [content.message, "成功"]
      - eq: [content.data.fileId, $file_id]
      - contains: [content.data.fileDownloadUrl, "https://esignoss"]

- test:
    name: "查询流程详情-可查询到企业发起人信息，无remarkImageFileKey，添加signFlowInitiator，添加signFlowConfig"
    api: api/sign-flow/detail.yml
    extract:
      - response: content.data
    variables:
      flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: ["${contain_keys($response,remarkImageFileKey)}", False]
      - eq: ["${contain_keys($response,handWrittenFileKey)}", False]
      - eq: [content.data.signFlowInitiator.orgInitiator.orgId, $account_id_sx_tsign]
      - eq: [content.data.signFlowInitiator.orgInitiator.transactor.psnId, $account_id_1_tsign]
      - eq: [content.data.signFlowConfig.chargeConfig.chargeMode, 0]
      - eq: [content.data.signFlowConfig.signFlowTitle, 签署1]
      - eq: [content.data.signFlowConfig.autoFinish, True]
      - contains: [content.data.signFlowConfig, signFlowExpireTime]
      - eq: [content.data.signFlowConfig.notifyUrl, $notifyUrl]
      - eq: [content.data.signFlowConfig.noticeConfig.noticeTypes, "1,2"]
      - eq: [content.data.signFlowConfig.signConfig.showBatchDropSealButton, True]
      - str_eq: [content.data.signFlowConfig.authConfig.psnAvailableAuthModes, "['PSN_BANK4_AUTHCODE']"]
      - str_eq: [content.data.signFlowConfig.authConfig.orgAvailableAuthModes, "['ORG_BANK_TRANSFER']"]
      - str_eq: [content.data.signFlowConfig.authConfig.willingnessAuthModes, "['VIDEO_WE_CHAT_VIDEO_DUAL']"]
      - eq: [content.data.signFlowConfig.authConfig.audioVideoTemplateId, "1"]

- test:
    name: "平台方发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":false,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(30)}","signFlowTitle":"签署${get_timestamp()}"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "查询流程详情-可查询到平台方信息"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.data.signFlowInitiator.orgInitiator.orgId, $account_id_dy_tsign]
      - eq: [content.data.signFlowInitiator.orgInitiator.transactor, null]
      - eq: [content.data.signFlowInitiator.psnInitiator, null]
      - eq: [content.data.signFlowConfig.chargeConfig.chargeMode, 0]

- test:
    name: "个人发起备注签"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"psnInitiator":{"psnId": $account_id_1_tsign}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","remarkSignFieldConfig":{"aiCheck": 0,"freeMode": false, "inputType": 1, "movableSignField": false, "remarkContent": "一二三","signFieldPosition": {"positionPage": "1", "positionX": 300, "positionY": 300}},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":1}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "查询流程详情-无remarkImageFileKey"
    api: api/sign-flow/detail.yml
    extract:
      - response: content.data
    variables:
      flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: ["${contain_keys($response,remarkImageFileKey)}", False]
      - eq: ["${contain_keys($response,handWrittenFileKey)}", False]

- test:
    name: "发起法人签"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"authConfig": {"audioVideoTemplateId": "1","orgAvailableAuthModes": ["ORG_BANK_TRANSFER"],"psnAvailableAuthModes": ["PSN_BANK4_AUTHCODE"], "willingnessAuthModes": ["VIDEO_WE_CHAT_VIDEO_DUAL"]},"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"$signFlowExpireTime","signFlowTitle":"签署1"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_dy_tsign}","transactorInfo": {"psnId":"$account_id_2_tsign"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":2}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "非自动开启"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"$file_id"}],"signFlowConfig":{"autoFinish":true,"autoStart":false,"signFlowExpireTime":"$signFlowExpireTime","signFlowTitle":"非自动开启"},"signers":[{"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signOrder":1,"signFields":[{"fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "未开启的流程可延期"
    api: api/RPC/updateFlowSignValidity.yml
    variables:
      json: {"flowId": "${flowId}","signValidity":$signFlowExpireTime}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, 执行成功]

- test:
    name: "自动开启"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"$file_id"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"signFlowExpireTime":"$signFlowExpireTime","signFlowTitle":"自动开启"},"signers":[{"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signOrder":1,"signFields":[{"fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "已开启的流程可延期"
    api: api/RPC/updateFlowSignValidity.yml
    variables:
      json: {"flowId": "${flowId}","signValidity":$signFlowExpireTime}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, 执行成功]

- test:
    name: "自动开启"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"$file_id"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"signFlowExpireTime":"$signFlowExpireTime","signFlowTitle":"自动开启"},"signers":[{"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signOrder":1,"signFields":[{"fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "RPC查看合同详情"
    api: api/RPC/queryFlowDetail/req.yml
    variables:
      - json: {"flowId": "$flowId"}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, 执行成功]

- test:
    name: "强制过期流程"
    api: api/RPC/expireFlow.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}
    variables:
      json: {"flowId": "${flowId}"}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, 执行成功]

- test:
    name: "已过期的流程可延期"
    api: api/RPC/updateFlowSignValidity.yml
    variables:
      json: {"flowId": "${flowId}","signValidity":$signFlowExpireTime}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, 执行成功]