- config:
    name: "悟空轩辕整合"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - app_Id1: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - xuanyuan_sixian: ${ENV(mobile_shield_wukong_appid_oid)}
      - xuanyuan_sixian_name: ${ENV(xuanyuan_sixian_name)}
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - fileId: ${ENV(fileId)}
      - oid_wang_tsign: ${ENV(oid_wang_tsign)}
      - phone_wang: ${ENV(phone_wang)}
      - mail_wang: ${ENV(mail_wang)}
      - none_tsign_account_id: ${ENV(none_tsign_account_id)}
      - accountId_withRealName_withoutAuth: ${ENV(accountId_withRealName_withoutAuth)}
      - fileId1: "3814bf8c025e4d9eb5d4b9090f7fbe4f"
      - fileId2: "f3c2b73eb020442dbf89edda5ce4a0a2"
      - fileId3: "453757d03c1d46e7bd2800bfb8a6549a"

- test:
    name: 一步发起流程，使用损坏和加密的文件
    variables:
      - attachments: []
      - doc1: { encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0 }
      - doc2: { encryption: 0,fileId: $fileId2, fileName: '文件1', source: 0 }
      - doc3: { encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0 }
      - docs: [ $doc1,$doc2,$doc3 ]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId1, sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 0, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: [content.message, 成功]

