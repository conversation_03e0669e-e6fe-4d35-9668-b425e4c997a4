- config:
    name: 指定印章平台绑定证书的印章id自动签署
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: "7876608918"
      - app_id: "7876608918"
      - personOid1: "05ee3d6b70de46a88ba9fa99db8d2e06"
      - path_pdf: data/个人借贷合同.pdf
      - sealId1: "c8fcfa1a-bd54-49d2-80d7-f137aad9a83c"  #有全部合同授权
      - sealId2: "173eef5e-543f-4169-afe1-c1f1660bb5f3"  #有人力资源合同授权
      - sealId3: "70682216-9ce4-4153-b8f5-30c45d3f3af4"  #没有授权
      - fileId1: ${get_file_id($app_id,$path_pdf)}
#平台自动签
- test:
    name: createFlowOneStep - 平台自动签 - 指定全部合同授权的印章Id
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc]
      - attachments: []
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 指定有授权的印章Id",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{"platformSign":true,"signOrder":1,"signfields":[{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":110},"sealId":$sealId1,"signType":1,"width":150},{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":210},"sealId":"","signType":1,"width":150}],"thirdOrderNo":777}]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.flowId

#平台自动签
- test:
    name: createFlowOneStep - 平台自动签 - 指定人力资源合同授权的印章Id
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc]
      - attachments: []
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 指定有授权的印章Id",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{"platformSign":true,"signOrder":1,"signfields":[{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":110},"sealId":$sealId2,"signType":1,"width":150},{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":210},"sealId":"","signType":1,"width":150}],"thirdOrderNo":777}]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId2: content.data.flowId

#平台自动签
- test:
    name: createFlowOneStep - 平台自动签 - 指定没有授权的印章Id签署
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc]
      - attachments: []
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 指定有授权的印章Id",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{"platformSign":true,"signOrder":1,"signfields":[{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":110},"sealId":$sealId3,"signType":1,"width":150},{"autoExecute":true,"actorIndentityType":2,"fileId":"$fileId1","posBean":{"posPage":1,"posX":110,"posY":210},"sealId":"","signType":1,"width":150}],"thirdOrderNo":777}]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437182]
      - eq: ["content.message", "未获取该企业有效的印章授权"]

#查询印章绑定的证书id
- test:
    name: 查询证书id1
    variables:
      - sealId: $sealId1
    api: api/seal-manager/getCertIdBySealId-api.yml
#    validate:
#      - contains: ["content.message", 成功]
#    extract:
#      - certId1: content.certId

