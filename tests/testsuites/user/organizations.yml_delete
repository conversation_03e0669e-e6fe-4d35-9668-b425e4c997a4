- config:
    name: 组织相关的测试
    variables:
        - name: "北京蚂蜂窝网络科技有限公司"
        - email: "<EMAIL>"
        - orgType: "CRED_ORG_USCC"
        - orgCode: "91110105669928206J"
        - legalName: "潘圣杰"
        - legalIdNo: "361001198505208479"
        - legalIdType: "CRED_PSN_CH_IDCARD"
        - mobile: "18759629653"
        - idNo: "513230198904164146"
        - idType: "CRED_PSN_CH_IDCARD"
        - person: "唐飞燕"
        - idNo_member: "******************"
        - person_merber: "齐熙泰"
    request:
        "base_url": ${ENV(footstone_api_url)}
        "headers":
        "Content-Type": "application/json"

#组织相关的测试放在footstone-user里面做了，这里去掉
#- test:
#    name: 账号操作，包含创建、更新、查询、注销等
#    testcase: testcases/user/organization_action.yml