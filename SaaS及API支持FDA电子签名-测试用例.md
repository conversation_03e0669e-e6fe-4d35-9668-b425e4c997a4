# SaaS及API支持FDA电子签名-测试用例

## 功能测试

### FDA签名配置管理

#### TL-合同偏好设置中开启FDA签名配置

##### PD-前置条件：用户已登录；具有合同偏好设置权限；

##### 步骤一：进入合同偏好设置页面

##### 步骤二：找到FDA签名配置选项，点击开启

##### 步骤三：配置默认签名样式和默认签署原因

##### 步骤四：保存配置

##### ER-预期结果：1：FDA签名配置成功开启；2：默认样式和签署原因保存成功；3：后续发起签署时可使用该配置；

#### TL-合同偏好设置中关闭FDA签名配置

##### PD-前置条件：用户已登录；FDA签名配置已开启；

##### 步骤一：进入合同偏好设置页面

##### 步骤二：找到FDA签名配置选项，点击关闭

##### 步骤三：确认关闭操作

##### ER-预期结果：1：FDA签名配置成功关闭；2：后续发起签署时不显示FDA选项；3：已配置的默认值被清空；

#### TL-FDA签名配置默认样式设置

##### PD-前置条件：用户已登录；FDA签名配置已开启；

##### 步骤一：在FDA签名配置中选择默认样式

##### 步骤二：配置签名者姓名显示格式

##### 步骤三：配置签名时间显示格式（包含时区）

##### 步骤四：配置签名含义显示方式

##### 步骤五：配置校验值显示选项

##### ER-预期结果：1：默认样式配置成功；2：各项显示格式符合FDA要求；3：配置可在发起签署时使用；

### 直接发起签署

#### TL-直接发起时启用FDA签名

##### PD-前置条件：用户已登录；具有发起签署权限；

##### 步骤一：进入直接发起签署页面

##### 步骤二：上传合同文件

##### 步骤三：添加签署方信息

##### 步骤四：开启FDA签名配置

##### 步骤五：配置FDA签名样式和签署原因

##### 步骤六：发起签署

##### ER-预期结果：1：签署流程成功发起；2：FDA签名配置生效；3：所有签署方均按FDA样式执行；4：签署页面显示签署理由选择；

#### TL-直接发起时不启用FDA签名

##### PD-前置条件：用户已登录；具有发起签署权限；

##### 步骤一：进入直接发起签署页面

##### 步骤二：上传合同文件

##### 步骤三：添加签署方信息

##### 步骤四：不开启FDA签名配置

##### 步骤五：发起签署

##### ER-预期结果：1：签署流程成功发起；2：使用普通签名样式；3：签署页面不显示签署理由选择；

#### TL-FDA签名样式配置验证

##### PD-前置条件：用户已登录；正在直接发起签署；FDA签名已启用；

##### 步骤一：配置签名者姓名为标准字体显示

##### 步骤二：设置签名含义为"同意"

##### 步骤三：选择时区为UTC+8

##### 步骤四：启用校验值显示

##### 步骤五：预览签名样式

##### ER-预期结果：1：签名样式预览正确显示；2：包含签名者姓名、签名含义、时区、校验值；3：字体为标准字体；4：符合FDA规范要求；

### 模板管理

#### TL-模板编辑时配置FDA签名

##### PD-前置条件：用户已登录；具有模板编辑权限；

##### 步骤一：进入模板编辑页面

##### 步骤二：在模板设置中开启FDA签名配置

##### 步骤三：配置FDA签名样式和签署原因

##### 步骤四：保存模板

##### ER-预期结果：1：模板保存成功；2：FDA签名配置已保存；3：使用该模板发起的流程均按FDA样式执行；

#### TL-模板发起时查看FDA签名配置

##### PD-前置条件：用户已登录；模板已配置FDA签名；

##### 步骤一：选择已配置FDA签名的模板

##### 步骤二：进入模板发起页面

##### 步骤三：查看FDA签名配置信息

##### ER-预期结果：1：可以查看到模板的FDA签名配置；2：显示签名样式和签署原因；3：配置信息准确无误；

### 签署页面交互

#### TL-FDA签名流程中选择签署理由

##### PD-前置条件：用户收到FDA签名流程的签署邀请；用户已登录；

##### 步骤一：进入签署页面

##### 步骤二：拖拽签名到指定位置

##### 步骤三：在弹出的签署理由选择框中选择理由

##### 步骤四：确认签署

##### ER-预期结果：1：签署理由选择框正常弹出；2：可以选择预设的签署理由；3：签署成功完成；4：签名样式符合FDA规范；

#### TL-FDA签名流程中自定义签署理由

##### PD-前置条件：用户收到FDA签名流程的签署邀请；用户已登录；

##### 步骤一：进入签署页面

##### 步骤二：拖拽签名到指定位置

##### 步骤三：在签署理由选择框中选择"自定义"

##### 步骤四：输入自定义签署理由

##### 步骤五：确认签署

##### ER-预期结果：1：可以输入自定义签署理由；2：自定义理由长度符合限制；3：签署成功完成；4：自定义理由正确显示在签名中；

### 签署区设置

#### TL-FDA签名启用后签署区样式变更

##### PD-前置条件：用户正在配置签署区；流程已启用FDA签名；

##### 步骤一：进入签署区配置页面

##### 步骤二：添加企业经办人章签署区

##### 步骤三：添加个人章签署区

##### 步骤四：查看签署区样式预览

##### ER-预期结果：1：签署区样式发生变更；2：仅支持手绘签署方式；3：签署区尺寸适配FDA签名样式；4：预览效果符合FDA规范；

## 接口测试

### V3API基于文件发起签署接口

#### TL-V3API发起签署时配置FDA签名

##### PD-前置条件：具有API调用权限；文件已上传；

##### 步骤一：调用基于文件发起签署接口

##### 步骤二：在请求参数中设置FDA签名配置为启用

##### 步骤三：配置FDA签名样式参数

##### 步骤四：配置签署理由参数

##### 步骤五：发送请求

##### ER-预期结果：1：接口调用成功；2：返回流程ID；3：流程启用FDA签名配置；4：签署页面按FDA样式显示；

#### TL-V3API发起签署时不配置FDA签名

##### PD-前置条件：具有API调用权限；文件已上传；

##### 步骤一：调用基于文件发起签署接口

##### 步骤二：在请求参数中不设置FDA签名配置

##### 步骤三：发送请求

##### ER-预期结果：1：接口调用成功；2：返回流程ID；3：流程使用普通签名样式；4：签署页面不显示签署理由选择；

### V3API追加删除签署区接口

#### TL-V3API追加签署区时FDA配置校验

##### PD-前置条件：具有API调用权限；原流程已启用FDA签名；

##### 步骤一：调用追加签署区接口

##### 步骤二：传入原流程ID

##### 步骤三：添加新的签署区信息

##### ER-预期结果：1：接口调用成功；2：新增签署区沿用原流程FDA配置；3：签署区样式符合FDA规范；

#### TL-V3API删除签署区时FDA配置保持

##### PD-前置条件：具有API调用权限；原流程已启用FDA签名；

##### 步骤一：调用删除签署区接口

##### 步骤二：传入要删除的签署区ID

##### 步骤三：执行删除操作

##### ER-预期结果：1：接口调用成功；2：签署区删除成功；3：剩余签署区FDA配置保持不变；

### V3API获取批量签链接接口

#### TL-V3API批量签包含FDA流程校验

##### PD-前置条件：具有API调用权限；存在多个签署流程；其中包含FDA签名流程；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：传入包含FDA签名流程的流程ID列表

##### 步骤三：检查接口返回结果

##### ER-预期结果：1：接口返回错误提示；2：提示信息说明不支持FDA流程批量签；3：返回具体的FDA流程ID；

#### TL-V3API批量签不包含FDA流程正常处理

##### PD-前置条件：具有API调用权限；存在多个普通签署流程；

##### 步骤一：调用获取批量签链接接口

##### 步骤二：传入普通签署流程ID列表

##### 步骤三：获取批量签链接

##### ER-预期结果：1：接口调用成功；2：返回批量签链接；3：可以正常进行批量签署；

## 兼容性测试

### 浏览器兼容性

#### TL-Chrome浏览器FDA签名功能验证

##### PD-前置条件：使用Chrome浏览器；用户已登录；

##### 步骤一：在Chrome浏览器中进入FDA签名配置页面

##### 步骤二：配置FDA签名样式

##### 步骤三：发起签署流程

##### 步骤四：完成签署操作

##### ER-预期结果：1：所有功能正常运行；2：页面显示正常；3：签名样式渲染正确；4：交互操作流畅；

#### TL-Safari浏览器FDA签名功能验证

##### PD-前置条件：使用Safari浏览器；用户已登录；

##### 步骤一：在Safari浏览器中进入FDA签名配置页面

##### 步骤二：配置FDA签名样式

##### 步骤三：发起签署流程

##### 步骤四：完成签署操作

##### ER-预期结果：1：所有功能正常运行；2：页面显示正常；3：签名样式渲染正确；4：交互操作流畅；

### 设备兼容性

#### TL-PC端FDA签名功能验证

##### PD-前置条件：使用PC设备；用户已登录；

##### 步骤一：在PC端访问FDA签名功能

##### 步骤二：配置和使用各项FDA签名功能

##### 步骤三：验证签名样式显示效果

##### ER-预期结果：1：所有功能正常运行；2：界面布局合理；3：操作体验良好；

#### TL-移动端FDA签名功能验证

##### PD-前置条件：使用移动设备；用户已登录；

##### 步骤一：在移动端访问FDA签名功能

##### 步骤二：配置和使用各项FDA签名功能

##### 步骤三：验证签名样式显示效果

##### ER-预期结果：1：功能适配移动端；2：界面响应式布局；3：触摸操作正常；

## 安全测试

### 权限控制测试

#### TL-无权限用户访问FDA配置

##### PD-前置条件：用户已登录；用户无FDA配置权限；

##### 步骤一：尝试访问FDA签名配置页面

##### 步骤二：尝试修改FDA签名配置

##### ER-预期结果：1：无法访问配置页面；2：显示权限不足提示；3：无法进行任何配置操作；

#### TL-普通用户查看FDA签名信息

##### PD-前置条件：普通用户已登录；存在FDA签名流程；

##### 步骤一：查看已完成的FDA签名合同

##### 步骤二：验证签名信息的完整性

##### ER-预期结果：1：可以查看签名信息；2：签名信息完整准确；3：无法修改签名信息；

### 数据完整性测试

#### TL-FDA签名数据防篡改验证

##### PD-前置条件：存在已完成的FDA签名合同；

##### 步骤一：获取签名的哈希值

##### 步骤二：验证签名数据完整性

##### 步骤三：检查时间戳信息

##### ER-预期结果：1：哈希值验证通过；2：签名数据未被篡改；3：时间戳信息准确；

## 性能测试

### 响应时间测试

#### TL-FDA签名配置页面加载性能

##### PD-前置条件：用户已登录；网络环境正常；

##### 步骤一：访问FDA签名配置页面

##### 步骤二：记录页面加载时间

##### 步骤三：测试配置保存响应时间

##### ER-预期结果：1：页面加载时间小于3秒；2：配置保存响应时间小于2秒；3：用户体验良好；

#### TL-FDA签名样式渲染性能

##### PD-前置条件：存在FDA签名流程；签署页面已加载；

##### 步骤一：拖拽签名到签署区

##### 步骤二：记录签名样式渲染时间

##### 步骤三：测试多个签名同时渲染性能

##### ER-预期结果：1：单个签名渲染时间小于1秒；2：多个签名渲染不影响页面性能；3：渲染效果流畅；

### 并发测试

#### TL-多用户同时配置FDA签名

##### PD-前置条件：多个用户账号；具有配置权限；

##### 步骤一：多个用户同时访问FDA配置页面

##### 步骤二：同时进行配置操作

##### 步骤三：验证配置结果

##### ER-预期结果：1：系统支持并发配置；2：各用户配置互不影响；3：配置结果正确保存；

## 异常测试

### 业务异常测试

#### TL-FDA签名配置数据异常处理

##### PD-前置条件：用户正在配置FDA签名；

##### 步骤一：输入异常的签署理由（超长文本）

##### 步骤二：尝试保存配置

##### ER-预期结果：1：系统提示输入内容超长；2：不允许保存异常配置；3：给出正确的输入提示；

#### TL-网络异常时FDA签名操作

##### PD-前置条件：用户正在进行FDA签名操作；

##### 步骤一：在操作过程中断网

##### 步骤二：尝试继续操作

##### 步骤三：网络恢复后重试

##### ER-预期结果：1：显示网络异常提示；2：操作状态得到保护；3：网络恢复后可继续操作；

### 系统异常测试

#### TL-FDA签名服务异常处理

##### PD-前置条件：FDA签名服务出现异常；

##### 步骤一：尝试配置FDA签名

##### 步骤二：尝试发起FDA签名流程

##### ER-预期结果：1：显示服务异常提示；2：提供降级方案；3：不影响普通签名功能；

### 边界值测试

#### TL-FDA签署理由字符长度边界测试

##### PD-前置条件：用户正在进行FDA签名；签署理由输入框已显示；

##### 步骤一：输入1个字符的签署理由

##### 步骤二：输入最大允许字符数的签署理由

##### 步骤三：输入超过最大字符数的签署理由

##### 步骤四：尝试提交签署

##### ER-预期结果：1：1个字符可以正常提交；2：最大字符数可以正常提交；3：超长字符提示错误并不允许提交；4：错误提示信息准确；

#### TL-FDA签名样式配置项边界测试

##### PD-前置条件：用户正在配置FDA签名样式；

##### 步骤一：测试签名者姓名最长字符限制

##### 步骤二：测试签名含义选项的完整性

##### 步骤三：测试时区选择的准确性

##### 步骤四：验证校验值显示的正确性

##### ER-预期结果：1：各配置项都有合理的边界限制；2：边界值处理正确；3：配置保存成功；4：预览效果符合预期；

### 数据验证测试

#### TL-FDA签名时间戳精确性验证

##### PD-前置条件：用户完成FDA签名；

##### 步骤一：记录签名操作的系统时间

##### 步骤二：查看签名中显示的时间戳

##### 步骤三：对比时间戳与实际操作时间

##### 步骤四：验证时区显示的准确性

##### ER-预期结果：1：时间戳精确到秒级；2：时间戳与实际操作时间一致；3：时区显示正确；4：符合FDA时间要求；

#### TL-FDA签名哈希值验证

##### PD-前置条件：存在已完成的FDA签名合同；

##### 步骤一：获取签名的哈希值

##### 步骤二：重新计算文档和签名的哈希值

##### 步骤三：对比两个哈希值

##### ER-预期结果：1：哈希值计算正确；2：两个哈希值完全一致；3：验证签名完整性；4：符合FDA安全要求；

### 国际化测试

#### TL-FDA签名中英文切换测试

##### PD-前置条件：系统支持中英文切换；用户已登录；

##### 步骤一：设置系统语言为中文

##### 步骤二：配置FDA签名并查看显示效果

##### 步骤三：切换系统语言为英文

##### 步骤四：查看FDA签名显示效果

##### ER-预期结果：1：中文环境下FDA签名显示正确；2：英文环境下FDA签名显示正确；3：语言切换不影响签名功能；4：签署理由支持对应语言；

#### TL-FDA签名时区国际化测试

##### PD-前置条件：用户在不同时区进行FDA签名；

##### 步骤一：在UTC+8时区完成签名

##### 步骤二：在UTC+0时区查看签名

##### 步骤三：在UTC-5时区查看签名

##### ER-预期结果：1：签名时间按签署时的时区记录；2：不同时区查看时显示一致；3：时区标识清晰准确；4：符合国际化要求；

### 用户体验测试

#### TL-FDA签名配置向导体验测试

##### PD-前置条件：新用户首次使用FDA签名功能；

##### 步骤一：进入FDA签名配置页面

##### 步骤二：查看配置向导或帮助信息

##### 步骤三：按照向导完成配置

##### ER-预期结果：1：提供清晰的配置向导；2：帮助信息详细易懂；3：配置过程简单流畅；4：新用户可以快速上手；

#### TL-FDA签名错误提示友好性测试

##### PD-前置条件：用户在使用FDA签名过程中出现各种错误；

##### 步骤一：故意输入错误的配置信息

##### 步骤二：尝试在不符合条件时使用FDA签名

##### 步骤三：查看系统错误提示信息

##### ER-预期结果：1：错误提示信息准确明确；2：提供解决方案建议；3：提示信息用户友好；4：不暴露系统内部信息；

## 冒烟测试用例

### MYTL-FDA签名基本配置功能

#### PD-前置条件：用户已登录；具有FDA配置权限；

#### 步骤一：进入合同偏好设置页面

#### 步骤二：开启FDA签名配置

#### 步骤三：设置默认签名样式

#### 步骤四：保存配置

#### ER-预期结果：1：FDA签名配置成功开启；2：默认样式保存成功；3：配置可在后续使用；

### MYTL-FDA签名发起流程

#### PD-前置条件：FDA签名配置已开启；用户具有发起权限；

#### 步骤一：进入直接发起页面

#### 步骤二：上传合同文件并添加签署方

#### 步骤三：启用FDA签名配置

#### 步骤四：发起签署

#### ER-预期结果：1：签署流程成功发起；2：FDA配置生效；3：签署方收到邀请；

### MYTL-FDA签名签署流程

#### PD-前置条件：收到FDA签名流程邀请；用户已登录；

#### 步骤一：进入签署页面

#### 步骤二：拖拽签名到指定位置

#### 步骤三：选择签署理由

#### 步骤四：确认签署

#### ER-预期结果：1：签署理由选择正常；2：签署成功完成；3：签名样式符合FDA规范；

### MYTL-FDA签名API接口基本功能

#### PD-前置条件：具有API调用权限；文件已准备；

#### 步骤一：调用基于文件发起签署接口

#### 步骤二：设置FDA签名配置参数

#### 步骤三：发送请求

#### ER-预期结果：1：接口调用成功；2：返回流程ID；3：FDA配置生效；

### MYTL-FDA签名样式显示验证

#### PD-前置条件：存在已完成的FDA签名合同；

#### 步骤一：打开已签署的合同

#### 步骤二：查看FDA签名样式

#### 步骤三：验证签名信息完整性

#### ER-预期结果：1：签名样式显示正确；2：包含所有必要信息；3：符合FDA规范；

### MYTL-FDA签名权限控制验证

#### PD-前置条件：存在不同权限级别的用户；

#### 步骤一：使用无权限用户尝试访问FDA配置

#### 步骤二：使用有权限用户正常配置

#### 步骤三：验证权限控制效果

#### ER-预期结果：1：权限控制生效；2：无权限用户被拒绝访问；3：有权限用户正常使用；

### MYTL-FDA签名模板配置功能

#### PD-前置条件：用户已登录；具有模板编辑权限；

#### 步骤一：进入模板编辑页面

#### 步骤二：开启FDA签名配置

#### 步骤三：设置签名样式和签署理由

#### 步骤四：保存模板

#### ER-预期结果：1：模板FDA配置保存成功；2：使用模板发起时FDA配置生效；3：模板列表显示FDA标识；

### MYTL-FDA签名签署区设置

#### PD-前置条件：用户正在配置签署区；流程已启用FDA签名；

#### 步骤一：添加个人签署区

#### 步骤二：查看签署区样式变更

#### 步骤三：验证仅支持手绘签署

#### ER-预期结果：1：签署区样式符合FDA要求；2：仅显示手绘签署选项；3：签署区尺寸适配FDA样式；

### MYTL-FDA签名批量操作限制

#### PD-前置条件：存在FDA签名流程和普通流程；

#### 步骤一：尝试将FDA流程加入批量签署

#### 步骤二：查看系统提示

#### 步骤三：验证普通流程批量操作正常

#### ER-预期结果：1：FDA流程不能批量签署；2：系统显示限制提示；3：普通流程批量操作不受影响；

### MYTL-FDA签名数据完整性验证

#### PD-前置条件：存在已完成的FDA签名合同；

#### 步骤一：查看签名的哈希值

#### 步骤二：验证签名时间戳

#### 步骤三：检查签名信息完整性

#### ER-预期结果：1：哈希值验证通过；2：时间戳准确到秒级；3：签名信息完整不可篡改；

### MYTL-FDA签名浏览器兼容性

#### PD-前置条件：用户使用主流浏览器；

#### 步骤一：在Chrome浏览器中使用FDA签名功能

#### 步骤二：在Safari浏览器中使用FDA签名功能

#### 步骤三：对比功能表现

#### ER-预期结果：1：主流浏览器都支持FDA功能；2：功能表现一致；3：用户体验良好；

### MYTL-FDA签名非epass企业提示

#### PD-前置条件：企业账号非epass认证；

#### 步骤一：尝试开启FDA签名配置

#### 步骤二：查看系统提示信息

#### ER-预期结果：1：系统显示需要epass认证提示；2：提供认证指引；3：不允许开启FDA配置；

## 线上验证用例

### PATL-FDA签名端到端流程验证

#### PD-前置条件：生产环境已部署；用户账号已准备；

#### 步骤一：完整配置FDA签名功能

#### 步骤二：发起包含多个签署方的FDA签名流程

#### 步骤三：所有签署方完成签署

#### 步骤四：验证最终合同的FDA签名效果

#### ER-预期结果：1：整个流程顺利完成；2：所有签名符合FDA规范；3：合同具有法律效力；4：审计日志完整；

### PATL-FDA签名API接口生产验证

#### PD-前置条件：生产环境API已部署；客户系统已集成；

#### 步骤一：客户系统调用FDA签名相关API

#### 步骤二：验证API响应和功能

#### 步骤三：完成完整的签署流程

#### ER-预期结果：1：API接口稳定可用；2：功能符合预期；3：性能满足要求；4：与客户系统集成正常；

### PATL-FDA签名合规性验证

#### PD-前置条件：FDA签名功能已上线；合规检查清单已准备；

#### 步骤一：按照FDA 21 CFR Part 11要求检查签名样式

#### 步骤二：验证审计日志的完整性和准确性

#### 步骤三：检查数据完整性和安全性

#### 步骤四：验证用户身份认证和权限控制

#### ER-预期结果：1：签名样式完全符合FDA要求；2：审计日志满足合规要求；3：数据安全性达标；4：通过合规性检查；

### PATL-FDA签名性能稳定性验证

#### PD-前置条件：生产环境正常运行；监控系统已部署；

#### 步骤一：在正常业务负载下使用FDA签名功能

#### 步骤二：监控系统性能指标

#### 步骤三：验证功能稳定性

#### ER-预期结果：1：系统性能稳定；2：响应时间符合要求；3：无功能异常；4：用户体验良好；

### PATL-FDA签名多浏览器兼容性验证

#### PD-前置条件：生产环境已部署；多种浏览器环境已准备；

#### 步骤一：在Chrome浏览器中完整使用FDA签名功能

#### 步骤二：在Safari浏览器中完整使用FDA签名功能

#### 步骤三：在Firefox浏览器中完整使用FDA签名功能

#### 步骤四：对比各浏览器的功能表现

#### ER-预期结果：1：所有主流浏览器都支持FDA签名功能；2：功能表现一致；3：用户体验良好；4：无兼容性问题；

## 回归测试

### 原有功能兼容性

#### TL-普通签名功能不受影响

##### PD-前置条件：系统已部署FDA签名功能；

##### 步骤一：使用原有普通签名功能

##### 步骤二：验证签名流程完整性

##### 步骤三：检查签名样式和功能

##### ER-预期结果：1：普通签名功能正常；2：签名流程无变化；3：签名样式保持原样；

#### TL-现有API接口向后兼容

##### PD-前置条件：系统已部署FDA签名功能；

##### 步骤一：使用原有API接口发起签署

##### 步骤二：验证接口返回结果

##### 步骤三：检查签署流程

##### ER-预期结果：1：原有API接口正常工作；2：返回结果格式不变；3：签署流程保持兼容；

### 补充遗漏场景测试

#### TL-非epass企业使用FDA签名提示

##### PD-前置条件：企业账号非epass认证；用户尝试使用FDA签名；

##### 步骤一：进入FDA签名配置页面

##### 步骤二：尝试开启FDA签名配置

##### 步骤三：查看系统提示信息

##### ER-预期结果：1：系统显示提示信息；2：说明需要epass企业认证；3：提供认证指引链接；4：不允许开启FDA配置；

#### TL-模板时间精确到秒级显示

##### PD-前置条件：用户已创建模板；模板已配置FDA签名；

##### 步骤一：进入模板列表页面

##### 步骤二：查看模板的创建时间和修改时间

##### 步骤三：编辑模板并保存

##### 步骤四：再次查看模板时间信息

##### ER-预期结果：1：模板时间显示精确到秒级；2：创建时间和修改时间都显示秒级；3：时间格式符合CFR要求；4：时间更新准确；

#### TL-大文件FDA签名性能测试

##### PD-前置条件：准备大于50MB的PDF文件；用户已登录；

##### 步骤一：上传大文件并配置FDA签名

##### 步骤二：发起签署流程

##### 步骤三：完成签署操作

##### 步骤四：记录各环节耗时

##### ER-预期结果：1：大文件上传成功；2：FDA签名配置正常；3：签署完成时间在可接受范围内；4：签名样式渲染正常；

#### TL-FDA签名合同解约流程

##### PD-前置条件：存在已完成的FDA签名合同；用户具有解约权限；

##### 步骤一：选择要解约的FDA签名合同

##### 步骤二：发起合同解约流程

##### 步骤三：验证解约流程是否沿用FDA配置

##### 步骤四：完成解约签署

##### ER-预期结果：1：解约流程成功发起；2：沿用原合同FDA配置；3：解约签署符合FDA规范；4：解约记录完整；

#### TL-FDA签名合同续签流程

##### PD-前置条件：存在已完成的FDA签名合同；合同支持续签；

##### 步骤一：选择要续签的FDA签名合同

##### 步骤二：发起合同续签流程

##### 步骤三：验证续签流程是否沿用FDA配置

##### 步骤四：完成续签签署

##### ER-预期结果：1：续签流程成功发起；2：沿用原合同FDA配置；3：续签签署符合FDA规范；4：续签记录完整；

#### TL-FDA签名流程重新发起

##### PD-前置条件：存在已完成的FDA签名合同；用户具有重新发起权限；

##### 步骤一：选择要重新发起的FDA签名合同

##### 步骤二：点击重新发起功能

##### 步骤三：验证重新发起流程是否沿用FDA配置

##### 步骤四：完成重新发起的签署

##### ER-预期结果：1：重新发起流程成功；2：沿用原合同FDA配置；3：新流程签署符合FDA规范；4：流程记录完整；

#### TL-FDA签名多语言签署理由测试

##### PD-前置条件：系统支持多语言；用户在不同语言环境下使用FDA签名；

##### 步骤一：在中文环境下配置FDA签署理由

##### 步骤二：切换到英文环境查看签署理由

##### 步骤三：在英文环境下进行签署

##### 步骤四：验证签署理由的语言显示

##### ER-预期结果：1：签署理由支持多语言；2：语言切换后显示对应语言；3：签署时理由显示正确；4：最终签名中理由语言准确；

#### TL-FDA签名批量操作限制测试

##### PD-前置条件：存在多个签署流程；其中包含FDA签名流程；

##### 步骤一：尝试将FDA签名流程加入批量签署

##### 步骤二：尝试对FDA签名流程进行批量操作

##### 步骤三：验证系统限制提示

##### ER-预期结果：1：系统阻止FDA流程批量操作；2：显示明确的限制提示；3：提示信息说明FDA签名需单独处理；4：不影响其他流程的批量操作；

#### TL-FDA签名审计日志完整性测试

##### PD-前置条件：存在完整的FDA签名流程；用户具有审计日志查看权限；

##### 步骤一：查看FDA签名流程的审计日志

##### 步骤二：验证日志记录的完整性

##### 步骤三：检查关键操作的日志记录

##### 步骤四：验证日志的时间戳准确性

##### ER-预期结果：1：审计日志记录完整；2：包含所有关键操作；3：时间戳精确到秒级；4：日志不可篡改；5：符合FDA审计要求；

#### TL-FDA签名证书有效性验证

##### PD-前置条件：存在已完成的FDA签名合同；

##### 步骤一：获取签名中的数字证书信息

##### 步骤二：验证证书的有效性

##### 步骤三：检查证书链的完整性

##### 步骤四：验证证书的时间有效性

##### ER-预期结果：1：数字证书有效；2：证书链完整；3：证书在有效期内；4：证书符合FDA要求；

#### TL-FDA签名跨时区一致性测试

##### PD-前置条件：用户在不同时区进行FDA签名操作；

##### 步骤一：在UTC+8时区配置FDA签名

##### 步骤二：在UTC+0时区查看配置

##### 步骤三：在UTC-5时区进行签署

##### 步骤四：在不同时区查看最终签名

##### ER-预期结果：1：配置在不同时区显示一致；2：签署时间按实际时区记录；3：时区标识清晰准确；4：全球用户查看一致；

#### TL-FDA签名系统升级兼容性测试

##### PD-前置条件：系统准备升级；存在历史FDA签名数据；

##### 步骤一：记录升级前的FDA签名数据

##### 步骤二：执行系统升级

##### 步骤三：验证升级后FDA签名功能

##### 步骤四：检查历史数据的完整性

##### ER-预期结果：1：系统升级成功；2：FDA签名功能正常；3：历史数据完整无损；4：新旧版本兼容；

#### TL-FDA签名并发签署压力测试

##### PD-前置条件：准备多个FDA签名流程；多个用户账号；

##### 步骤一：多个用户同时进行FDA签署

##### 步骤二：监控系统性能指标

##### 步骤三：验证签署结果的准确性

##### 步骤四：检查签名样式的一致性

##### ER-预期结果：1：系统支持并发签署；2：性能指标正常；3：所有签署结果准确；4：签名样式一致；

#### TL-FDA签名移动端适配测试

##### PD-前置条件：用户使用移动设备；FDA签名流程已发起；

##### 步骤一：在移动端打开签署页面

##### 步骤二：查看FDA签名样式显示效果

##### 步骤三：进行签署操作

##### 步骤四：验证签署完成后的显示

##### ER-预期结果：1：移动端显示适配良好；2：FDA签名样式清晰可读；3：签署操作流畅；4：功能完整可用；

#### TL-FDA签名数据导出功能测试

##### PD-前置条件：存在多个已完成的FDA签名合同；用户具有导出权限；

##### 步骤一：选择要导出的FDA签名合同

##### 步骤二：执行合同导出操作

##### 步骤三：验证导出文件的完整性

##### 步骤四：检查FDA签名信息的保留

##### ER-预期结果：1：合同导出成功；2：FDA签名信息完整保留；3：导出格式符合要求；4：可用于合规审计；
