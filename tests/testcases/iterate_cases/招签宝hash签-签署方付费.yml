- config:
    name: "招签宝项目-hash签署-签署方付费  "
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: "**********"
      #        -   fileId1: ${get_file_id($app_id,$path_pdf1)}
      - signer_stan: "3986d7544d9848f186ee9c5dcd342de1"
      - sixian_stan: "3986d7544d9848f186ee9c5dcd342de1"
      - resourceShareId:
      - caogu_accountId: "3986d7544d9848f186ee9c5dcd342de1"
      - accountId_gan: "3986d7544d9848f186ee9c5dcd342de1"
      - yuzan_accountId: "3986d7544d9848f186ee9c5dcd342de1"
      - gan_sanfang: "3986d7544d9848f186ee9c5dcd342de1"
      - sixian_organize_id: "bb4364e663ad43ee9381e65232b5ae1f"
      - fileId1: ${ENV(file_id_in_tengqing_PaaS_id_without_real_with_willingness)}

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - orgId: $sixian_stan
      - alias: 印章2
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - accountId: $signer_stan
      - color: "RED"
      - height: 200
      - alias: "印章"
      - type: "SQUARE"
      - width: 200
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: "获取对应的实名组织详情-2"
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - orgId: $sixian_stan
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: content.data.seals.0.sealId

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - accountId:  $signer_stan
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 一步发起指定证书id用户手动签-成功
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "福昕项目",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $gan_sanfang,
            "authorizedAccountId": $gan_sanfang
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 0,
            "signerRoleType": "0",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 100,
                "posY": 100
              },
            "signDateBeanType": "0",
            "signDateBean": { "fontSize": 100,"posX": 120,"posY": 150 },
            "signType": 1

          }
          ],
        "thirdOrderNo": "个人"
      },
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $gan_sanfang,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      }


      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $gan_sanfang
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
- test:
    name: 查询签署区
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200] #content.data.signfields.0.signerAccountId
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId
      - signfieldId1: content.data.signfields.1.signfieldId


- test:
    name: "查询详情，主要查询processId"
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - operatorid: $yuzan_accountId
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
    extract:
      - processId: content.data.processId
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}


- test:
    name: 意愿认证-短信验证码发送
    variables:
      - accountId: $signer_stan
      - bizId:
      - bizType:  "SIGN"
      - sendType: "SMS"
      - bizCtxIds: [$sign_flow_one]
    api: api/realname/realname/willCreateCodeAuth.yml
    extract:
      - willAuthId2: content.data.willAuthId
      - bizId: content.data.bizId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authCode: 123456
      - accountId: $signer_stan
      - willAuthId: $willAuthId2
      - bizId:
      - bizType:  "SIGN"
    api: api/realname/realname/checkVerCodeModel.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 招签宝hash签署-个人签-不扣钱
    variables:
      - json:  {"needBilling":false,"billingBizId": $thirdPartyUserId_person_ckl, "hashBase64":"nqrF18Z5c7Xn4X5LU+jXZct6jSNue0ZxZRKr+esCNq8=","operatorId":"3986d7544d9848f186ee9c5dcd342de1","signerId":"3986d7544d9848f186ee9c5dcd342de1","willingnessBizId": $bizId }
    api: api/iterate_cases/hashSign_zqb.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 招签宝hash签署-企业签-扣钱
    variables:
      - json:  {"needBilling":true, "billingBizId": $thirdPartyUserId_person_ckl, "hashBase64":"nqrF18Z5c7Xn4X5LU+jXZct6jSNue0ZxZRKr+esCNq8=","operatorId":"3986d7544d9848f186ee9c5dcd342de1","signerId":$sixian_organize_id,"willingnessBizId": $bizId }
    api: api/iterate_cases/hashSign_zqb.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 招签宝hash签署-账号不存在
    variables:
      - json:  {"needBilling":true,"billingBizId": $thirdPartyUserId_person_ckl,"hashBase64":"nqrF18Z5c7Xn4X5LU+jXZct6jSNue0ZxZRKr+esCNq8=","operatorId":"3986d7544d9848f186ee9c5dcd342de1","signerId":"3986d7544d9848f186ee9c5dcd342de","willingnessBizId": $bizId }
    api: api/iterate_cases/hashSign_zqb.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "账号不存在"]
      - eq: ["content.code",1435203]

- test:
    name: 招签宝hash签署-意愿认证失败
    variables:
      - json:  {"billingBizId": $thirdPartyUserId_person_ckl,"hashBase64":"nqrF18Z5c7Xn4X5LU+jXZct6jSNue0ZxZRKr+esCNq8=","operatorId":"3986d7544d9848f186ee9c5dcd342de1","signerId":"3986d7544d9848f186ee9c5dcd342de1","willingnessBizId": "will_ec1c138cb1fd4c8a9e0bf88160c8ee2" }
    api: api/iterate_cases/hashSign_zqb.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "意愿认证失败"]
      - eq: ["content.code",1436202]
