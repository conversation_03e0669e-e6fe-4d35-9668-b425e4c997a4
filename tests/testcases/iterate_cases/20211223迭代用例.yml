- config:
    name: "创建1223用例可以签署中下载"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf1: data/损坏的pdf.pdf
      - path_pdf2: data/加密.pdf
      - path_pdf3: data/劳动合同书.pdf
      - group: ''
      - app_id: ${ENV(gan_appid_download)}
      - fileId1: "3814bf8c025e4d9eb5d4b9090f7fbe4f"
      - fileId2: "f3c2b73eb020442dbf89edda5ce4a0a2"
      - fileId3: "453757d03c1d46e7bd2800bfb8a6549a"
      - imagfileId: "5d134d1a5033458293a122bded604950"

- test:
    name: "创建签署人账号 - gan"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: A1112021121518291111166
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建签署人第二个账号 gan"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: A11120211215182911111
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan2: content.data.accountId
      - yuzan_accountId2: content.data.accountId

- test:
    name: "创建签署人账号 - gan第三个账号"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan_3: content.data.accountId
      - yuzan_accountId_3: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "使用caogu_accountId创建机构"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建机构-东营伟信建筑安装工程有限公司"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: dongying201211
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId

- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: "8fbe5926547e40149978fb8c5a448394"
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - accountId: $yuzan_accountId
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


#手动将数据取出  用固定的数据
- test:
    name: 一步发起 使用加密的fileid  同一gid对应的两个oid  分别对应两个主体
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0}
      - attachments: [{"attachmentName":"附件","fileId":$imagfileId}]
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId3,
            "sealType": "",
            "posBean":
              {
                #                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": 1,
            "signType": 0

          }
          ],
        "thirdOrderNo": "企业2"
      },
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId2,
            "authorizedAccountId": $dongying_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId3,
            "sealType": "",
            "posBean":
              {
                #                  "posPage": "1",
                "posX": 600,
                "posY": 600
              },
            "signDateBeanType": 1,
            "signType": 0

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 查询签署区
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200] #content.data.signfields.0.signerAccountId
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId
      - signfieldId1: content.data.signfields.0.signfieldId

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行签署-报错-两个无权限印章-泗县d92dfecb-0d52-47ee-9834-aba36deaee97-东营458034b9-9b8b-4e12-bae9-4d4780bba3fb
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - accountId: "091bc7d9666146a2b44f699620623a48"
      - sealId: $p1_sealId
      - addSignfield0:  {signfieldId: $signfieldId0,"fileId":$fileId3,"signerAccountId":"091bc7d9666146a2b44f699620623a48","actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "第一个用户手动签，第一个用户手动签"},"sealType":"","sealId":"d92dfecb-0d52-47ee-9834-aba36deaee97","signType":1}
      - addSignfield1: {signfieldId: $signfieldId1,"fileId":$fileId3,"signerAccountId":"091bc7d9666146a2b44f699620623a48","actorIndentityType":"2",
                        "authorizedAccountId":$dongying_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "第一个用户手动签，第一个用户手动签"},"sealType":"","sealId":"458034b9-9b8b-4e12-bae9-4d4780bba3fb","signType":1}
      - addSignfields: []
      - updateSignfields: [$addSignfield0,$addSignfield1]
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: []
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.message","成功"]


- test:
    name: 再次查看 还是两个签署区
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId
      - signfieldId1: content.data.signfields.1.signfieldId
      - signer0: content.data.signfields.0.signerAccountId
      - signer1: content.data.signfields.1.signerAccountId
    validate:
      - eq: [status_code, 200]
      - eq: [ "content.data.signfields.0.signerAccountId", "d76365e805d64381b65350a86bf448ba" ]
      - eq: [ "content.data.signfields.1.signerAccountId", "1597b980b4744d94b3526c924b5fb6a6" ]






- test:
    name: 一步发起 使用加密的fileid  同一gid对应的两个oid  分别对应两个主体
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0}
      - attachments: [{"attachmentName":"附件","fileId":$imagfileId}]
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId3,
            "sealType": "",
            "posBean":
              {
                #                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": 1,
            "signType": 0

          }
          ],
        "thirdOrderNo": "企业2"
      },
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId2,
            "authorizedAccountId": $dongying_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId3,
            "sealType": "",
            "posBean":
              {
                #                  "posPage": "1",
                "posX": 600,
                "posY": 600
              },
            "signDateBeanType": 1,
            "signType": 0

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

#手动将数据取出  用固定的数据
- test:
    name: 第二次发起  一步发起   两个gid对应的两个oid  分别对应两个主体
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0}
      - attachments: [{"attachmentName":"附件","fileId":$imagfileId}]
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId_3,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId3,
            "sealType": "",
            "posBean":
              {
                #                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": 1,
            "signType": 0

          }
          ],
        "thirdOrderNo": "企业2"
      },
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId2,
            "authorizedAccountId": $dongying_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId3,
            "sealType": "",
            "posBean":
              {
                #                  "posPage": "1",
                "posX": 600,
                "posY": 600
              },
            "signDateBeanType": 1,
            "signType": 0

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 查询签署区
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200] #content.data.signfields.0.signerAccountId
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId
      - signfieldId1: content.data.signfields.0.signfieldId

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行签署-报错-两个无权限印章-泗县d92dfecb-0d52-47ee-9834-aba36deaee97-东营458034b9-9b8b-4e12-bae9-4d4780bba3fb
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - accountId: "091bc7d9666146a2b44f699620623a48"
      - sealId: $p1_sealId
      - addSignfield0:  {signfieldId: $signfieldId0,"fileId":$fileId3,"signerAccountId":"091bc7d9666146a2b44f699620623a48","actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "第一个用户手动签，第一个用户手动签"},"sealType":"","sealId":"d92dfecb-0d52-47ee-9834-aba36deaee97","signType":1}
      - addSignfield1: {signfieldId: $signfieldId1,"fileId":$fileId3,"signerAccountId":"3986d7544d9848f186ee9c5dcd342de1","actorIndentityType":"2",
                        "authorizedAccountId":$dongying_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "第一个用户手动签，第一个用户手动签"},"sealType":"","sealId":"458034b9-9b8b-4e12-bae9-4d4780bba3fb","signType":1}
      - addSignfields: []
      - updateSignfields: [$addSignfield0,$addSignfield1]
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: []
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437157]
      - eq: ["content.message","签署人信息校验失败"]


- test:
    name: 再次查看 还是两个签署区
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId
      - signfieldId1: content.data.signfields.1.signfieldId
      - signer0: content.data.signfields.0.signerAccountId
      - signer1: content.data.signfields.1.signerAccountId
    validate:
      - eq: [status_code, 200]
      - eq: [ "content.data.signfields.0.signerAccountId", "135c24fca90e470e822c0cb4ab8b58a8" ]
      - eq: [ "content.data.signfields.1.signerAccountId", "d76365e805d64381b65350a86bf448ba" ]


#非标合规二期新增case
- test:
    name: "/api/v2/signflows/createFlowOneStep 一步发起 发起个人静默授权失败（是静默授权不是静默签署）"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0}
      - doc1: {encryption: 0,fileId: $fileId2, fileName: '文件1', source: 0}
      - doc2: {encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0}
      - attachments: [{"attachmentName":"附件","fileId":$imagfileId}]
      - docs: [$doc,$doc1,$doc2]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
         "agreeAutoSign": true,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $yuzan_accountId
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 0,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",

            "signDateBeanType": 1,
            "signType": 0

          }

          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 当前应用不支持授权静默签，请联系交付经理开通']



- test:
    name: "/api/v2/signflows/createFlowOneStep 一步发起 发起个人授权免意愿失败"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0}
      - doc1: {encryption: 0,fileId: $fileId2, fileName: '文件1', source: 0}
      - doc2: {encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0}
      - attachments: [{"attachmentName":"附件","fileId":$imagfileId}]
      - docs: [$doc,$doc1,$doc2]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "agreeSkipWillingness": true,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $yuzan_accountId
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 0,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",

            "signDateBeanType": 1,
            "signType": 0

          }

          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 当前应用不支持快捷签署，请联系交付经理开通']



- test:
    name: "/api/v2/signflows/createFlowOneStep 一步发起 使用正常+损坏+加密的pdf"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0}
      - doc1: {encryption: 0,fileId: $fileId2, fileName: '文件1', source: 0}
      - doc2: {encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0}
      - attachments: [{"attachmentName":"附件","fileId":$imagfileId}]
      - docs: [$doc,$doc1,$doc2]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",

            "signDateBeanType": 1,
            "signType": 0

          },
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",

            "signDateBeanType": 1,
            "signType": 0

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: "createAllAtOnce一步到位创建流程-对内-添加文档list"
    variables:
      - attachment1: {fileId: $imagfileId, attachmentName: "附件2.pdf"}
      - doc1: { encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0 }
      - doc2: { encryption: 0,fileId: $fileId2, fileName: '文件1', source: 0 }
      - doc3: { encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0 }
      - json: {"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[$attachment1],"docs":[$doc1,$doc2,$doc3],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$yuzan_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":0,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId2: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]





- test:
    name: "创建签署流程-分步流程"
    variables:
      - autoArchive: true
      - businessScene: 悟空非实名签校验add-update字段
      - configInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', signPlatform: '',redirectUrl: "http://www.baidu.com"}
      # redirectUrl: "http://www.baidu.com",
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $yuzan_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flow_one: content.data.flowId
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


- test:
    name: "添加1份流程文档-分步流程"
    variables:
      - doc1: { encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0 }
      - doc2: { encryption: 0,fileId: $fileId2, fileName: '文件1', source: 0 }
      - doc3: { encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0 }
      - docs: [$doc1,$doc2,$doc3]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "个人-添加成功个人签名域-指定签名域 可以添加位置信息-分步流程"
    variables:
      - flowId: $sign_flowId
      - signfields: [
      { "signDateBeanType": "2","posBean":{"addSignTime":false,"location": "创建第一个手动签署区创建第一个手动签署区"},"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"sealType":null,"sealId":"","signType":0}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]


- test:
    name: "开启签署流程-分步流程"
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]





