- config:
    name: "0611接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''

- test:
    name: "校验下载签署凭证接口"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id3)}
      - operator_id: "1111111111"
      - flowId: ${ENV(flowId_voucher)}
    api: api/iterate_cases/voucher.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.basicInfo.verifyUrl", "https://verify.antchain.antgroup.com/home/<USER>"]
      - eq: ["content.data.basicInfo.verifyGuideUrl", "https://qianxiaoxia.yuque.com/docs/share/5d851df1-b08a-46f6-8220-ef0d49f0c87e?#"]
      - len_gt: ["content.data.detail.flowDocs.0.evidenceId", 0]

- test:
    name: "根据流程id获取审批链接"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id3)}
      - operator_id: ${ENV(operator_id)}
      - flowId: ${ENV(flowId_approval)}
      - redirectUrl: https://www.baidu.com
      - batchId: ${ENV(batchId)}
    api: api/iterate_cases/getApprovalflowUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.url", 0]

