#def: update_third_user_id($userId, $userType, $email, $mobile, $name, $cardNo)
request:
  url: ${ENV(footstone_user_url)}/v1/accounts/updateByThirdId?thirdPartyUserId=$userId&thirdPartyUserType=$userType
  method: POST
  headers: ${get_headers($app_id)}
  json:
    {
      "cardNo": $cardNo,
      "email": $email,
      "mobile": $mobile,
      "name": $name
    }
validate:
  - eq: [status_code, 200]
#指定appid创建账号
