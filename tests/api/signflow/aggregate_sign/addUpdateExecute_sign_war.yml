#def: addUpdateExecute_sign($flowId,$addSignfields,$updateSignfields,$signfieldIds,$approvalPolicy,$dingUser,$accountId)
request:
  url: ${ENV(item_url)}/v1/signflows/$flowId/signfields/add-Update-Execute
  method: PUT
  headers: { "Content-Type": "application/json", "X-Tsign-Open-App-Id": $app_id,
             "filter-result": "true", "isCredible": "true",
             "X-Tsign-Open-Auth-Mode": "simple", 'X-Tsign-Client-Id': 'OPEN_SERVICE_HYBRID',
             "X-Tsign-Service-Group": $group }
  json:
    accountId: $accountId
    addSignfields: $addSignfields
    updateSignfields: $updateSignfields
    signfieldIds: $signfieldIds
    approvalPolicy: $approvalPolicy
    dingUser: $dingUser

# 同步添加或更新签署区并执行签署
