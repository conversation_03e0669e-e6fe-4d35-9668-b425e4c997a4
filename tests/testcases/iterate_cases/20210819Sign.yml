- config:
    name: "********迭代测试用例"
    base_url: ${ENV(base_url)}

    variables:
      #      - salary_url: ${ENV(base_url)}
      - app_id: ${ENV(app_id_tengqing_SaaS)}
      - accountId: ${ENV(accountId_in_SaaS)}
      - app_id: ${ENV(app_id_tengqing_SaaS)}
      - fileId_tengqing_SaaS: ${ENV(fileId_tengqing_SaaS)}
      - account_id_dy: ${ENV(mobile_shield_wukong_appid_oid)}
      - account_id_sq: ${ENV(account_id_sq_SaaS)}
      - group: ''
      - operator_id: ${ENV(account_id1_in_tsign)}

  #0819迭代项目
- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，flowConfigItemBean传值
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: {buttonConfig:{propButton: "1,2,3,15"}}
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，flowConfigItemBean返回包含ignoreSignLink
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，propButton 传空值
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: {buttonConfig:{propButton: ""}}
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性包含app_id 全部
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "back"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，propButton 传null
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: {buttonConfig:{propButton: null}}
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，包含app_id 全部
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "back"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，buttonConfig 传空
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: {buttonConfig: null}
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，包含app_id 全部
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "back"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，buttonConfig 传null
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: {buttonConfig: {}}
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，返回app_id 中全部
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "back"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，flowConfigItemBean 传空
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: {}
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，返回app_id 中全部
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "back"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，flowConfigItemBean 传空值
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean:
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，返回app_id 中全部
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "back"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，flowConfigItemBean 传null
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: null
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，返回app_id 中全部
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "back"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，flowConfigItemBean传入不存在的值
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: {buttonConfig:{propButton: "123456"}}
    api: api/iterate_cases/signflows.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建流程，flowConfigItemBean传入重复的值
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
      - flowConfigItemBean: {buttonConfig:{propButton: "15,15,15"}}
    api: api/iterate_cases/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，返回去重后的数据
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，flowConfigItemBean传值
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig:{propButton: "1,2,3,15"}}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性返回ignoreSignLink
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，propButton 传空值
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig:{propButton: ""}}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 属性，返回app_id 中全部
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "返回"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，propButton 传null
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig:{propButton: null}}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config null返回appid 中的配置
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，propButton 传null
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig:{propButton: ""}}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 空值返回appid 中的配置
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，buttonConfig 传空
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig:{}}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 返回appid 中的配置
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，buttonConfig 传null
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: null}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 返回appid 中的配置
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，flowConfigItemBean 传空
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 返回appid 中的配置
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，flowConfigItemBean 传null
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: null, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 返回appid 中的配置
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，flowConfigItemBean 传入不存在的值
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig:{propButton: "1101,1"}}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    base_url:  ${ENV(base_url)}
    name: 创建一步发起流程，flowConfigItemBean 传重复数据
    variables:
      - docs: [{"fileId": $fileId_tengqing_SaaS, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig:{propButton: "10,10,15,15"}}, autoArchive: true, autoInitiate: true, businessScene: "签签签签签署署署署署", contractRemind: 65, initiatorAccountId: "", initiatorAuthorizedAccountId: '', flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: $accountId, authorizedAccountId: $accountId}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId_tengqing_SaaS, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType:1, width:150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    base_url:  ${ENV(base_url)}
    name: 获取config 返回去重后的数据
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $accountId
      - querySpaceAccountId: ''
    api: api/iterate_cases/config.yml
    extract:
      - req: content.data.pageConfig.configList
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($req)}', "ignoreSignLink"]