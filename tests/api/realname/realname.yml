- api:   #运营商三要素
    def: createMobileThree($accountId,$name,$idno,$mobile)
    request:
      url: ${ENV(realname_url)}/v1/accounts/identities/$accountId/createMobileThree
      method: POST
      headers: ${get_headers($app_id)}
      json:
        name: $name
        idNo: $idno
        mobile: $mobile

- api:   #运营商三要素的短信验证码
    def: updateMobileThree($accountId,$serviceId,$authcode)
    request:
      url: ${ENV(realname_url)}/v1/accounts/identities/$accountId/updateMobileThree
      method: PUT
      json:
        serviceId: $serviceId
        code: $authcode
      headers: ${get_headers($app_id)}

- api:   #意愿发送验证码
    def: sendSms($accountId,$flowId)
    request:
      url: ${ENV(realname_url)}/v1/willingness/sendSms
      method: POST
      headers: ${get_headers($app_id)}
      json:
        accountId: $accountId
        flowIds: [$flowId]

- api:   #意愿校验验证码
    def: checkSms($accountId,$authcode,$bizId)
    request:
      url: ${ENV(realname_url)}/v1/willingness/checkSms
      method: PUT
      json:
        accountId: $accountId
        authCode: $authcode
        bizId: $bizId
      headers: ${get_headers($app_id)}

- api:   #企业信息比对
    def: busOrgInfoAuth($orgId,$request)
    request:
      url: ${ENV(realname_url)}/v1/organizations/identities/$orgId/infoVerify
      method: POST
      headers: ${get_headers($app_id)}
      json: $request

- api:   #企业打款
    def: busOrgToPay($orgId,$request)
    request:
      url: ${ENV(realname_url)}/v1/organizations/identities/$orgId/pay
      method: PUT
      headers: ${get_headers($app_id)}
      json: $request

- api:   #金额校验
    def: busOrgPayAuth($orgId,$request)
    request:
      url: ${ENV(realname_url)}/v1/organizations/identities/$orgId/verifyPay
      method: PUT
      headers: ${get_headers($app_id)}
      json: $request