- config:
    name: "国密改造项目接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/关键字文档.pdf
      - group: ''
      - data: "待签署文本数据"

###############先验证SM2##############################################
- test:
    name: "SM2_appId下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - SM2_yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber:
      - orgLegalName:
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "RSA_appId下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - thirdPartyUserId: yuzan455651212312321
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - RSA_yuzan_accountId: content.data.accountId

- test:
    name: "验证文本签P1接口参数data是否必填"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"accountId":$SM2_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: data不能为空"]

- test:
    name: "验证文本签接口参数data为空字符串能否发起"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":"","accountId":$SM2_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: data不能为空"]

- test:
    name: "验证文本签接口参数data为正常字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":$SM2_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ['content.data.signatureAlgorithm','SM3withSM2']

- test:
    name: "验证文本签接口参数当type=PLATFORM_USER时，accountId是否必填"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 文本用户签, accountId不能为空"]

- test:
    name: "验证文本签接口参数当type=PLATFORM时，accountId正常个人账号"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":$SM2_yuzan_accountId,"type":"PLATFORM"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ['content.data.signatureAlgorithm','SM3withSM2']

- test:
    name: "验证文本签接口参数当type=PLATFORM_USER时，accountId正常个人账号"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":$SM2_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ['content.data.signatureAlgorithm','SM3withSM2']

- test:
    name: "验证文本签接口参数当type=PLATFORM_USER时，accountId是空字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":"","type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 文本用户签, accountId不能为空"]

- test:
    name: "验证文本签接口参数当type=PLATFORM_USER时，accountId是一个不存在的账号"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":"123456","type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 用户不存在或用户类型错误"]

- test:
    name: "验证文本签接口参数当type=PLATFORM_USER时，accountId是一个企业id"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":sixian_organize_id,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 用户不存在或用户类型错误"]

- test:
    name: "验证文本签接口参数appid和accountId不匹配"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"accountId":$RSA_yuzan_accountId,"data":$data,"type":"PLATFORM_USER"}
      - message: ${ENV(message3)}
      - code: ${strToInt()}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",$code]
      - contains: ["content.message",$message]

- test:
    name: "验证文本签接口参数当type是否必填"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":$SM2_yuzan_accountId}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: type不能为空"]

- test:
    name: "验证文本签接口参数当type是一个空字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":$SM2_yuzan_accountId,"type":""}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: type值错误"]

- test:
    name: "验证文本签接口参数当type是一个不存在的字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":$SM2_yuzan_accountId,"type":"TEST"}
    api: api/iterate_cases/dataSignP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]

- test:
    name: "验证文本签接口参数当type是PLATFORM"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"type":"PLATFORM"}
    api: api/iterate_cases/dataSignP1.yml
    extract:
      - signResult1: content.data.signResult
      - timestampSignature: content.data.timestampSignature
      - certBase64: content.data.certBase64
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ['content.data.signatureAlgorithm','SM3withSM2']

- test:
    name: "验证文本平台签的验签-正常"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfo.signature.modify", False]

- test:
    name: "验证文本平台签的验签-data是否必填"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-data是空字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":"","signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-signResult是否必填"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-data和signResult不匹配"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":"不匹配","signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438401]
      - eq: ["content.message", "验签失败"]

- test:
    name: "验证文本平台签的验签-signResult不存在"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":"11111111111","timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438401]
      - eq: ["content.message", "验签失败"]

- test:
    name: "验证文本平台签的验签-signResult为空字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":"","timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: signResult不能为空"]

- test:
    name: "验证文本平台签的验签-certBase64是否必填"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-certBase64是空字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":"","signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-certBase64是别的项目文本签的证书"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":"MIIDyzCCArOgAwIBAgIMRt87WtD1uTE6GHehMA0GCSqGSIb3DQEBCwUAMHUxCzAJBgNVBAYTAkNOMQ8wDQYDVQQIHgZTV06sXgIxDzANBgNVBAceBmxfgs93ATEQMA4GA1UEChMHU21hcnRDQTERMA8GA1UECx4IZnphZwBDAEExHzAdBgNVBAMUFlNtYXJ0Q0EgUlNBIENBMV9TVUJDQTQwHhcNMjAwNjE2MTExODAwWhcNMjEwNjE2MTExODAwWjBzMQswCQYDVQQHDAJoejELMAkGA1UECAwCemoxCzAJBgNVBAYTAmNuMR4wHAYDVQQLDBUxMUA5MTM0MTMyNE1BMlJNQjFXM1QxKjAoBgNVBAMMIeazl+WOv+a3seaziee6r+WHgOawtOaciemZkOWFrOWPuDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMzHeuKB4CIrcx6b66+dL66wPZM/XBK3fG1VnhtaIObCoHPl2arDzM4WbZkXMCjoP6mSgsD0oy6qcJ+7Ojv/wNhpzuc85UFuEv+dPk2w1wtX68I3RNgmQSq2wjCLQqVluXmj5EjdcIeZKzNbu9NfMVwAnSt9DDCE2i10WmOvk1ZH429EpXGxHo+ca8ondicyENkdgkSKp8cItwF3FjF6CtydgGEIiGmUC3wS0ZPjDB8MKcmgEi1K3HlwCkfyYDina3bhfI5Ibq0YJcKUo1syrJB6T8qpnM9n/ThGSm5pm0J8b4pfoE/GyXtN/pCgZWv3+6NUZ/2pGeNTdJWV2+K7CasCAwEAAaNdMFswHwYDVR0jBBgwFoAUIY9fUY1++9cy5oHBs9FNn9Hu0WwwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQU/XU9mmYaARzdBJ/b/YAbw34MG18wCwYDVR0PBAQDAgbAMA0GCSqGSIb3DQEBCwUAA4IBAQCIeouNqn7WaHCZt6b0gH39Hcfd/fMLFAP0Y2aXyz7uGxGCLurFcyvSiFpQ9Jp/NjPVDFrrwBwj/deACmyI3qCd8H+wVTffOIRLUI6E2bMDX+QKIFmIFUmsNhNQS8n1/n51VrBAUf5ykkAzhjk5zxLXC7mAaPqhS3rQYupIq2NbQvRswpPwgWV1+fuB/fXNOwYi6AeeVixi+xYqbb+beVG/JdK/Oiv2C37Ac8ZbqhhLXU/M2o7OeG5KuxTHS2lhaOB61DAtx3GpJ/FFNk6uRb5jtwV+fNubciElegzHn3k/c3dD5bqAn8S76M7ZPwCMyyKW6MCs0m24exKF9CNX+DlF","signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438401]

- test:
    name: "验证文本平台签的验签-signatureAlgorithm是否必填"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-signatureAlgorithm是空字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":""}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-signatureAlgorithm是SHA256withRSA"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SHA256withRSA"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438401]
      - eq: ["content.message", '验签失败']

- test:
    name: "验证文本平台签的验签-signatureAlgorithm是true"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":true}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", '参数错误: signatureAlgorithm值错误']

- test:
    name: "验证文本平台签的验签-timestampSignature是否必填"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-timestampSignature是空字符串"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2","timestampSignature":""}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证文本平台签的验签-timestampSignature是别的项目下文本签的"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2","timestampSignature":"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\n"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438401]
      - eq: ["content.message", '验签失败']

- test:
    name: "验证文本签接口参数当type是PLATFORM_USER"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"accountId":$SM2_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    extract:
      - signResult1: content.data.signResult
      - timestampSignature: content.data.timestampSignature
      - certBase64: content.data.certBase64
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ['content.data.signatureAlgorithm','SM3withSM2']

- test:
    name: "验证文本平台用户签的验签-正常"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SM3withSM2"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfo.signature.modify", False]

#######################RSA证书验证#########################################

- test:
    name: "验证文本签接口参数当type是PLATFORM"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"type":"PLATFORM"}
    api: api/iterate_cases/dataSignP1.yml
    extract:
      - signResult1: content.data.signResult
      - timestampSignature: content.data.timestampSignature
      - certBase64: content.data.certBase64
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ['content.data.signatureAlgorithm','SHA256withRSA']

- test:
    name: "验证文本平台签的验签-正常"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SHA256withRSA"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfo.signature.modify", False]

- test:
    name: "验证文本签接口参数当type是PLATFORM_USER"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignP1.yml
    extract:
      - signResult1: content.data.signResult
      - timestampSignature: content.data.timestampSignature
      - certBase64: content.data.certBase64
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ['content.data.signatureAlgorithm','SHA256withRSA']

- test:
    name: "验证文本平台用户签的验签-正常"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"signResult":$signResult1,"timestampSignature":$timestampSignature,"certBase64":$certBase64,"signatureAlgorithm":"SHA256withRSA"}
    api: api/iterate_cases/verifyP1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfo.signature.modify", False]

