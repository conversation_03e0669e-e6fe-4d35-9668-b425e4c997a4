#def: getBatchSignUrl($app_id,$accountId,$authorizerId,$batchSerialId,$signPlatform,$redirectUrl,$flowIds)
request:
  url: /v1/signflows/getBatchSignUrl
  method: POST
  headers: ${get_headers($app_id)}
  json:
    accountId: $personOid
    authorizerId: ${ENV(authorizerId)}
    signPlatform: 5
    redirectUrl: "https://www.esign.cn/"
    flowIds: $flowIds
validate:
  - eq: [status_code, 200]
#获取批量签署链接
