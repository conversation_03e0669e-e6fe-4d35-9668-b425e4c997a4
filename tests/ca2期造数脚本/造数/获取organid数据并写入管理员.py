import json
import requests

ouidL = []
with open("100个实名组织企业0208.json", "r", encoding="utf-8") as f :
    f = f.read()
    data = json.loads(f)
    items = data["data"]["items"]
    for item in items:
        # print(item["ouid"])
        ouid = item["ouid"]
        ouidL.append(ouid)
ouidL = ouidL[:53]
print(ouidL)
for i in ouidL:
    if i == "a97780e49e1f48f8bd3a1e87af0cd2fe":
        continue
    url = 'http://easun-service.testk8s.tsign.cn/transferIcAdmin/input'
    headers = {
        'Content-Type': 'application/json'
    }

    data = {
        "input": {
            "organOuid": i,
    "originAdminOuid": "713e3c56d0b24b8d98766693d6e675b8",
            "grantedAdminOuid": "713e3c56d0b24b8d98766693d6e675b8"
        }
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))

    # 打印响应
    print(response.status_code)
    print(response.json())
    if response.status_code != 200:
        print("请求失败")
        break
