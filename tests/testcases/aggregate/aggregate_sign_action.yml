- config:
    name: 融合层签署流程
    base_url: ${ENV(footstone_api_url)}
    variables:
      - autoArchive: False
      - group: ''
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - contractValidity: ''
      - signValidity: ''
      - extend: {}
      - sealId_p1_type: 1
      - sealId_p2_type: 1
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - personOid3: 310913bf68594f8bb7c457215e0b4831
      - path_pdf: data/个人借贷合同.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - dingUser: {}
      - client_id: pc
- test:
    name: 融合层签署场景-"创建新账号1"
    api: api/user/accounts/create_accounts.yml   #无email/mobile
    variables:
      - idNo: "350725198204117350"
      - idType: "19"
      - name: "孙明睿"
      - mobile: ***********
      - email: <EMAIL>
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]
- test:
    name: 融合层签署场景-"创建新账号2"
    api: api/user/accounts/create_accounts.yml   #无email/mobile
    variables:
      - idNo: "350525197001159475"
      - idType: "19"
      - name: "施开霁"
      - mobile: ***********
      - email: <EMAIL>
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: 融合层签署场景-"创建新组织1"
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_Id2
      - idNo: 71110115669128233L
      - name: "杭州姑姑姑技有限公司"
      - thirdPartyUserId: ${get_timestamp()}
      - creator: $personOid2
    extract:
      - orgOid1: content.data.orgId

- test:
    name: 获取文件信息
    variables:
      - fileId: $fileId1
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey


- test:
    name: 融合层签署场景-根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileKey: fileKey
      - fileName: 个人借贷合同.pdf
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 融合层签署场景-创建模板，获取templateTypeId
    variables:
      - fileKey: $fileKey
      - fileMimeType: "application/pdf"
      - templateName: "测试模板"
      - accountId: $personOid1
    api: api/file_template/templates/create_templates.yml
    extract:
      - templateId: content.data.templateId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 融合层签署场景-根据文件模板创建文件
    variables:
      - name: 测试
      - accountId: $personOid1
      - chunkedFields: []
      - simpleFormFields: {"出借方": "1","借款方": "2018-12-21"}
      - fileName: 个人借贷合同-模板文件
      - templateId: $templateId
    api: api/file_template/files/createbytemplate.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 融合层签署场景-创建流程和文档
    variables:
      - autoArchive:
      - configInfo:
      - contractValidity:
      - extend:
      - payerAccountId:
      - signValidity:
      - initiatorAccountId: $personOid1
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 融合层签署场景-查询流程详情
    api: api/signflow/aggregate_sign/select_detail.yml
    variables:
      - queryAccountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",0]

- test:
    name: 融合层签署场景-"创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - accountId: $personOid1
      - color: "RED"
      - height: 2
      - alias: "某某的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: 融合层签署场景-查询personOid1的印章
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - sealId_p1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 融合层签署场景-"创建企业模版印章"
    api: api/seals/seals/create_c_template_seal_appid.yml
    variables:
      - orgId: $orgOid1
      - color: "RED"
      - height: 2
      - width: 2
      - alias: "某某某"
      - type: "TEMPLATE_ROUND"
      - central: NONE
      - hText:
      - qText:
    extract:
      - sealId_o1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: 融合层签署场景-查询orgOid1的印章
    api: api/seals/seals/select_c_seals.yml
    variables:
      - orgId: $orgOid1
    extract:
      - sealId_o1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 融合层签署场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 融合层签署场景-批量添加流程签署区
    variables:
      - flowId: $flowId
      - posBean: {}
      - autoExecute: 0
      - signfield1: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$personOid1,$personOid1,$sealId_p1,$sealId_p1_type)} #个人签
      - signfield2: ${gen_signfield_data_V2(1,$autoExecute,$fileId1,$flowId,$posBean,0,$personOid2,$orgOid1,$sealId_o1,$sealId_p2_type)} #企业签
      - signfields: [$signfield1,$signfield2]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldIds: content.data.signfieldIds
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]

- test:
    name: 融合层签署场景-查询流程详情
    api: api/signflow/aggregate_sign/select_detail.yml
    variables:
      - queryAccountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",1]
      - eq: ["content.data.signers.0.signStatus",1]  #签署区状态
      - eq: ["content.data.signers.1.signStatus",1]

- test:
    name: 融合层签署场景-添加_更新_执行签署区
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,$sealId_p1_type)}
      - addSignfield_4: ${gen_signfield_data_V2(1,0,$fileId1,$flowId,$posBean,1,$personOid2,$orgOid1,$sealId_o1,$sealId_p2_type)}
      - addSignfields: [$addSignfield_3,$addSignfield_4]
      - updateSignfields: []
      - signfieldIds: $signfieldIds
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    extract:
      - docSignResult_fileId: content.data.docSignResult.0.fileId
      - signfieldSignResult_signfieldId: content.data.signfieldSignResult.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.docSignResult.0.optResult",0]
      - eq: ["content.data.docSignResult.0.failedReason",null]

- test:
    name: 融合层签署场景-查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - accountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 融合层签署场景-归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 融合层签署场景-查询流程：已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - queryAccountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 融合层签署场景-查询流程详情
    api: api/signflow/aggregate_sign/select_detail.yml
    variables:
      - queryAccountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 融合层签署场景-"获取签署文档和签署区"
    api: api/signflow/aggregate_sign/docs_signfields.yml
    variables:
      - flowId: $flowId
      - accountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ["content.data.canExecute",false]
      - eq: ["content.data.flowStatus",2]
      - eq: ["content.data.signedDocs.0.signStatus",2]


#- test:
#    name: 融合层签署场景-"注销组织"
#    api: api/user/organizations/delete_organizations.yml
#    variables:
#      - organId: $orgOid1
#      - operatorid: ''
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#      - eq: ["content.message","成功"]

- test:
    name: 融合层签署场景-"注销账号"
    api: api/user/accounts/delete_accounts.yml
    variables:
      - accountId: $personOid1
      - operatorid: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: 融合层签署场景-"注销账号"
    api: api/user/accounts/delete_accounts.yml
    variables:
      - accountId: $personOid2
      - operatorid: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]