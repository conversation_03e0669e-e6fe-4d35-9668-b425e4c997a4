- config:
    name: 通过文件创建签署流程
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "**********"
      - file_id: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id2_in_tsign)}
      - account_id_2_tsign: ${ENV(account_id2_in_tsign)}
      - account_id_3_tsign: ${ENV(account_id3_in_tsign)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}


- test:
    name: "企业方发起1"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"authConfig": {"audioVideoTemplateId": "1","orgAvailableAuthModes": ["ORG_BANK_TRANSFER"],"psnAvailableAuthModes": ["PSN_BANK4_AUTHCODE"]},"autoFinish":true,"autoStart":true,"chargeConfig":{"barrierCode": "DD23D3GS21","chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"$signFlowExpireTime","signFlowTitle":"签署1"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002,]
      - eq: [content.message, '参数错误: 当前项目下已配置隔离模式，请勿传入隔离码']
- test:
    name: "企业方发起2"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"authConfig": {"audioVideoTemplateId": "1","orgAvailableAuthModes": ["ORG_BANK_TRANSFER"],"psnAvailableAuthModes": ["PSN_BANK4_AUTHCODE"]},"autoFinish":true,"autoStart":true,"chargeConfig":{"barrierCode": "","chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"$signFlowExpireTime","signFlowTitle":"签署1"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435405,]
      - contains: [content.message, '企业账户中电子合同份数不足']

- test:
    name: "企业方发起3"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: { "attachments": [ { "fileId": "$file_id" } ],"copiers": [ { "copierPsnInfo": { "psnId": "$account_id_2_tsign" } } ],"docs": [ { "fileEditPwd": "","fileId": "$file_id","fileName": "签署文件.PDF","neededPwd": false } ],"signFlowConfig": { "authConfig": { "audioVideoTemplateId": "1","orgAvailableAuthModes": [ "ORG_BANK_TRANSFER" ],"psnAvailableAuthModes": [ "PSN_BANK4_AUTHCODE" ] },"autoFinish": true,"autoStart": true,"chargeConfig": { "barrierCode": "","chargeMode": 0, "orderType":"DISTRIBUTION" },"noticeConfig": { "noticeTypes": "1,2" },"notifyUrl": "$notifyUrl","signConfig": { "availableSignClientTypes": "","showBatchDropSealButton": true },"signFlowExpireTime": "$signFlowExpireTime","signFlowTitle": "签署1" },"signFlowInitiator": { "orgInitiator": { "orgId": $account_id_sx_tsign, "transactor": { "psnId": $account_id_1_tsign } } },"signers": [ { "noticeConfig": { "noticeTypes": "1,2" },"psnSignerInfo": { "psnId": "$account_id_2_tsign" },"signConfig": { "forcedReadingTime": 0,"signOrder": 1 },"signFields": [ { "customBizNum": "","fileId": "$file_id","normalSignFieldConfig": { "assignedSealId": "","autoSign": false,"availableSealIds": [ ],"freeMode": false,"movableSignField": true,"orgSealBizTypes": "","psnSealStyles": "0,1","signFieldPosition": { "acrossPageMode": "","positionPage": "1","positionX": 200,"positionY": 200 },"signFieldSize": 200,"signFieldStyle": 1 },"signDateConfig": { "dateFormat": "yyyy-MM-dd","fontSize": 20,"showSignDate": 2,"signDatePositionPage": 1,"signDatePositionX": 200,"signDatePositionY": 200 },"signFieldType": 0 } ],"signerType": 0 } ] }
    validate:
      - eq: [status_code, 200]
      - eq: [ content.code, 1435002, ]
      - contains: [ content.message, '请勿使用渠道商扣费' ]

#-   test:
#        name: "获取签署链接"
#        variables:
#        -   #        -   flowId: $flowId
#        -   accountId: $account_id_2_tsign
#        -   urlType: 0
#        -   multiSubject: true
#        -   organizeId:
#        -   compressContext: true
#        api: api/iterate_cases/executeUrl.yml
#        extract:
#        -   shortUrl: content.data.shortUrl
#        -   code: content.code
#        -   message: content.message
#        validate:
#        -   eq: ["content.code",0]
#
#
#- test:
#    name: /v3/signflows/{flowId}/executeUrl - 获取签署链接失败
#    variables:
#        - flowId: $flowId
#        - accountId : $account_id_2_tsign
#        - authorizedAccountId: $account_id_2_tsign
#        - clientType:
#        - signerAccount: $account_id_2_tsign
#        - authorizedAccount: $account_id_2_tsign
#    api: api/xy_wk_integration/V3_executeUrl.yml
#    validate:
#        - contains: ["content.message", "成功"]
