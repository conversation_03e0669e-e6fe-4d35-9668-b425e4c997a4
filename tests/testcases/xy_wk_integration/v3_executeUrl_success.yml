- config:
    name: "悟空轩辕整合"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(mobile_shield_wukong_appid)}
      - path_pdf1: data/个人借贷合同.pdf
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}
      - appid_oid: ${ENV(mobile_shield_wukong_appid_oid)}
      - another_orgid_paltformAutoSign: ${ENV(another_orgid_paltformAutoSign)}
      - dongying_transfer_receiver: ${ENV(dongying_transfer_receiver)}
      - sixian_transfer_receiver: ${ENV(sixian_transfer_receiver)}
      - fileId1: ${ENV(fileId)}

- test:
    name: createFlowOneStep - "个人1的个人签，个人1代企业A签，个人2代企业B签"
    variables:
      - doc: {fileId: $fileId1, fileName: "个人借贷合同.pdf"}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      #        - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 轩辕悟空整合-个人1的个人签，个人1代企业A签，个人2代企业B签",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},
                   signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 },
                  {platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "<EMAIL>",authorizedAccount: "杭州书契网络科技合伙企业(有限合伙)"},
                   signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 },
                  {platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: "esigntest东营伟信建筑安装工程有限公司"},
                   signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/xy_wk_integration/V3_createFlowOneStep.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



#获取签署链接
- test:
    name: /v3/signflows/{flowId}/executeUrl - 成功获取签署链接
    variables:
      - flowId: $flowId1
      - accountId: $flowId1_accountId
      - authorizedAccountId: $flowId1_authorizedAccountId
      - clientType: $flowId1_clientType
      - signerAccount: $flowId1_signerAccount
      - authorizedAccount: $flowId1_authorizedAccount
    api: api/xy_wk_integration/V3_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]