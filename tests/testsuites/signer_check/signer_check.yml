config:
  name: 签署人查询信息、校验权限、信息反馈、补全信息
  variables:
    - mobile1: ***********
    - mobile2: ***********
    - email: <EMAIL>
    - personOid1: ${getOid($mobile1)}
    - personOid2: ${getOid($mobile2)}
    - personOid3: ${getOid($email)}
    - idType_person: CRED_PSN_CH_IDCARD
    - thirdPartyUserId_person_1: autotest${get_randomNo()}
    - name_person_1: 陈凯丽
    - idNumber_person_1: 362323199201061068
    - app_id: ${ENV(BZQ-App-Id)}
    - path_pdf: data/个人借贷合同.pdf
    - contractValidity:
    - payerAccountId:
    - signValidity:

  request:
    base_url: ${ENV(footstone_api_url)}
    "headers":
      "Content-Type": "application/json"

testcases:

  签署人查询信息、校验权限、信息反馈、补全信息:
    testcase: testcases/signer_check/signer_check.yml
