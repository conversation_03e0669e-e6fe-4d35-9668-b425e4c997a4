base_url: ${ENV(saas-common-manage_url)}
name: 创建分享
request:
  headers: ${get_headers($app_id,$accountId)}
  json:
    callSorce: $callSorce
    accountId: $accountId
    needShareCode: $needShareCode
    needShareQrCode: $needShareQrCode
    resourceId: $resourceId
    resourceType: $resourceType
    shareOperateType: $shareOperateType
    shareType: $shareType
    shareTargets: $shareTargets
    subjectId: $subjectId
    router: $router
    shareEndTime: $shareEndTime
  method: post
  url: /v1/saas-common/shares/create
variables:
  needShareCode: true
  needShareQrCode: false
  resourceType: 'PROCESS'
  router: "urging"
  callSorce: "CONTRACT"
