#def: create_organizations($accountId,$name,$orgType,$orgCode,$legalName,$legalIdNo)         #创建组织
request:
  url: ${ENV(footstone_user_url)}/v1/organizations/createOverall
  method: POST
  headers: ${get_headers($app_id)}
  json:
    creater: $accountId
    credentials:
      globalIdentity: CRED_PSN_CH_IDCARD
      orgCode: null
      regCode: null
      usccCode: null
    idcards:
      organ:
        type: $orgType
        value: $orgCode
      projThirdparty:
        thirdpartyUserId: ''
        thirdpartyUserType: ''
      thirdparty:
        thirdpartyKey: ''
        thirdpartyUserId: ''
        thirdpartyUserType: ''
    properties:
      agentIdno: ''
      agentName: ''
      legalIdno: $legalIdNo
      legalName: $legalName
      name: $name
    source: {}


