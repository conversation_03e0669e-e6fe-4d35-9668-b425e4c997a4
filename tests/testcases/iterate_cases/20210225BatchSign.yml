- config:
    name: "0225flow-manager幂等"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(xuanyuan_appid)}


#批量获取流程签署文档列表
- test:
    name: "签署参与人信息有重复数据"
    variables:
      - app_id: $app_id
      - flowId: ["b2875ce8eec442d5aeeed6faa30a0d06"]
    api: api/flow-manager/batchQueryFlowDocs-api.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.success",true]
      - eq: ["content.message","执行成功"]






































      #一步发起流程
      #- test:
      #    name: 一步发起
      #    variables:
      #        - doc : {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      #        - attachments: []
      #        - docs: [$doc]
      #        - copiers: []
      #        - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      #        - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
      #    api: api/mobile-shield/createFlowOneStep.yml
      #    extract:
      #      - sign_flowId1: content.data.flowId
      #    validate:
      #- eq: ["content.message",成功]




























