- config:
    name: "悟空轩辕整合 - 验证企业-法人-经办人签署区"    #需要打开链接验证  查看是否有法人签  消息队列 ： pdftoimg_pretreatment_topic
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "**********"
      - xuanyuan_sixian: ${ENV(mobile_shield_wukong_appid_oid)}
      - xuanyuan_sixian_name: ${ENV(xuanyuan_sixian_name)}
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - group: ''
      - path_pdf1: data/16M.pdf
      - fileId: ${get_file_id($app_id,$path_pdf1)}
      - oid_linjue_tsign: "3986d7544d9848f186ee9c5dcd342de1"  #linjue
      - phone_linjue: "***********"
      - mail_linjue: "<EMAIL>"
      - none_tsign_account_id: ${ENV(none_tsign_account_id)}
      - accountId_withRealName_withoutAuth: ${ENV(accountId_withRealName_withoutAuth)}
      - fileId2: "b047c68ccdbf474c8e16ff6a6c6b20fa"

- test:
    name: "创建签署人账号 - gan"
    variables:
      - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId
      - yuzan_accountId: content.data.accountId

- test:
    name: V2一步发起 pospage传all
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - doc: {encryption: 0,fileId: $fileId, fileName: '文件1', source: 0}
      - docs: [$doc]
      - copiers: []
      - attachments: []
      - "contractValidity": ${get_timestamp_13(1800000)}
      - flowInfo: { autoArchive: true, autoInitiate: true, "contractValidity":$contractValidity, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2"}}
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": "3986d7544d9848f186ee9c5dcd342de1",
            "authorizedAccountId": "008f0fa30f1a44989d5784ac05ded90a"
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean":
              {
                "posPage": "all",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": 2,
            "signType": 2

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: V2一步发起 pospage传all  页面数据库里改成0
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - doc: {encryption: 0,fileId: "b047c68ccdbf474c8e16ff6a6c6b20fa", fileName: '文件1', source: 0}
      - docs: [$doc]
      - copiers: []
      - attachments: []
      - "contractValidity": ${get_timestamp_13(1800000)}
      - flowInfo: { autoArchive: true, autoInitiate: true, "contractValidity":$contractValidity, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2"}}
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": "3986d7544d9848f186ee9c5dcd342de1",
            "authorizedAccountId": "008f0fa30f1a44989d5784ac05ded90a"
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": "b047c68ccdbf474c8e16ff6a6c6b20fa",
            "sealType": "",
            "posBean":
              {
                "posPage": "all",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": 2,
            "signType": 2

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002,]
      - contains: ["content.message",'参数错误: 未解析到文档页码']



- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $oid_linjue_tsign
      - urlType: 0
      - multiSubject: true
      - organizeId:
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
#分步签署流程开始
- test:
    name: "创建签署流程-分步流程"
    variables:
      - autoArchive: false
      - businessScene: 集测-分步发起流程
      - configInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', signPlatform: '',redirectUrl: "http://www.baidu.com"}
      # redirectUrl: "http://www.baidu.com",
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: ""
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flow_one: content.data.flowId
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


- test:
    name: "添加1份流程文档-分步流程"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "个人-添加成功个人签名域-指定签名域 可以添加位置信息-分步流程"
    variables:
      - flowId: $sign_flowId
      - signfields: [
      { "signDateBeanType": "2","signDateBean": { "fontSize": 50,"posX": 220,"posY": 150 },"posBean":{"addSignTime":false,"posPage":"1","posX":200,"posY":500,"location": "创建第一个手动签署区创建第一个手动签署区"},"fileId":$fileId,"signerAccountId":$accountId_gan,"actorIndentityType":"0","authorizedAccountId":$accountId_gan,"assignedPosbean":false,"order":1,"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]


- test:
    name: "开启签署流程-分步流程"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.code",0]

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $oid_linjue_tsign
      - urlType: 0
      - multiSubject: true
      - organizeId:
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
#分步签署流程关闭


#以下为V3一步发起
- test:
    name: v3一步发起 多主体经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: '', noticeType: '', redirectUrl: '', signPlatform: ''}
      - json: {
        "docs":  $docs,
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "v3一步发起 多主体经办人签",
          "flowConfigInfo": {
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": ""
            },
            "signConfig": {
              "redirectUrl": "",
              "signPlatform": ""
            }
          },
          "hashSign": false,
          "remark": "印章平台",
          "signValidity": *************
        },
        "signers": [
        {
          "signOrder": 1,
          "signerAccount": {
            "signerAccountId": "3986d7544d9848f186ee9c5dcd342de1",
            "authorizedAccountId": "008f0fa30f1a44989d5784ac05ded90a"
          },
          "signfields": [
          {
            "autoExecute": false,
            "actorIndentityType": 4,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 100,
              "posY": 100
            },
            "signType": 1
          },
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "signType": 1
          }
          ],
          "thirdOrderNo": "xyz"
        }
        ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]



- test:
    name: v3一步发起 多主体经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: '', noticeType: '', redirectUrl: '', signPlatform: ''}
      - json: {
        "docs":  $docs,
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "v3一步发起 多主体经办人签",
          "flowConfigInfo": {
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": ""
            },
            "signConfig": {
              "redirectUrl": "",
              "signPlatform": ""
            }
          },
          "hashSign": false,
          "remark": "印章平台",
          "signValidity": *************
        },
        "signers": [
        {
          "signOrder": 1,
          "signerAccount": {
            "signerAccountId": "3986d7544d9848f186ee9c5dcd342de1",
            "authorizedAccountId": "008f0fa30f1a44989d5784ac05ded90a"
          },
          "signfields": [
          {
            "autoExecute": false,
            "actorIndentityType": 4,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 100,
              "posY": 100
            },
            "signType": 1
          },
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "signType": 1
          }
          ],
          "thirdOrderNo": "xyz"
        }
        ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: 获取签署链接-个人签
    variables:
      - accountId: $oid_linjue_tsign
      - flowId: $sign_flowId2
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


      #- test:
      #    name:  获取批量签署url-clientType-H5
      #    variables:
      #      - json: {
      #    "clientType": "H5",
      #    "operatorId": $oid_linjue_tsign,
      #    "redirectUrl": "",
      #    "signFlowIds": [$sign_flowId1,
      #        $sign_flowId2,"02d954d32d4543c0914f14d08187d2b3","71f29af130424867a933aef5e4598514","032bb28cf463450e80e393bf6cbadc07","5aa736bcff4f471e809c5c4d3752d695",
      #"4d6355ec119146488ba6cbf0b8aa917a","79ec28aa7c2f4f838624fab2b9b3b685","2f7d7eb08707486589a49308e035c826","6802e05a293d4458864f25de5dd4a179"
      #    ]
      #}
      #    api: api/signflow/batchSign0210/getBatchUrl.yml
      #    validate:
      - eq: ["content.message", "成功"]
      #      - contains: ["content.data.batchSignUrlWithoutLogin", "gan.ning"]
      #    teardown_hooks:
      #      - ${teardown_re_find($response)}
      #    extract:
      #      - batchSerialId: batchSerialId
      #
      #
      #- test:
      #    name:  获取批量签署url-clientType-PC
      #    variables:
      #      - json: {
      #    "clientType": "PC",
      #    "operatorId": $oid_linjue_tsign,
      #    "redirectUrl": "",
      #    "signFlowIds": [$sign_flowId1,
      #        $sign_flowId2,"02d954d32d4543c0914f14d08187d2b3","71f29af130424867a933aef5e4598514","032bb28cf463450e80e393bf6cbadc07","5aa736bcff4f471e809c5c4d3752d695",
      #"4d6355ec119146488ba6cbf0b8aa917a","79ec28aa7c2f4f838624fab2b9b3b685","2f7d7eb08707486589a49308e035c826","6802e05a293d4458864f25de5dd4a179"
      #    ]
      #}
      #    api: api/signflow/batchSign0210/getBatchUrl.yml
      #    validate:
      - eq: ["content.message", "成功"]
      #    teardown_hooks:
      #      - ${teardown_re_find($response)}
      #    extract:
      #      - batchSerialId: batchSerialId
      #
      #- test:
      #    name:  获取批量签署url-clientType-ALL
      #    variables:
      #      - json: {
      #    "clientType": "ALL",
      #    "operatorId": $oid_linjue_tsign,
      #    "redirectUrl": "",
      #    "signFlowIds": [$sign_flowId1,
      #        $sign_flowId2,"02d954d32d4543c0914f14d08187d2b3","71f29af130424867a933aef5e4598514","032bb28cf463450e80e393bf6cbadc07","5aa736bcff4f471e809c5c4d3752d695",
      #"4d6355ec119146488ba6cbf0b8aa917a","79ec28aa7c2f4f838624fab2b9b3b685","2f7d7eb08707486589a49308e035c826","6802e05a293d4458864f25de5dd4a179"
      #    ]
      #}
      #    api: api/signflow/batchSign0210/getBatchUrl.yml
      #    validate:
      - eq: ["content.message", "成功"]
      #      - contains: ["content.data.batchSignUrlWithoutLogin", "gan.ning"]
      #    teardown_hooks:
      #      - ${teardown_re_find($response)}
      #    extract:
      #      - batchSerialId: batchSerialId
      #
      #
      #- test:
      #    name:  获取批量签署url-clientType-不传
      #    variables:
      #      - json: {
      #    "operatorId": $oid_linjue_tsign,
      #    "redirectUrl": "",
      #    "signFlowIds": [$sign_flowId1,
      #        $sign_flowId2,"02d954d32d4543c0914f14d08187d2b3","71f29af130424867a933aef5e4598514","032bb28cf463450e80e393bf6cbadc07","5aa736bcff4f471e809c5c4d3752d695",
      #"4d6355ec119146488ba6cbf0b8aa917a","79ec28aa7c2f4f838624fab2b9b3b685","2f7d7eb08707486589a49308e035c826","6802e05a293d4458864f25de5dd4a179"
      #    ]
      #}
      #    api: api/signflow/batchSign0210/getBatchUrl.yml
      #    validate:
      - eq: ["content.message", "成功"]
#      - contains: ["content.data.batchSignUrlWithoutLogin", "gan.ning"]
#    teardown_hooks:
#      - ${teardown_re_find($response)}
#    extract:
#      - batchSerialId: batchSerialId