- config:
    name: "V3批量签署-7876649549-cddbaf1de33eacf989ce734a17a5a42b"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "7876649549"
      - path_pdf1: data/个人借贷合同.pdf
      - fileId: ${get_file_id($app_id,$path_pdf1)}
      - psnId: "ee46622cd1f14f8fb7b889361a952bf1" #桃浪标准签署账号
      - psnId_0: "8a48c66df52e40b4a5b29320eaa87117" #测试猫猫零
      - psnId_1: "349871f0550343c88733953d229035f3" #测试猫猫一
      - psn_sealId: "b49d7661-2471-45bb-a725-246d53c45c51" #签署人/指定印章ID
      - orgId_A: "ebd4ec8090e549df8dfc0b6c796df97c" # 桃浪企业A
      - orgIdA_sealId_0: "620576e0-5497-43a7-9ca1-b489772f4973"
      - signTips: {"signTipsTitle": "签署声明标题","signTipsContent": "签署声明内容-signTipsContent"}

- test:
    name: "创建签署人账号-未实名"
    variables:
      - thirdPartyUserId: autotest-bfj-******************
      - name: 测试暴富君
      - idNumber: ******************
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - psn_bfj: content.data.accountId

- test:
    name: "创建签署人账号-猫猫零"
    variables:
      - thirdPartyUserId: autotest-mao0-******************
      - name: 测试猫猫零
      - idNumber: ******************
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - psn_mao0: content.data.accountId



- test:
    name: "V3发起流程1-AI手绘+0备注签署区-阅读倒计时10S"
    api: api/sign-flow/create-by-file.yml
    variables:
      - psn_tl: {"signConfig": {"forcedReadingTime": 10},"psnSignerInfo": {"psnId": "$psnId"},"signerType": 0,"signFields": [{"signFieldType": "0","fileId": "$fileId","normalSignFieldConfig": {"psnSealStyles": "2","autoSign": false,"freeMode": false,"signFieldPosition": {"positionPage": "1","positionX": "400","positionY": "300"},"signFieldStyle": 1} },{"signFieldType": "1","fileId": "$fileId","remarkSignFieldConfig": {"inputType": 2,"autoSign": false,"freeMode": false,"signFieldPosition": {"positionPage": "1","positionX": "400","positionY": "300"} } } ] }
      - json: {"docs": [{"fileId": "$fileId","fileName": "字数少一点.pdf" }],"signFlowConfig": {"signFlowTitle": "流程1-AI手绘+备注签署区+阅读倒计时10S","autoFinish": true,"autoStart": true},"signers": [$psn_tl]}
    extract:
      - flowId_1: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name:  桃浪-获取批量签署url-V3【流程1】
    variables:
      - json: {
        "operatorId": $psnId,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_1
        ]
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
    extract:
      - batchSerialId_only_flowId_1: content.data.batchSerialId

- test:
    name: 获取批量签署流程列表-需阅读
    variables:
      - batchSerialId: $batchSerialId_only_flowId_1
    api: api/batchSignV3/getBatchSignList.yml
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.assignedPosSigns", 1]  #已指定位置流程数：1
      - len_eq: ["content.data.assignedPosSealSigns", 0]  #已指定印章位置流程数：0
      - len_eq: ["content.data.freeSigns", 0]  #需指定印章位置流程数：0
      - eq: ["content.data.assignedPosSigns.0.needRead", true] #需阅读


- test:
    name: "V3发起流程2：个人+企业签署-不指定签署位置-个人普通手绘"
    api: api/sign-flow/create-by-file.yml
    variables:
      - signers_psn: {"psnSignerInfo": {"psnId": "$psnId"},"signerType": 0,"signFields": [{"signFieldType": "0","fileId": "$fileId","normalSignFieldConfig": {"psnSealStyles": "0","autoSign": false,"freeMode": true,"signFieldStyle": 1} }]}
      - signers_org: {"orgSignerInfo": {"orgId": "$orgId_A","transactorInfo": {"psnId": "$psnId" } },"signerType": 1,"signFields": [{"signFieldType": "0","fileId": "$fileId","normalSignFieldConfig": {"signFieldStyle": 1,"autoSign": false,"freeMode": true} }]}
      - json: {"docs": [{"fileId": "$fileId","fileName": "字数少一点.pdf"}],"signFlowConfig": {"signFlowTitle": "流程2：个人+企业签署-不指定签署位置-普通手绘","autoFinish": true,"autoStart": true},"signers": [$signers_psn,$signers_org]}
    extract:
      - flowId_2: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "V3发起流程3-企业A法人签-不指定签署位置-个人模板章-0有风险提示"
    api: api/sign-flow/create-by-file.yml
    variables:
      - signers_psn: {"psnSignerInfo": {"psnId": "$psnId"},"signerType": 0,"signFields": [{"signFieldType": "0","fileId": "$fileId","normalSignFieldConfig": {"psnSealStyles": "1","autoSign": false,"freeMode": true,"signFieldStyle": 1} }]}
      - signers_faren: {"orgSignerInfo": {"orgId": "$orgId_A","transactorInfo": {"psnId": "$psnId"} },"signerType": 2,"signFields": [{"signFieldType": "0","fileId": "$fileId","normalSignFieldConfig": {"signFieldStyle": 1,"autoSign": false,"freeMode": true} }]}
      - signers_psn0: {"signConfig": $signTips,"psnSignerInfo": {"psnId": "$psnId_0"},"signerType": 0,"signFields": [{"signFieldType": "0","fileId": "$fileId","normalSignFieldConfig": {"psnSealStyles": "1","autoSign": false,"freeMode": true,"signFieldStyle": 1} }]}
      - json: {"docs": [{"fileId": "$fileId","fileName": "人工审核单位实名认证授权委托书.pdf"}],"signFlowConfig": {"signFlowTitle": "流程3-企业A法人签-不指定签署位置-个人模板章-0有风险提示","autoFinish": true,"autoStart": true},"signers": [$signers_psn,$signers_faren,$signers_psn0]}
    extract:
      - flowId_3: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name:  猫猫零-获取批量签署url-【流程3】
    variables:
      - json: {
        "operatorId": $psnId_0,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_3
        ]
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
    extract:
      - batchSerialId_0_only_flowId_3: content.data.batchSerialId

- test:
    name: 获取批量签署流程列表
    variables:
      - batchSerialId: $batchSerialId_0_only_flowId_3
    api: api/batchSignV3/getBatchSignList.yml
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.assignedPosSigns", 0]  #已指定位置流程数：1
      - len_eq: ["content.data.assignedPosSealSigns", 0]  #已指定印章位置流程数：0
      - len_eq: ["content.data.freeSigns", 1]  #需指定印章位置流程数：1
      - eq: ["content.data.freeSigns.0.needRead", true] #需阅读,有签署声明

- test:
    name: "流程4-V3发起-个人设置阅读倒计时-企业不设置阅读倒计时"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs": [{"fileId": "$fileId","fileName": "人工审核单位实名认证授权委托书.pdf"}],"signFlowConfig": {"signFlowTitle": "流程4-个人设置阅读倒计时-企业不设置阅读倒计时","autoFinish": true,"autoStart": true},"signers": [{"psnSignerInfo": {"psnId": "$psnId_0"},"signerType": 0,"signConfig": {"forcedReadingTime": 10},"signFields": [{"signFieldType": "0","fileId": "$fileId","normalSignFieldConfig": {"psnSealStyles": "1","autoSign": false,"freeMode": false,"signFieldStyle": 1,"signFieldPosition": {"positionPage": "1","positionX": "300","positionY": "100"} } }]},{"orgSignerInfo": {"orgId": "$orgId_A","transactorInfo": {"psnId": "$psnId"} },"signerType": 2,"signFields": [{"signFieldType": "0","fileId": "$fileId","normalSignFieldConfig": {"signFieldStyle": 1,"autoSign": false,"freeMode": true} }]}]}
    extract:
      - flowId_4: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name:  获取批量签署url-V3
    variables:
      - json: {
        "operatorId": $psnId,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_1,$flowId_2,$flowId_3,$flowId_4,"c42352176a3f4015a122d020ae2e4d10","02d954d32d4543c0914f14d08187d2b3","71f29af130424867a933aef5e4598514","032bb28cf463450e80e393bf6cbadc07","5aa736bcff4f471e809c5c4d3752d695",
          "4d6355ec119146488ba6cbf0b8aa917a","79ec28aa7c2f4f838624fab2b9b3b685","2f7d7eb08707486589a49308e035c826","6802e05a293d4458864f25de5dd4a179"
        ]
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
#    teardown_hooks:
#      - ${teardown_re_find($response)}
    extract:
      - batchSerialId0: content.data.batchSerialId


- test:
    name: 获取批量签署流程列表-flow-manager
    variables:
      - json: {
        "batchQueryFlowConfigModels": [
        {"flowId": "$flowId_1"},
        {"flowId": "$flowId_2"},
        {"flowId": "$flowId_3"}
      ]
}
    api: api/flow-manager/batchQueryFlowConfigList.yml
    validate:
      - eq: [status_code, 200]
#      - eq: ["content.data.noSupportSigns.0.appId","7876627435"]


- test:
    name: 获取批量签署流程列表
    variables:
      - batchSerialId: $batchSerialId0
    api: api/batchSignV3/getBatchSignList.yml
#    extract:
#      - shortUrl: content.data.noSupportSigns
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.noSupportSigns",9]  #不支持批量签署
      - len_eq: ["content.data.freeSigns",3] #三个自由签署流程
      - len_eq: ["content.data.assignedPosSigns",1] #1个流程指定签署位置


- test:
    name: 获取批量签中文档页数最小的文档信息
    variables:
      - batchSerialId: $batchSerialId0
    api: api/batchSignV3/getBatchSignDocInfo.yml
#    extract:
#      - shortUrl: content.data.actorIdentityTypes
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.actorIdentityTypes",3] # 个人、企业、法人

- test:
    name: 获取批量签署印章列表-默认
    variables:
      - batchSerialId: $batchSerialId0
      - subjectId: ""
    api: api/batchSignV3/getBatchSignSeals.yml
#    extract:
#      - shortUrl: content.data.noSupportSigns
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.subjects",2] #两个主体
      - len_gt: ["content.data.organSeals",0] # 返回企业印章
      - len_gt: ["content.data.legalSeals",0] # 返回法人章

- test:
    name: 获取批量签署印章列表-个人
    variables:
      - batchSerialId: $batchSerialId0
      - subjectId: $psnId
    api: api/batchSignV3/getBatchSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.currentSubjectId",$psnId] #两个主体
      - len_gt: ["content.data.personalSeals",0] # 返回个人模板章
      - contains: ["content.data.handDrawnWay","1"] # AI手绘
      - contains: ["content.data.handDrawnWay","0"] # 普通手绘
      - contains: ["content.data.specifiedSealType","HAND_DRAW"] # 印章类型
      - contains: ["content.data.specifiedSealType","AI_HAND_DRAW"] # 印章类型
      - contains: ["content.data.specifiedSealType","TEMPLATE"] # 印章类型
      - eq: ["content.data.hasRemarkSign",true] # 包含备注签


## todo: 提交签署暂未完成
- test:
    name: 设置签名域信息
    variables:
      - batchSerialId: $batchSerialId0
      - json:  {"signFieldBeans": [{"actorIdentityType": 0,"posBean": {"addSignTime": false,"posPage": 1,"posX": 100,"posY": 683,"qrcodeSign": false,"signDateBean": null,"signDateBeanType": 0},"signType": 1},{"actorIdentityType": 2,"posBean": {"addSignTime": false,"posPage": 1,"posX": 273,"posY": 671,"qrcodeSign": false,"signDateBean": null,"signDateBeanType": 0},"signType": 1},{"actorIdentityType": 3,"posBean": {"addSignTime": false,"posPage": 1,"posX": 464,"posY": 650,"qrcodeSign": false,"signDateBean": null,"signDateBeanType": 0},"signType": 1}]}
    api: api/batchSignV3/batchSignSignPositionInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]


- test:
    name: 提交批量签署
    variables:
      - batchSerialId: $batchSerialId0
      - json: {"batchSerialId": "$batchSerialId0","personalSealIds": ["${psn_sealId}"],"subjectSealBeans": [{"sealId": "$orgIdA_sealId_0","actorIdentityType": 2,"crossEnterpriseSeal": false,"subjectId": "$orgId_A"}],"accountId": "$psnId","securityCode": ""}
    api: api/batchSignV3/batchSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.code",0]


- test:
    name: 获取签署结果
    variables:
      - batchSerialId: $batchSerialId0
    api: api/batchSignV3/getBatchSignResult.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data",0]


- test:
    name:  猫猫零-获取批量签署url-V3【流程4】
    variables:
      - json: {
        "operatorId": $psnId_0,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_4
        ]
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
    extract:
      - batchSerialId_4: content.data.batchSerialId

- test:
    name: 获取批量签署流程列表
    variables:
      - batchSerialId: $batchSerialId_4
    api: api/batchSignV3/getBatchSignList.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.assignedPosSigns.0.needRead", true] #需要阅读

- test:
    name:  桃浪-获取批量签署url-V3【流程4】
    variables:
      - json: {
        "operatorId": $psnId,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_4
        ]
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
#    teardown_hooks:
#      - ${teardown_re_find($response)}
    extract:
      - batchSerialId_5: content.data.batchSerialId

- test:
    name: 获取批量签署流程列表
    variables:
      - batchSerialId: $batchSerialId_5
    api: api/batchSignV3/getBatchSignList.yml
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.assignedPosSigns", 0]  #已指定位置流程数：0
      - len_eq: ["content.data.assignedPosSealSigns", 0]  #已指定印章位置流程数：0
      - eq: ["content.data.freeSigns.0.needRead", false] #无需阅读



- test:
    name:  桃浪-获取批量签署url-V3【流程4】-指定强制阅读
    variables:
      - json: {
        "operatorId": $psnId,
        "redirectUrl": "",
        "forcedRead": true,
        "signFlowIds": [
          $flowId_4
        ]
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
    extract:
      - batchSerialId_4_forcedRead: content.data.batchSerialId

- test:
    name: 获取批量签署流程列表
    variables:
      - batchSerialId: $batchSerialId_4_forcedRead
    api: api/batchSignV3/getBatchSignList.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.freeSigns.0.needRead", true] #强制阅读
      - eq: ["content.data.forcedRead", true] #强制阅读到底

#转交流程
#- test:
#    name: "查询流程4流程详情"
#    variables:
#      - flowId: $flowId_4
#      - queryAccountId: $psnId_0
#      - operator_id: $psnId_0
#    api: api/mobile-shield/signflows_search_V2.yml
#    validate:
#      - eq: [ status_code, 200 ]
#      - eq: [ "content.code", 0 ]
#      - eq: [ "content.message", "成功" ]
#      - len_eq: [ content.data.signDocs.0.signfields, 1 ]
#    extract:
#      - flowId4_0_signfields: content.data.signDocs.0.signfields.0.signfieldId
#
#- test:
#    name: 猫猫零-将流程4转交给未实名用户
#    variables:
#      - flowId: $flowId_4
#      - signfieldIds: [$flowId4_0_signfields]
#      - transferSourceAccountId: $psnId_0
#      - transferTargetAccountId: $psn_bfj
#    api: api/signflow/signfields/signfieldsTransfer.yml
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#      - eq: ["status_code", 200]
#
#- test:
#    name:  流程转交后-被转交人获取批量签署url-【流程4】
#    variables:
#      - json: {
#        "operatorId": $psn_bfj,
#        "redirectUrl": "",
#        "signFlowIds": [
#          $flowId_4
#        ]
#      }
#    api: api/batchSignV3/batchSignUrl.yml
#    validate:
#      - eq: [status_code, 200]
#      - len_gt: ["content.data",0]
#    extract:
#      - batchSerialId_flowId4_bfj: content.data.batchSerialId
#
#- test:
#    name: 获取批量签署流程列表
#    variables:
#      - batchSerialId: $batchSerialId_flowId4_bfj
#    api: api/batchSignV3/getBatchSignList.yml
#    validate:
#      - eq: [status_code, 200]
#      - len_eq: ["content.data.assignedPosSigns", 0]  #已指定位置流程数：0
#      - len_eq: ["content.data.assignedPosSealSigns", 0]  #已指定印章位置流程数：0
#      - eq: ["content.data.freeSigns.0.needRead", true] #强制阅读

#V1发起不指定手绘类型-后端进行兜底
- test:
    name: 分步发起流程6
    variables:
      - json: { "autoArchive": "false","businessScene": "签署${get_timestamp()}","configInfo": { "noticeType": "1","signPlatform": "1,2,3","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false },"extend": { },"initiatorAccountId": "$psn_mao0","initiatorAuthorizedAccountId": "$psn_mao0","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId_6: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 追加文件到流程
    variables:
      - flowId: $sign_flowId_6
      - docs: [ { fileId: "$fileId", fileName: "文档.pdf", filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: 添加签署区
    variables:
      - flowId: $sign_flowId_6
      - signfield_mao0: { "fileId": "$fileId","signerAccountId": "$psn_mao0","actorIndentityType": "0","authorizedAccountId": "$psn_mao0","assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "0","signType": 1 }
      - signfield_bfj: { "fileId": "$fileId","signerAccountId": "$psn_bfj","actorIndentityType": "0","authorizedAccountId": "$psn_bfj","assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "1","signType": 1 }
      - signfields: [ $signfield_mao0,$signfield_bfj]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]
      - len_eq: [ content.data.signfieldBeans, 2 ]
- test:
    name: 流程开启成功
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId_6
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name:  猫猫零获取批量签署url-【流程6】
    variables:
      - json: {
        "operatorId": $psn_mao0,
        "redirectUrl": "",
        "signFlowIds": [
          $sign_flowId_6
        ]
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
    extract:
      - batchSerialId_flowId6_mao0: content.data.batchSerialId

- test:
    name: 获取批量签署印章列表-默认
    variables:
      - batchSerialId: $batchSerialId_flowId6_mao0
      - subjectId: ""
    api: api/batchSignV3/getBatchSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.subjects",1] #两个主体
      - len_eq: ["content.data.organSeals",0] # 返回企业印章
      - ne: ["content.data.handDrawnWay",null] # 返回手绘类型，发起时未指定-后端兜底取应用配置的手绘类型

- test:
    name:  暴富君获取批量签署url-【流程6】
    variables:
      - json: {
        "operatorId": $psn_bfj,
        "redirectUrl": "",
        "signFlowIds": [
          $sign_flowId_6
        ],
        "noticeTypes": "1,2"
      }
    api: api/batchSignV3/batchSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data",0]
    extract:
      - batchSerialId_flowId6_bfj: content.data.batchSerialId

- test:
    name: 获取批量签署印章列表-默认
    variables:
      - batchSerialId: $batchSerialId_flowId6_bfj
      - subjectId: ""
    api: api/batchSignV3/getBatchSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.subjects",1] #两个主体
      - len_eq: ["content.data.organSeals",0] # 返回企业印章
      - len_gt: ["content.data.personalSeals",0] # 个人未实名返回预览章