- config:
    name: "线上问题补充集测脚本 - - 指定位置骑缝签"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_Id1: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - group: ''

- test:
    name: "一个列表-发起签署前-单个参与人二要素校验 - 1个用户，用户不存在"
    variables:
      - app_id: $app_Id1
      - json: {"name":"三四个二","uniqueId":"<EMAIL>"}
    api: api/iterate_cases/checkSingleName.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "一个列表-发起签署前-单个参与人二要素校验 - 1个用户，用户存在"
    variables:
      - app_id: $app_Id1
      - json: {"name":"王瑶济","uniqueId":"18850583991"}
    api: api/iterate_cases/checkSingleName.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "一个列表-发起签署前-单个参与人二要素校验 - 1个用户，用户不存在"
    variables:
      - app_id: $app_Id1
      - json: {"name":"0","uniqueId":"14444444444"}
    api: api/iterate_cases/checkSingleName.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]


- test:
    name: "一个列表-发起签署前-单个参与人二要素校验 - 1个用户，用户存在"
    variables:
      - app_id: $app_Id1
      - json: {"name":"陈凯丽","uniqueId":"18850583991"}
    api: api/iterate_cases/checkSingleName.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]


- test:
    name: "一个列表-发起签署前-单个参与人二要素校验 - name不传"
    variables:
      - app_id: $app_Id1
      - json: {"name":"","uniqueId":"18850583991"}
    api: api/iterate_cases/checkSingleName.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message","账号和姓名不能为空"]



- test:
    name: "一个列表-发起签署前-单个参与人二要素校验 - uniqueId不传"
    variables:
      - app_id: $app_Id1
      - json: {"name":"陈凯丽","uniqueId":""}
    api: api/iterate_cases/checkSingleName.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message","账号和姓名不能为空"]


