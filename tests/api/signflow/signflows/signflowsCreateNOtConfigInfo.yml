#def: signflowsCreateNOtConfigInfo($autoArchive, $businessScene, $contractValidity, $extend, $initiatorAccountId, $payerAccountId, $signValidity)
request:
  url: /v1/signflows
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    contractValidity: $contractValidity
    extend: $extend
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity
validate:
  - eq: [status_code, 200]



#获取上传文件url  #contentMd5, contentType, fileName, fileSize必填
