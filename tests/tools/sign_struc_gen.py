orgOid1 = '8692d7b199624b01b326cf6768bc05b6'


# 个人手动签署，支持单页、骑缝、不限位置
def gen_signfield_data_V1(fileId, flowId, posBean, signType, signerAccountId):
    actorIndentityType = 2
    autoExecute = 0
    sealId = ''
    sealType = ''
    authorizedAccountId = ''
    gen_signfield_data_V2(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType)


# 支持指定签约主体，支持手动、自动签署，支持单页、骑缝、不限位置
def gen_signfield_data_V2(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType):
    is_assignedPosbean = False
    if posBean:
        is_assignedPosbean = True
    is_assignedItem = False
    is_assignedSeal = False
    is_autoExecute = False
    if autoExecute == 1:
        is_autoExecute = True
    if not sealId:
        sealId = ""
    if sealType == 'all':
        sealType = '0,1'
    signfield = {"actorIndentityType": actorIndentityType,
                 "assignedItem": is_assignedItem,
                 "assignedPosbean": is_assignedPosbean,
                 "assignedSeal": is_assignedSeal,
                 "authorizedAccountId": authorizedAccountId,
                 "autoExecute": is_autoExecute,
                 "extendFieldDatas": {},
                 "fileId": fileId,
                 "flowId": flowId,
                 "order": 1,
                 "posBean": posBean,
                 "sealFileKey": "",
                 "sealId": sealId,
                 "sealType": sealType,
                 "signType": signType,
                 "signerAccountId": signerAccountId
                 }
    return signfield


def gen_signfield_update_data(authorizedAccountId, extendFieldDatas, posBean, sealFileKey, sealId, signfieldId):
    if not extendFieldDatas:
        extendFieldDatas = {}
    signfield = {"authorizedAccountId": authorizedAccountId,
                 "extendFieldDatas": extendFieldDatas,
                 "posBean": posBean,
                 "sealFileKey": sealFileKey,
                 "sealId": sealId,
                 "signfieldId": signfieldId}
    return signfield


# add by hupo ******** 添加参数order
# 支持指定签约主体，支持手动、自动签署，支持单页、骑缝、不限位置
def gen_signfield_data_V3(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType, order):
    is_assignedPosbean = False
    if posBean:
        is_assignedPosbean = True
    is_assignedItem = False
    is_assignedSeal = False
    is_autoExecute = False
    if autoExecute:
        is_autoExecute = True
    if not sealId:
        sealId = ""
    if sealType == 'all':
        sealType = '0,1'
    signfield = {"actorIndentityType": actorIndentityType,
                 "assignedItem": is_assignedItem,
                 "assignedPosbean": is_assignedPosbean,
                 "assignedSeal": is_assignedSeal,
                 "authorizedAccountId": authorizedAccountId,
                 "autoExecute": is_autoExecute,
                 "extendFieldDatas": {},
                 "fileId": fileId,
                 "flowId": flowId,
                 "order": order,
                 "posBean": posBean,
                 "sealFileKey": "",
                 "sealId": sealId,
                 "sealType": sealType,
                 "signType": signType,
                 "signerAccountId": signerAccountId}

    return signfield


# 支持指定签约主体，支持手动、自动签署，支持单页、骑缝、不限位置，order=1,返回签署区list
def gen_signfield_data_V4(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType, num):
    is_assignedPosbean = False
    if posBean:
        is_assignedPosbean = True
    is_assignedItem = False
    is_assignedSeal = False
    is_autoExecute = False
    if autoExecute == 1:
        is_autoExecute = True
    if not sealId:
        sealId = ""
    if sealType == 'all':
        sealType = '0,1'
    signfields = []
    range_num = range(1, num)
    for r in range_num:
        signfield = {"actorIndentityType": actorIndentityType,
                     "assignedItem": is_assignedItem,
                     "assignedPosbean": is_assignedPosbean,
                     "assignedSeal": is_assignedSeal,
                     "authorizedAccountId": authorizedAccountId,
                     "autoExecute": is_autoExecute,
                     "extendFieldDatas": {},
                     "fileId": fileId,
                     "flowId": flowId,
                     "order": 1,
                     "posBean": posBean,
                     "sealFileKey": "",
                     "sealId": sealId,
                     "sealType": sealType,
                     "signType": signType,
                     "signerAccountId": signerAccountId}
        signfields.append(signfield)
    return signfields


# 根据V2为基础修改，方便对一些字段的增删
def gen_signfield_data_V5(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType, **kwargs):
    is_assignedPosbean = False
    if posBean:
        is_assignedPosbean = True
    is_assignedItem = False
    is_assignedSeal = False
    is_autoExecute = False
    if autoExecute == 1:
        is_autoExecute = True
    if not sealId:
        sealId = ""
    if sealType == 'all':
        sealType = '0,1'

    signfield = {"actorIndentityType": actorIndentityType,
                 "assignedItem": is_assignedItem,
                 "assignedPosbean": is_assignedPosbean,
                 "assignedSeal": is_assignedSeal,
                 "authorizedAccountId": authorizedAccountId,
                 "autoExecute": is_autoExecute,
                 "extendFieldDatas": {},
                 "fileId": fileId,
                 "flowId": flowId,
                 "order": 1,
                 "posBean": posBean,
                 "sealFileKey": "",
                 "sealId": sealId,
                 "sealType": sealType,
                 "signType": signType,
                 "signerAccountId": signerAccountId
                 }
    for key, values in kwargs.items():
        if values == None and "del" in key:  # 传入带del的key，且value为None，则把这个key删除
            signfield.pop(key[:-4])
        else:
            signfield[key] = values
    return signfield


# 简化版api
def gen_signfield_update_data_simple_api(authorizedAccountId, extendFieldDatas, posBean, sealId, signfieldId):
    if not extendFieldDatas:
        extendFieldDatas = {}
    signfield = {"authorizedAccountId": authorizedAccountId,
                 "extendFieldDatas": extendFieldDatas,
                 "posBean": posBean,
                 "sealId": sealId,
                 "signfieldId": signfieldId}
    return signfield
