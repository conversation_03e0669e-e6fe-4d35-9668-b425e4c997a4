- config:
    name: "0630增加合同推送给乙方能力-此文件调用水帘洞的能力 不能并发执行"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id_origin: ${ENV(BZQ-App-Id)}
      - fileId1: ${get_file_id($app_id_origin,$path_pdf)}
      - fileId: ${get_file_id($app_id_origin,$path_pdf2)}
      - imagfileId: "5d134d1a5033458293a122bded604950"
      - sixian: "8fbe5926547e40149978fb8c5a448394"
      - sixianbzq: "cd19a02133c34c9b8379d2a0858cd31e "
      - dongying: "${ENV(orgid_dy)}"
      - mingxiu: "5e8899b894954a83af3dfd9924a10d32"
      - shenzhentiangu: "09d83d8687b74df292862c0cd8cc2e98"
      - ganning: "${ENV(gn_oid_tsign)}"
      - tengqing: "${ENV(account_id1_in_tsign)}"
      - app_id_authorized: ${ENV(app_id_for_esigntest浙江浙农实业投资有限公司)}

- test:
    name: "甲、乙分别对应A、B两个企业 甘-sixian tengqing-dongying 无序签"
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '附件1', source: 0}
      - attachments: [{fileId: $fileId, attachmentName: "附件2.pdf"}]
      - docs: [$doc]
      - copiers: [{copierAccountId: $ganning,copierIdentityAccountId: $mingxiu, copierIdentityAccountType: 1}]
      - flowInfo: {initiatorAccountId: $ganning ,initiatorAuthorizedAccountId: $shenzhentiangu, autoArchive: true, autoInitiate: true, businessScene: "********附件下载",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $ganning,
            "authorizedAccountId": $sixian
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          },

          ],
        "thirdOrderNo": "企业2"
      },
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $tengqing,
            "authorizedAccountId": $sixian
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 600,
                "posY": 600
              },
            "signDateBeanType": "2",
            "signDateBean": { "fontSize": 150,"posX": 120,"posY": 150,"posPage": "1" },
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      }

      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']


- test:
    name: "获取签署链接"
    variables:
      - app_id: $appId
      - flowId: $sign_flow_one
      - accountId: $ganning
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: 一步发起指定证书id用户手动签-成功 - 签署主体-泗县 发起主体-深圳天谷
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '附件1', source: 0}
      - attachments: [{fileId: $fileId, attachmentName: "附件2.pdf"}]
      - docs: [$doc]
      - copiers: []
      - flowInfo: {initiatorAccountId: $ganning ,initiatorAuthorizedAccountId: $sixian, autoArchive: true, autoInitiate: true, businessScene: "********附件下载",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $ganning,
            "authorizedAccountId": $dongying
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      },
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $tengqing,
            "authorizedAccountId": $sixian
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 600,
                "posY": 600
              },
            "signDateBeanType": "2",
            "signDateBean": { "fontSize": 150,"posX": 120,"posY": 150,"posPage": "1" },
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']



- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - accountId: $dongying
    api: api/seals/seals/select_p_seals.yml
    extract:
      - seal_id_dy: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询泗县印章"
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - accountId: $sixian
    api: api/seals/seals/select_p_seals.yml
    extract:
      - seal_id_sx: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询签名域"
    variables:
      - flowId: $sign_flow_one
      - app_id: ${ENV(BZQ-App-Id)}
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId1: content.data.signfields.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - accountId: $ganning
      - flowId: $sign_flow_one
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - authcode: 123456
      - accountId: $ganning
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行签名域-成功
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - flowId: $sign_flow_one
      - accountId: $ganning
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$ganning,"actorIndentityType":"2","authorizedAccountId":$dongying,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$seal_id_dy,"signType":1,"signfieldId":$signfieldId1}
      - addSignfields: []
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
