- config:
    name: "api3.0新增动态模板" #bugfix  新增的备注签署区 影响了footstone-mix的自动签署的功能
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: "**********"
      - app_id: "**********"
      - app_id: "**********"
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf: data/个人借贷合同.pdf
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US
      - app_Id1: ${ENV(wukong_forcewill)}
      - accountId: "705789a4bc6545e7b9cdc4ebadafc177"
      - fileId1: "1d5d95daa3124dbeb774b82910bcbfb8"


- test:
    name: 一步发起rpc接口-自动签署
    variables:
      - flowId: ''
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0709迭代指定附件上传"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}

      - signers: [{
                    "signerAccountId": "705789a4bc6545e7b9cdc4ebadafc177",
                    "signerAuthorizedAccountType": 1,
                    "signerAuthorizedAccountId": "705789a4bc6545e7b9cdc4ebadafc177",
                    "signOrder": 1,
                    "signfields":
                      [
                      {
                        "signerRoleType": "1",
                        "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                        "signType": 1,
                        "autoExecute": true,
                        "sealId": "9e8dcc92-07bc-4faa-8cfd-8f18dc0e9a36",
                        "posBean":
                          {
                            "posPage": "1",
                            "posX": 123.53,
                            "posY": 313.79,
                            "signDateBeanType": 2
                          }
                      }
                      ]
                  } ]
      - attachments: []
      - signValidity:
      - docs: [{
                 "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                 "encryption": 0,
                 "source": 0
               }]

    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: 查询签署区
    variables:
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: [ "status_code",200 ] #content.data.signfields.0.signerAccountId
      - eq: [ "content.data.signfields.0.statusDescription", '执行完成' ]