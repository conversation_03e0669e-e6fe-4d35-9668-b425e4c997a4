- config:
    name: "分享一期 - 有核验的签署流程"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      # 准备一个已经完成的签署流程和文件核验shareId
      - flowid1_completed: ${ENV(share_flowid_complete)}
      - flowid1_oid_no: ${ENV(share_flowid_complete_oid_no)}
      - flowid1_oid_yes: ${ENV(share_flowid_complete_oid_yes)}
      - flowid1_completed_resourceShareId: ${ENV(share_flowid_complete_resourceShareId)}
      # 准备一个进行中的签署流程和文件核验shareId
      - flowid2_processing: ${ENV(share_flowid_processing)}
      - flowid2_oid_no: ${ENV(share_flowid_processing_oid_no)}
      - flowid2_oid_yes: ${ENV(share_flowid_processing_oid_yes)}
      - flowid2_processing_resourceShareId: ${ENV(share_flowid_processing_resourceShareId)}

- test:
    name: "流程参与人查询 -signDocDetail"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_yes
      - resourceShareId: ""
      - authorizerIds: $flowid1_oid_yes
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]
      - eq: ["content.data.flowDocs.0.docStatus",2]


- test:
    name: "非流程参与人查询 - 有resourceShareId -signDocDetail"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_no
      - resourceShareId: $flowid1_completed_resourceShareId
      - authorizerIds: $flowid1_oid_no
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]
      - eq: ["content.data.flowDocs.0.docStatus",2]

- test:
    name: "非流程参与人查询 - 无resourceShareId -signDocDetail"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_no
      - resourceShareId: ""
      - authorizerIds: $flowid1_oid_no
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]
      - eq: ["content.data.flowDocs.0.docStatus",0]


- test:
    name: "流程参与人查询 -detail_V2"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_yes
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.flowStatus",2]

- test:
    name: "非流程参与人查询 - 有resourceShareId -detail_V2"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_no
      - resourceShareId: $flowid1_completed_resourceShareId
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.flowStatus",2]

- test:
    name: "非流程参与人查询 - 无resourceShareId -detail_V2"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_no
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]



- test:
    name: "流程参与人查询 -simpleconfig"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_yes
      - resourceShareId: ""
    api: api/share/simpleconfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList.0.name",查看]
      - eq: ["content.data.pageConfig.configList.0.code",see]
      - eq: ["content.data.pageConfig.configList.0.type",text]

- test:
    name: "非流程参与人查询 - 有resourceShareId -simpleconfig"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_no
      - resourceShareId: $flowid1_completed_resourceShareId
    api: api/share/simpleconfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList.0.name",查看]
      - eq: ["content.data.pageConfig.configList.0.code",see]
      - eq: ["content.data.pageConfig.configList.0.type",text]

- test:
    name: "非流程参与人查询 - 无resourceShareId -simpleconfig"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_no
      - resourceShareId: ""
    api: api/share/simpleconfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[]]



- test:
    name: "流程参与人查询 -config"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_yes
      - resourceShareId: ""
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]

- test:
    name: "非流程参与人查询 - 有resourceShareId -config"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_no
      - resourceShareId: $flowid1_completed_resourceShareId
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]

- test:
    name: "非流程参与人查询 - 无resourceShareId -config"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid1_completed
      - queryAccountId: $flowid1_oid_no
      - resourceShareId: ""
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]



- test:
    name: "流程参与人查询 -signDocDetail"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid2_processing
      - queryAccountId: $flowid2_oid_yes
      - resourceShareId: ""
      - authorizerIds: $flowid2_oid_yes
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",1]
      - eq: ["content.data.flowDocs.0.docStatus",1]


- test:
    name: "非流程参与人查询 - 有resourceShareId -signDocDetail"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid2_processing
      - queryAccountId: $flowid2_oid_no
      - resourceShareId: $flowid2_processing_resourceShareId
      - authorizerIds: $flowid2_oid_no
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437159]
      - eq: ["content.message",没有流程查询权限]



- test:
    name: "流程参与人查询 -detail_V2"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid2_processing
      - queryAccountId: $flowid2_oid_yes
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.flowStatus",1]

- test:
    name: "非流程参与人查询 - 有resourceShareId -detail_V2"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid2_processing
      - queryAccountId: $flowid2_oid_no
      - resourceShareId: $flowid2_processing_resourceShareId
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437159]
      - eq: ["content.message",没有流程查询权限]


- test:
    name: "流程参与人查询 -simpleconfig"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid2_processing
      - queryAccountId: $flowid2_oid_yes
      - resourceShareId: ""
    api: api/share/simpleconfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList.0.name",查看]
      - eq: ["content.data.pageConfig.configList.0.code",see]
      - eq: ["content.data.pageConfig.configList.0.type",text]

- test:
    name: "非流程参与人查询 - 有resourceShareId -simpleconfig"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid2_processing
      - queryAccountId: $flowid2_oid_no
      - resourceShareId: $flowid2_processing_resourceShareId
    api: api/share/simpleconfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[]]


- test:
    name: "流程参与人查询 -config"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid2_processing
      - queryAccountId: $flowid2_oid_yes
      - resourceShareId: ""
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]

- test:
    name: "非流程参与人查询 - 有resourceShareId -config"
    variables:
      - app_id: $app_Id1
      - flowId: $flowid2_processing
      - queryAccountId: $flowid2_oid_no
      - resourceShareId: $flowid2_processing_resourceShareId
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[]]









