#def: getBatchSignFlows($app_id,$accountId,$authorizerId,$batchSerialId,$signPlatform,$redirectUrl,$flowIds)
request:
  url: ${ENV(item_url)}/getBatchSignFlows/input
  method: POST
  headers: ${get_headers($app_id)}
  json:
    appId: ${ENV(X-Tsign-Open-App-Id)}
    accountId: $personOid
    authorizerId: ${ENV(authorizerId)}
    signPlatform: 5
    redirectUrl: "https://www.esign.cn/"
    flowIdList: $flowIds
validate:
  - eq: [status_code, 200]
#批量签署流程分类
