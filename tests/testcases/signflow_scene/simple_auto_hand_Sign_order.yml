- config:
    name: 简化版顺序签场景：对接平台自动签署-用户自动签署-用户手动签署
    ###def: simple_auto_hand_Sign_order()
    "request":
      "base_url": ""
      "headers":
        "Content-Type": "application/json"
    variables:
      - client_id: pc
- test:
    name: 数据准备：对个人账户1静默授权
    variables:
      - accountId: $personOid1
      - type: silent
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 数据准备：查询平台印章
    api: api/seals/seals/select_plat_seals.yml
    extract:
      - plat_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name:  数据准备：查询个人1印章
    api: api/seals/seals/select_p_seals.yml
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name:  数据准备：查询个人2印章
    api: api/seals/seals/select_p_seals.yml
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name:  数据准备：查询企业印章
    api: api/seals/seals/select_c_seals.yml
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版顺序签场景：创建流程
    variables:
      - autoArchive: False
      - businessScene: 添加签署区拆分场景$now_time
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 简化版顺序签场景：添加流程文档
    variables:
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：对接平台自动签署，顺序1
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：添加用户2手动签署，顺序2
    variables:
      - posBean: {'posPage':'1','posX':200, 'posY':600}
      - signfield: { 'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':2,  'posBean':$posBean, 'sealType':'','signType':1}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：添加用户1自动签署，顺序3
    variables:
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1,'authorizedAccountId':$personOid1, 'order':3,  'posBean':$posBean, 'sealId':$p1_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId3: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：添加企业1手动签署，顺序4
    variables:
      - posBean: {'posPage':'1-2','posX':400, 'posY':600}
      - signfield: { 'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'1', 'authorizedAccountId':$orgOid1,'assignedPosbean':True, 'order':4,  'posBean':$posBean, 'sealType':'','signType':2}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId4: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(8)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版顺序签场景：查询签署区-顺序1自动签完成，顺序2签署中
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId2, $expectedKey,1)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId3, $expectedKey,0)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId4, $expectedKey,0)}",True]

- test:
    name: 简化版顺序签场景：更新签署区
    variables:
      - posBean4: {'posPage':'1','posX':300, 'posY':600}
      - signfield: ${gen_signfield_update_data($personOid2, , $posBean4, ,$p2_sealId,$signfieldId2)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 添加签署区拆分场景：执行流程签署区
    variables:
      - signfieldIds: [$signfieldId2]
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 简化版顺序签场景：查询签署区-顺序1自动签完成，顺序2手动签署完成，顺序3自动签署完成，顺序4签署中
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
    api: api/signflow/signfields/signfieldsSelect.yml
    setup_hooks:
      - ${sleep_N_secs(3)}
    extract:
      - signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId2, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId3, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId4, $expectedKey,1)}",True]

- test:
    name: 简化版顺序签场景：更新签署区
    variables:
      - posBean4: {'posPage':'1','posX':300, 'posY':600}
      - signfield: ${gen_signfield_update_data($orgOid1, , $posBean4, ,$o_sealId,$signfieldId4)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 添加签署区拆分场景：执行流程签署区
    variables:
      - signfieldIds: [$signfieldId4]
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 添加签署区拆分场景：查询签署区-全部签署完成
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId2, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId3, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($signfields, $conditionKey,$signfieldId4, $expectedKey,4)}",True]

- test:
    name: 简化版顺序签场景：对接平台自动签署，顺序5
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':500}
      - signfield: { 'fileId':$fileId1, 'order':5,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId5: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加签署区拆分场景：归档
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加签署区拆分场景：查询流程-已归档
    api: api/signflow/signflows/signflowsSelect.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 添加签署区拆分场景：获取签署文档
    variables:
      - urlType: 0
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]