- config:
    name: "********迭代"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - account_id: ${ENV(account_id3_in_tsign)}
      - account_id1_in_tsign: ${ENV(account_id1_in_tsign)}
      - account_id2_in_tsign: ${ENV(account_id2_in_tsign)}
      - fileId: ${ENV(fileId)}
      - orgid_dy: ${ENV(orgid_dy)}
      - group:  ${ENV(envCode)}
      - phone_wang: ${ENV(phone_wang)}
      - name_wang: ${ENV(name_wang)}
      - oid_wang_tsign: ${ENV(oid_wang_tsign)}

- test:
    name: 分布发起流程
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id
      - initiatorAuthorizedAccountId: $account_id
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加个人签名域-fieldType=2，signType为0，inputType为1，发起失败
    variables:
      - flowId: $sign_flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$account_id1_in_tsign","actorIndentityType":"0","authorizedAccountId":"$account_id1_in_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"sealBizTypes":"","remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一\n八","remarkFontSize":200,"remarkFieldHeight":400,"remarkFieldWidth":400},"signDateBean":{"posPage":"1","posX":"344","posY":"464","width":300},"handDrawnWay":"0","order":1,"posBean":{"posPage":"2","posX":"444","posY":"225"},"sealType":"0","sealId":"","signType":0}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",不允许通过手绘]
      - eq: ["content.code", 1435002]

- test:
    name: 添加个人签名域-fieldType=2，signType为0，inputType为2，remarkFieldHeight==null，remarkFieldWidth!=null，发起失败
    variables:
      - flowId: $sign_flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$account_id1_in_tsign","actorIndentityType":"0","authorizedAccountId":"$account_id1_in_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"sealBizTypes":"","remarkFieldConfig":{"aiCheck":0,"inputType":2,"remarkContent":"一\n八","remarkFontSize":200,"remarkFieldWidth":400},"signDateBean":{"posPage":"1","posX":"344","posY":"464","width":300},"handDrawnWay":"0","order":1,"posBean":{"posPage":"2","posX":"444","posY":"225"},"sealType":"0","sealId":"","signType":0}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: [content.message,长度或宽度]
      - eq: ["content.code", 1435002]

- test:
    name: 添加个人签名域-fieldType=2，signType为0，inputType为2，remarkFieldHeight!=null，remarkFieldWidth==null，发起失败
    variables:
      - flowId: $sign_flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$account_id1_in_tsign","actorIndentityType":"0","authorizedAccountId":"$account_id1_in_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"sealBizTypes":"","remarkFieldConfig":{"aiCheck":0,"inputType":2,"remarkContent":"一\n八","remarkFontSize":200,"remarkFieldHeight":400},"signDateBean":{"posPage":"1","posX":"344","posY":"464","width":300},"handDrawnWay":"0","order":1,"posBean":{"posPage":"2","posX":"444","posY":"225"},"sealType":"0","sealId":"","signType":0}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: [content.message,长度或宽度]
      - eq: ["content.code", 1435002]

- test:
    name: 添加个人签名域-fieldType=2，signType为2，发起失败
    variables:
      - flowId: $sign_flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$account_id1_in_tsign","actorIndentityType":"0","authorizedAccountId":"$account_id1_in_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"sealBizTypes":"","remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一\n八","remarkFontSize":200,"remarkFieldHeight":400,"remarkFieldWidth":400},"signDateBean":{"posPage":"1","posX":"344","posY":"464","width":300},"handDrawnWay":"0","order":1,"posBean":{"posPage":"2","posX":"444","posY":"225"},"sealType":"0","sealId":"","signType":2}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",只支持单页签名/自由签署区]
      - eq: ["content.code", 1435002]

- test:
    name: 添加个人签名域-fieldType=2，signType为0，inputType为2，发起成功
    variables:
      - flowId: $sign_flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$account_id1_in_tsign","actorIndentityType":"0","authorizedAccountId":"$account_id1_in_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"sealBizTypes":"","remarkFieldConfig":{"aiCheck":0,"inputType":2,"remarkContent":"一\n八","remarkFontSize":200,"remarkFieldHeight":400,"remarkFieldWidth":400},"signDateBean":{"posPage":"1","posX":"344","posY":"464","width":300},"handDrawnWay":"0","order":1,"posBean":{"posPage":"2","posX":"444","posY":"225"},"sealType":"0","sealId":"","signType":0}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",成功]
      - eq: ["content.code", 0]
      - len_eq: [content.data.signfieldBeans.0.signfieldId, 32]

- test:
    name: 同一个主体添加多个自由备注签，长宽字体不一致
    variables:
      - flowId: $sign_flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$account_id1_in_tsign","actorIndentityType":"0","authorizedAccountId":"$account_id1_in_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"sealBizTypes":"","remarkFieldConfig":{"aiCheck":0,"inputType":2,"remarkContent":"一\n八","remarkFontSize":100,"remarkFieldHeight":500,"remarkFieldWidth":500},"signDateBean":{"posPage":"1","posX":"344","posY":"464","width":300},"handDrawnWay":"0","order":1,"posBean":{"posPage":"2","posX":"444","posY":"225"},"sealType":"0","sealId":"","signType":0}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",成功]
      - eq: ["content.code", 0]
      - len_eq: [content.data.signfieldBeans.0.signfieldId, 32]

- test:
    name: 未传入字号，发起成功
    variables:
      - flowId: $sign_flowId
      - json: {"signfields":[{"fileId":"$fileId","forceReadTime":0,"signerAccountId":"$account_id2_in_tsign","actorIndentityType":"0","authorizedAccountId":"$account_id2_in_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"sealBizTypes":"","remarkFieldConfig":{"aiCheck":0,"inputType":2},"signDateBean":{"posPage":"1","posX":"344","posY":"464","width":300},"handDrawnWay":"0","order":1,"posBean":{"posPage":"2","posX":"444","posY":"225"},"sealType":"0","sealId":"","signType":0}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",成功]
      - eq: ["content.code", 0]
      - len_eq: [content.data.signfieldBeans.0.signfieldId, 32]

- test:
    name: 长宽字体只取第一组
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $account_id1_in_tsign
      - operatorid: $account_id1_in_tsign
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: [content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkFontSize, 200]
      - eq: [content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkFieldHeight, 400]
      - eq: [content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkFieldWidth, 400]
      - eq: [content.data.signDocs.0.signfields.1.remarkFieldConfig.remarkFontSize, 100]
      - eq: [content.data.signDocs.0.signfields.1.remarkFieldConfig.remarkFieldHeight, 500]
      - eq: [content.data.signDocs.0.signfields.1.remarkFieldConfig.remarkFieldWidth, 500]

- test:
    name: 未传入字号时默认值为14
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $account_id2_in_tsign
      - operatorid: $account_id2_in_tsign
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: [content.data.signDocs.0.signfields.2.remarkFieldConfig.remarkFontSize, 14]
      - str_eq: [content.data.signDocs.0.signfields.2.remarkFieldConfig.remarkFieldHeight, None]
      - str_eq: [content.data.signDocs.0.signfields.2.remarkFieldConfig.remarkFieldWidth, None]

- test:
    name: 开启流程
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 转交备注签署区-手机号跟姓名不匹配
    variables:
      - json: {"flowId":"$sign_flowId","originalAccountId":"$account_id2_in_tsign","transferToAccount":"$phone_wang","transferToAccountName":"张三"}
    api: api/v3/remark-signfields/transfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1439004]
      - eq: ["content.message", 请输入正确的姓名和账号]

- test:
    name: 转交备注签署区-手机号为空
    variables:
      - json: {"flowId":"$sign_flowId","originalAccountId":"$account_id2_in_tsign","transferToAccountName":"$name_wang"}
    api: api/v3/remark-signfields/transfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", transferToAccount不能为空]

- test:
    name: 转交备注签署区-姓名为空
    variables:
      - json: {"flowId":"$sign_flowId","originalAccountId":"$account_id2_in_tsign","transferToAccount":"$phone_wang"}
    api: api/v3/remark-signfields/transfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", transferToAccountName不能为空]

- test:
    name: 转交备注签署区
    variables:
      - json: {"flowId":"$sign_flowId","originalAccountId":"$account_id2_in_tsign","transferToAccount":"$phone_wang","transferToAccountName":"$name_wang"}
    api: api/v3/remark-signfields/transfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 转交接收人正确
    variables:
      - flowId: $sign_flowId
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.1.signerAccountId", $oid_wang_tsign]
      - eq: ["content.data.signers.1.signerAuthorizedAccountId", $oid_wang_tsign]
      - eq: ["content.data.signers.1.signerAccount", $phone_wang]
      - eq: ["content.data.signers.1.signerName", $name_wang]