#!/usr/bin/env python
# -- coding: utf-8 --
# <AUTHOR> gan
# @File : hrunnerToJson.py
import json


def toJson(stra):
    cont = stra.replace("'", '"').replace("False", "false").replace("True", "true").replace("\n", "").replace("None",
                                                                                                              "null")
    jsonD = json.loads(cont)
    res = json.dumps(jsonD, sort_keys=True, indent=4, separators=(', ', ': '), ensure_ascii=False)
    print(res)


if __name__ == '__main__':
    inr = """ {'code': 0, 'message': '成功', 'data': {'sealLimited': False, 'personalHandSign': False, 'personalHandDrawnText': None, 'personalSeals': [], 'officialSeals': [{'sealLimited': False, 'authorizerId': '69953b56a4d64b439e94dc28de0b4aae', 'organizationId': '8fbe5926547e40149978fb8c5a448394', 'organizationName': '泗县深泉纯净水有限公司', 'realname': True, 'realnameStatus': None, 'organSeals': [{'thirdSeal': False, 'sealId': 'bd898cc7-84c8-48bd-8cd0-0156c6818ede', 'sealFileKey': '$453993d9-13b5-4e36-8323-b84963d0e294$3196690302', 'sealAlias': '米团公章', 'sealUrl': 'https://ttapi.tsign.cn/openwebserver/v2/seal?sealId=bd898cc7-84c8-48bd-8cd0-0156c6818ede&content=%E7%8E%8B%E7%91%B6%E6%B5%8E', 'width': 159, 'height': 159, 'ifDefault': False, 'sealBizType': 'COMMON', 'sealUserFlag': True, 'sealType': 1, 'crossEnterpriseSeal': False, 'ownerName': None}, {'thirdSeal': False, 'sealId': 'd0cfc838-0d4d-4e04-a69e-c2c7c44fdcc8', 'sealFileKey': '$4480c04b-2c53-444d-89a3-125443ea0955$1512077845', 'sealAlias': '测试3333333', 'sealUrl': 'https://ttapi.tsign.cn/openwebserver/v2/seal?sealId=d0cfc838-0d4d-4e04-a69e-c2c7c44fdcc8&content=%E7%8E%8B%E7%91%B6%E6%B5%8E', 'width': 159, 'height': 159, 'ifDefault': False, 'sealBizType': 'COMMON', 'sealUserFlag': True, 'sealType': 1, 'crossEnterpriseSeal': False, 'ownerName': None}], 'legalSeals': []}, {'sealLimited': False, 'authorizerId': '69953b56a4d64b439e94dc28de0b4aae', 'organizationId': '8fbe5926547e40149978fb8c5a448394', 'organizationName': '泗县深泉纯净水有限公司', 'realname': True, 'realnameStatus': 2, 'organSeals': [], 'legalSeals': [{'thirdSeal': False, 'sealId': '71d81e33-18d9-4cea-8da9-6e50c43e8ebc', 'sealFileKey': '$9a2c054a-ca37-4722-9925-16a89084e533$2699798813', 'sealAlias': '钟灵-二级-法人章', 'sealUrl': 'https://ttapi.tsign.cn/openwebserver/v2/seal?sealId=71d81e33-18d9-4cea-8da9-6e50c43e8ebc&content=%E7%8E%8B%E7%91%B6%E6%B5%8E', 'width': 95, 'height': 95, 'ifDefault': False, 'sealBizType': 'LEGAL_PERSON', 'sealUserFlag': True, 'sealType': 2, 'crossEnterpriseSeal': False, 'ownerName': None}, {'thirdSeal': False, 'sealId': 'f6f75810-b80c-43e0-afb4-95c1f78d4b04', 'sealFileKey': '$91f9f6de-881e-49e5-bbea-eab4dfe72393$907708175', 'sealAlias': '测试11133444', 'sealUrl': 'https://ttapi.tsign.cn/openwebserver/v2/seal?sealId=f6f75810-b80c-43e0-afb4-95c1f78d4b04&content=%E7%8E%8B%E7%91%B6%E6%B5%8E', 'width': 136, 'height': 136, 'ifDefault': False, 'sealBizType': 'LEGAL_PERSON', 'sealUserFlag': True, 'sealType': 2, 'crossEnterpriseSeal': False, 'ownerName': None}, {'thirdSeal': False, 'sealId': 'c9c90b78-2c1a-412b-8f52-d5035864de3e', 'sealFileKey': '$b70660cc710246b082b6005efdbc8a3b$3305870998$H', 'sealAlias': '法人1', 'sealUrl': 'https://ttapi.tsign.cn/openwebserver/v2/seal?sealId=c9c90b78-2c1a-412b-8f52-d5035864de3e&content=%E7%8E%8B%E7%91%B6%E6%B5%8E', 'width': 136, 'height': 136, 'ifDefault': False, 'sealBizType': 'LEGAL_PERSON', 'sealUserFlag': False, 'sealType': 2, 'crossEnterpriseSeal': False, 'ownerName': None}, {'thirdSeal': False, 'sealId': '2d421dda-5ee1-451f-bd85-7316697c40fc', 'sealFileKey': '$cf9af819-4ae8-4615-aa33-ea0d6dc9a71c$2132377462', 'sealAlias': '测试333334', 'sealUrl': 'https://ttapi.tsign.cn/openwebserver/v2/seal?sealId=2d421dda-5ee1-451f-bd85-7316697c40fc&content=%E7%8E%8B%E7%91%B6%E6%B5%8E', 'width': 95, 'height': 95, 'ifDefault': False, 'sealBizType': 'LEGAL_PERSON', 'sealUserFlag': True, 'sealType': 2, 'crossEnterpriseSeal': False, 'ownerName': None}]}], 'authSeals': None}}


"""
    toJson(inr)
