- config:
    name: "个人印章相关的各种操作"
    ###def: p_seal_action($idNo,$idType,$name,$color,$height,$width,$sealAlias,$templateType,$text,$p_seal_path,$p_fileName) #,$contentType,$contentMd5)
    variables:
      - fileSize: ${get_file_size($p_seal_path)}
    #        - contentMd5: ${get_file_base64_md5($p_seal_path)}
    #        - contentType: "image/jpeg"
    request:
      base_url: "${ENV(footstone_api_url)}"

- test:
    name: "创建一个新账号"
    api: api/user/accounts/create_accounts.yml
    extract:
      - accountId: content.data.accountId
    #    teardown_hooks:
    #        - ${teardown_hook_sleep_N_secs($response, 5)}   #创建账号内含的创建印章去掉了，由上层自己去调用创建印章
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

        #- test:
        #    name: "查询个人默认印章"
        #    api: api/seals/seals/select_p_seals.yml
        #    extract:
        #        - seals: content.data.seals
        #    validate:
      - eq: ["content.data.total",1]
      - eq: ["content.data.seals.0.defaultFlag",true]

- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    extract:
      - sealId: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: "查询个人印章列表"
    api: api/seals/seals/select_p_seals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.total",1]


- test:
    name: 获取上传文件url
    api: api/file_template/files/get_uploadUrl.yml
    variables:
      - fileSize: ${get_file_size($p_seal_path)}
      - contentMd5: ${get_file_base64_md5($p_seal_path)}
      - contentType: "image/jpeg"
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 上传文件到oss
    variable_binds:
      - binary: ${open_file($p_seal_path)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Md5: ${get_file_base64_md5($p_seal_path)}
        Content-Type: "image/jpeg"
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: "创建个人图片印章"
    api: api/seals/seals/create_p_image_seal.yml
    variables:
      - sealType: 3
      - type: FILEKEY
    extract:
      - sealId: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: "查询个人印章列表"
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealId1: content.data.seals.0.sealId
      - sealId2: content.data.seals.1.sealId
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.seals",2]

- test:
    name: "清除个人印章"
    api: api/seals/seals/delete_p_seal.yml
    parameters:
      - sealId:
      - $sealId1
      - $sealId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "注销上面创建的账号"
    api: api/user/accounts/delete_accounts.yml

- test:
    name: "注销后查询不到账号"
    api: api/user/accounts/get_accounts.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","账号不存在"]
      - eq: ["content.data",null]