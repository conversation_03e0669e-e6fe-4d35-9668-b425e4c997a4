#def: addUpdateExecute_sign($flowId,$addSignfields,$updateSignfields,$signfieldIds,$approvalPolicy,$dingUser,$accountId)
request:
  url: ${ENV(item_url)}/v3/signflows/$flowId/signfields/add-Update-Execute
  method: PUT
  headers: ${get_headers($app_id)}
  json:
    accountId: $accountId
    addSignfields: $addSignfields
    updateSignfields: $updateSignfields
    signfieldIds: $signfieldIds
    approvalPolicy: $approvalPolicy
    dingUser: $dingUser

# 同步添加或更新签署区并执行签署