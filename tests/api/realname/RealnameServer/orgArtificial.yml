#def: orgArtificial($name,$codeUSC,$codeREG,$codeORG,legalName,legalIdno,legalMobile,agentMobile)
request:
  url: /orgArtificial/model
  method: POST
  json:
    name: $name
    codeUSC: $codeUSC
    codeREG: $codeREG
    codeORG: $codeORG
    address: '浙江省杭州市西湖区西斗门路3号天堂软件园D幢19楼'
    openTo: '2022-12-17'
    bizLicensePhoto: 'x'
    orgCodeCertPhoto: 'x'
    taxRegCertPhoto: 'x'
    legalName: $legalName
    legalIdno: $legalIdno
    legalMobile: $legalMobile
    legalCertType: 19
    legalCertFront: 'x'
    legalCertBack: 'x'
    legalCertBegin: "2018-01-01"
    legalCertEnd: "2028-01-01"
    legalNationality: "1"
    legalHoldCertFront: "x"
    legalHoldCertBack: "x"
    agentName: "李俊涛"
    agentIdno: "429006198904073095"
    agentCertType: "19"
    agentMobile: $agentMobile
    agentCertFront: "x"
    agentCertBack: "x"
    agentHoldCertFront: "x"
    agentHoldCertBack: "x"
    proxyPhoto: "x"
    operatorType: "x"
    dataSource: "dataSource"

