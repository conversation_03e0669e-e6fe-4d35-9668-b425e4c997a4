#对开放服务补充的接口用例

#个人账号创建
- api:
    def: create_account($name,$idNumber,$mobile,$email,$thirdPartyUserId)
    request:
      url: /v1/accounts
      method: POST
      json:
        name: $name
        idNumber: ${get_idNo()}
        idType: CRED_PSN_CH_IDCARD
        mobile: $mobile
        email: $email
        thirdPartyUserId: ${get_randomNo()}

#按照证件类型修改个人账号
- api:
    def: updateByIdNumber($name,$idNumber,$idType,$mobile,$email,$thirdPartyUserId)
    request:
      url: /v1/accounts/updateByIdNumber?idNumber=$idNumber&idType=$idType
      method: POST
      json:
        name: $name
        mobile: $mobile
        email: $email
        thirdPartyUserId: $thirdPartyUserId

#按照第三方用户ID修改个人账号
- api:
    def: updateByThirdId($name,$mobile,$email,$thirdPartyUserId)
    request:
      url: /v1/accounts/updateByThirdId?thirdPartyUserId=$thirdPartyUserId
      method: POST
      json:
        name: $name
        mobile: $mobile
        email: $email

#按照证件号查询个人账号
- api:
    def: getByIdNumber($idNumber,$idType)
    request:
      url: /v1/accounts/getByIdNumber?idNumber=$idNumber&idType=$idType
      method: GET
      json:
        idNumber: $idNumber
        idType: $idType

#按照第三方用户ID查询个人账号
- api:
    def: getByThirdId($thirdPartyUserId)
    request:
      url: /v1/accounts/getByThirdId?thirdPartyUserId=$thirdPartyUserId
      method: GET
      json:
        thirdPartyUserId: $thirdPartyUserId

#按照按照证件号注销个人账号
- api:
    def: deleteByIdNumber($idNumber,$idType)
    request:
      url: /v1/accounts/deleteByIdNumber?idNumber=$idNumber&idType=$idType
      method: DELETE
      json:
        idNumber: $idNumber
        idType: $idType

#按照证件号修改企业账号
- api:
    def: updateByIdNumber_ORG($idNumber,$idType,$name)
    request:
      url: /v1/organizations/updateByIdNumber?idNumber=$idNumber&idType=$idType
      method: POST
      json:
        name: $name

#按照第三方用户ID修改企业账号
- api:
    def: updateByThirdId_ORG($thirdPartyUserId,$name)
    request:
      url: v1/organizations/updateByThirdId?thirdPartyUserId=$thirdPartyUserId
      method: POST
      json:
        name: $name

#按照证件号查询企业账号
- api:
    def: getByIdNumber_ORG($idNumber,$idType)
    request:
      url: /v1/organizations/getByIdNumber?idNumber=$idNumber&idType=$idType
      method: GET
      json:
        idNumber: $idNumber
        idType: $idType

#按照第三方用户ID查询企业账号
- api:
    def: getByThirdId_ORG($thirdPartyUserId)
    request:
      url: /v1/organizations/getByThirdId?thirdPartyUserId=$thirdPartyUserId
      method: GET

#按照证件号注销企业账号
- api:
    def: deleteByIdNumber_ORG($idNumber,$idType)
    request:
      url: /v1/organizations/deleteByIdNumber?idNumber=$idNumber&idType=$idType
      method: DELETE
      json:
        idNumber: $idNumber
        idType: $idType

#按照第三方用户ID注销企业账号
- api:
    def: deleteByThirdId_ORG($thirdPartyUserId)
    request:
      url: /v1/organizations/deleteByThirdId?thirdPartyUserId=$thirdPartyUserId
      method: DELETE
