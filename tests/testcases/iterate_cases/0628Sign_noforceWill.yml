- config:
    name: "0628接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf: data/个人借贷合同.pdf
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US
      - app_Id1: ${ENV(wukong_forcewill)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}

- test:
    name: "创建签署人账号"
    variables:
      - thirdPartyUserId: fengjiu201dfgfjk34556991321914
      - name: 曾艳
      - idNumber: 362324199301130629
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId: content.data.accountId


- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:

      - accountId: $accountId
      - color: "RED"
      - height: 2
      - alias: "某某的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: 一步发起
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 查询流程配置
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.data.coerciveWill",False]

- test:
    name: 获取实名地址场景：悟空实名：获取个人实名地址
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
        # 为什么要判断data.type = 0
#      - eq: ["content.data.type", "0"]

- test:
    name: 发起实名流程
    variables:
      - accountId: $accountId
      - name: 曾艳
      - notifyUrl: www.baidu.com
      - mobileNo: ***********
    extract:
      - realnameflowId: content.data.flowId
    api: api/realname/realname/telecom3Factors.yml
    validate:
      - eq: [status_code, 200]


- test:
    name: 实名验证码
    variables:
      - flowId: $realnameflowId
      - authcode: 123456

    api: api/realname/realname/telecom3FactorsCode.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: verifycode
    variables:
      - flowId: $realnameflowId
      - authcode: 123456

    api: api/realname/verifyCode.yml
    extract:
      - verifycode: content.data.verifyCode
    validate:
      - eq: [status_code, 200]

#意愿认证
- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $accountId

      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $accountId
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 执行签署
    variables:
      - securityCode: $verifycode
      - flowId: $sign_flowId1
      - accountId: $accountId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfields: [{"actorIndentityType": 0, "assignedItem": false, "assignedPosbean": true, "assignedSeal": false, "authorizedAccountId": $accountId, "autoExecute": false, "extendFieldDatas": {}, "fileId": $fileId1, "flowId": $sign_flowId1, "order": 1, "posBean": {"addSignTime": true, "key": "", "posPage": "1", "posX": 400, "posY": 200, "qrcodeSign": true, "width": 0}, "sealFileKey": "", "sealId": $sealId_p1, "sealType": 1, "signType": 1, "signerAccountId": $accountId}]

      - updateSignfields: []
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $accountId
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_appid_async_code.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
