- config:
    name: 通过文件创建签署流程
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - file_id: ${ENV(file_id_in_app_id_tengqing_SaaS_with_willing)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - account_id_2_tsign: ${ENV(account_id2_in_tsign)}
      - account_id_3_tsign: ${ENV(account_id3_in_tsign)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - org_id_1: ${ENV(orgid_dy)}
      - org_id_2: ${ENV(orgid_sx)}
      - phone_1: ${ENV(phone_1)}
      - phone_2: ${ENV(phone_2)}
      - phone_3: ${ENV(phone_3)}

- test:
    name: "个人发起方为企业账号"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"${notifyUrl}","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"psnInitiator":{"psnId":$account_id_dy_tsign}},"signers":[{"noticeConfig":{"noticeTypes":"1,2,3,4,5,6,7"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 个人发起方账号不能为企业账号]

- test:
    name: "个人发起方为空"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"psnInitiator":{"psnId":null}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, psnId不能为空]

- test:
    name: "同时传了个人发起和企业发起方"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(30)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"psnInitiator":{"psnId":$account_id_2_tsign}, "orgInitiator": {"orgId": $account_id_dy_tsign}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 个人发起方和机构发起方不能同时传值]

- test:
    name: "企业方发起，transactor为空"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(30)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 机构发起方orgId与transactor不能为空]

- test:
    name: "企业方发起，signFields为空-发起失败"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signerType":0}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, signFields不能为空]

- test:
    name: "企业方发起，transactor 非空"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":false,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "一步发起接口传入企业oid+经办人手机号，及个人签手机号与企业经办人相同发起拦截"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1,2,3,4","showBatchDropSealButton":true},"signFlowTitle":"bug重现"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_2_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnAccount":"${phone_2}"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType":0,"signTipsContent":"","signTipsTitle":""},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":true,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"ALL","positionPage":"1","positionX":400,"positionY":200},"signFieldSize":0,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":12,"showSignDate":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":""},"orgSignerInfo":{"orgId":"${account_id_sx_tsign}","transactorInfo":{"psnAccount":"${phone_2}"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 机构签署方与机构签署经办人传入必须为orgId与psnId或orgName与psnAccount]

- test:
    name: "企业签名域添加企业oid，不传经办人-无法追加"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${org_id_1}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code,  1435002]
      - contains: [content.message, "非自动签签署机构签署方经办人transactorInfo不能为空"]

- test:
    name: "企业签名域企业名称，不传经办人-无法追加"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgName":"${ENV(xuanyuan_sixian_name)}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code,  1435002]
      - contains: [content.message, "机构签署方orgName非空时transactorInfo不能为空"]

- test:
    name: "流程延期-小于当前时间"
    api: api/sign-flow/delay.yml
    variables:
      flowId: $flowId
      json: {"signFlowExpireTime": "${get_timestamp(0)}"}
    validate:
      - eq: [content.code, 1437202]
      - contains: [content.message, 签署截止时间不能小于当前时间]

- test:
    setup_hooks:
      - ${sleep_N_secs(3)}
    name: "流程延期-成功"
    api: api/sign-flow/delay.yml
    variables:
      flowId: $flowId
      json: {"signFlowExpireTime": "${get_timestamp(20)}"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "流程延期-重复延期"
    api: api/sign-flow/delay.yml
    variables:
      flowId: $flowId
      json: {"signFlowExpireTime": "${get_timestamp(10)}"}
    validate:
      - eq: [content.code, 1435011]
      - contains: [content.message, 流程只能延期一次]

- test:
    name: "催签-流程开启后半小时不能催办"
    api: api/sign-flow/urge.yml
    variables:
      flowId: $flowId
      json: {"noticeTypes":"","urgedOperator":{"psnAccount":"","psnId":""}}
    validate:
      - eq: [content.code, 1437137]
      - eq: [content.message, '您刚刚催办过了，发起后半小时内不能催办']

- test:
    name: "查询流程详情"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    extract:
      - signFieldId: content.data.signers.0.signFields.0.signFieldId
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]

- test:
    name: "删除文档-流程已开启，无法删除"
    api: api/sign-flow/unsigned-files-delete.yml
    variables:
      flowIds: $flowId
      fileIds: $file_id,
    validate:
      - eq: [content.code, 1437505]
      - contains: [content.message, 不允许删除]

- test:
    name: "添加文档-流程已开启，无法添加"
    api: api/sign-flow/unsigned-files-add.yml
    variables:
      flowId: $flowId
      json: {"unsignedFiles":[{"fileEditPwd":"","fileId":"$file_id","fileName":"追加的文件7","neededPwd":false}]}
    validate:
      - eq: [content.code, 1437502]
      - contains: [content.message, 不允许添加文档]

- test:
    name: "删除附件-流程已开启，无法删除"
    api: api/sign-flow/attachments-delete.yml
    variables:
      flowId: $flowId
      fileIds: $file_id
    validate:
      - eq: [content.code, 1437405]
      - contains: [content.message, 不允许删除附件]

- test:
    name: "添加附件-无法重复添加附件"
    api: api/sign-flow/attachments-add.yml
    variables:
      flowId: $flowId
      json: {"attachmentList":[{"fileId":"$file_id"}]}
    validate:
      - eq: [content.code, 1437404]
      - contains: [content.message, 流程已添加附件]

- test:
    name: "下载流程文件"
    api: api/sign-flow/file-download-url.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [content.code, 1437518]
      - contains: [content.message, 不允许下载文档]

- test:
    name: "撤回流程"
    api: api/sign-flow/revoke.yml
    variables:
      flowId: $flowId
      json: {"revokeReason": "<>/|:\"*?一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "查询流程详情-撤回原因"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]
      - eq: [content.data.revokeReason, "<>/|:\"*?一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二"]

- test:
    name: "signFlowInitiator 为空，为平台方发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":false,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(30)}","signFlowTitle":"签署${get_timestamp()}"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "查询流程详情"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    extract:
      - signFieldId: content.data.signers.0.signFields.0.signFieldId
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]

- test:
    name: "催签-流程未开启不能催签"
    api: api/sign-flow/urge.yml
    variables:
      flowId: $flowId
      json: {"noticeTypes":"","urgedOperator":{"psnAccount":"","psnId":""}}
    validate:
      - eq: [content.code, 1437126]
      - eq: [content.message, '流程未发起或已完结, 不能执行催签操作']

- test:
    name: "删除签名域-成功"
    api: api/sign-flow/sign-fields-delete.yml
    variables:
      flowId: $flowId
      signFieldIds: $signFieldId
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]

- test:
    name: "删除文档"
    api: api/sign-flow/unsigned-files-delete.yml
    variables:
      flowId: $flowId
      fileIds: $file_id,
    validate:
      - eq: [content.code, 0]
      - eq: [content.message, 成功]

- test:
    name: "添加文档-没传密码"
    api: api/sign-flow/unsigned-files-add.yml
    variables:
      flowId: $flowId
      json: {"unsignedFiles":[{"fileEditPwd":"","fileId":"$file_id","fileName":"追加的文件7","neededPwd":true}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 文档加密密码不能为空]

- test:
    name: "添加文档-成功"
    api: api/sign-flow/unsigned-files-add.yml
    variables:
      flowId: $flowId
      json: {"unsignedFiles":[{"fileEditPwd":"","fileId":"$file_id","fileName":"追加的文件7","neededPwd":false}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "删除附件-成功"
    api: api/sign-flow/attachments-delete.yml
    variables:
      flowId: $flowId
      fileIds: $file_id
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "添加附件-成功"
    api: api/sign-flow/attachments-add.yml
    variables:
      flowId: $flowId
      json: {"attachmentList":[{"fileId":"$file_id"}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "流程延期-草稿状态不能延期"
    api: api/sign-flow/delay.yml
    variables:
      flowId: $flowId
      json: {"signFlowExpireTime": "${get_timestamp(10)}"}
    validate:
      - eq: [content.code, 1437201]
      - contains: [content.message, 非签署中或者非过期的的流程不能延期]

- test:
    name: "开启流程-没有签署区不能开启"
    api: api/sign-flow/start.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [content.code, 1437110]
      - contains: [content.message, 流程没有签署区不允许开启流程]

- test:
    name: "关闭流程-非开启状态不允许归档流程"
    api: api/sign-flow/finish.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [content.code, 1437112]
      - contains: [content.message, 非开启状态不允许归档流程]

- test:
    name: "流程列表"
    api: api/sign-flow/sign-flow-list.yml
    variables:
      json: {"operator":{"psnAccount":"","psnId":""},"organization":{"orgId":"${account_id_2_tsign}","orgName":""},"pageNum":1,"pageSize":100,"signFlowStartTimeFrom":"${get_timestamp()}","signFlowStartTimeTo":"${get_timestamp(10)}","signFlowStatus":[]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "添加签名域-签名域为空"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${account_id_1_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signerType":0}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, signFields不能为空]

- test:
    name: "添加签名域"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${account_id_1_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - str_eq: [content.data.0.orgId, None]
      - str_eq: [content.data.0.orgName, None]
      - str_eq: [content.data.0.psnAccount, None]
      - str_eq: [content.data.0.psnId, "${account_id_1_tsign}"]

- test:
    name: "开启流程-成功"
    api: api/sign-flow/start.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "获取意愿验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      accountId: $account_id_1_tsign
      flowId: $flowId
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "校验验证码"
    api: api/realname/realname/checkSmsa.yml
    variables:
      accountId: $account_id_1_tsign
      bizId: $bizId
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - eq: [content.data.passed, True]

- test:
    name: "获取签署区信息"
    api: api/mobile-shield/signflows_search_signfields.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
    extract:
      - signfieldId: content.data.signfields.0.signfieldId

- test:
    name: "获取可用印章信息"
    api: api/signflow/signflows/getSignSeals.yml
    variables:
      flowId: $flowId
      loginAccountId: $account_id_1_tsign
      signerAccountId: $account_id_1_tsign
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
    extract:
      - personalSealId: content.data.personalSeals.0.sealId
      - personalSealFileKey: content.data.personalSeals.0.sealFileKey

- test:
    name: "签署流程"
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    variables:
      flowId: $flowId
      accountId: $account_id_1_tsign
      addSignfields: []
      updateSignfields: [{"sealId":"$personalSealId","sealFileKey":"$personalSealFileKey","signfieldId":"$signfieldId","actorIndentityType":0,"assignedItem":false,"assignedPosbean":true,"assignedSeal":false,"authorizedAccountId":"$account_id_1_tsign","autoExecute":false,"signDateBeanType":1,"extendFieldDatas":{},"fileId":"$file_id","order":1,"posBean":{"addSignTime":false,"posPage":"1","posX":87,"posY":454,"qrcodeSign":false,"width":200,"signDateBean":{"posPage":"1","fontName":"simsun","format":"yyyy年MM月dd日","fontSize":12,"posX":157,"posY":476},"sealType":1,"signType":1,"signerAccountId":"$account_id_1_tsign"}}]
      signfieldIds: [$signfieldId]
      approvalPolicy: 0
      dingUser: {}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    setup_hooks:
      - ${sleep_N_secs(5)}
    name: "关闭流程-自动归档-已归档"
    api: api/sign-flow/finish.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [content.code, 1437136]
      - contains: [content.message, 流程已归档]

- test:
    name: "获取签署链接"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":"$account_id_1_tsign"},"redirectConfig":{"redirectDelayTime":0,"redirectUrl":"http://www.alibaba.com"},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "RPC接口获取签署链接"
    api: api/getSignPageUrl/signPageUrlInput.yml
    variables:
      json: {"accountId":"${account_id_1_tsign}","appScheme":"","authorizedAccountId":"${account_id_1_tsign}","clientType":"ALL","flowId":"${flowId}","needLogin":false,"redirectDelayTime":3,"redirectUrl":""}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "添加文档-没传密码"
    api: api/sign-flow/unsigned-files-add.yml
    variables:
      flowId: $flowId
      json: {"unsignedFiles":[{"fileEditPwd":"","fileId":"$file_id","fileName":"追加的文件7","neededPwd":true}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 文档加密密码不能为空]

- test:
    name: "发起用户手动签"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_dy_tsign}","transactorInfo":{"psnId":"${account_id_1_tsign}"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "根据发起人查询流程列表"
    api: api/sign-flow/sign-flow-list.yml
    variables:
      json: {"initiator":{"initiatorPsnId":"${account_id_1_tsign}"},"operator":{"psnAccount":"","psnId":""},"organization":{"orgId":"${account_id_2_tsign}","orgName":""},"pageNum":1,"pageSize":100,"signFlowStartTimeFrom":"${get_timestamp()}","signFlowStartTimeTo":"${get_timestamp(10)}","signFlowStatus":[]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "获取签署链接-操作人为无关的人"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":"$account_id_2_tsign"},"organization":{"orgId":"${account_id_sx_tsign}"},"redirectConfig":{},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 1437114]
      - contains: [content.message, "当前用户不是流程参与人, 无权查看流程"]

- test:
    name: "获取签署链接-operator传签署人oid&organization传签署主体oid"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":"$account_id_1_tsign"},"organization":{"orgId":"${account_id_dy_tsign}"},"redirectConfig":{},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

- test:
    name: "获取签署链接-operator不传&organization传签署主体oid"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":""},"organization":{"orgId":"${account_id_dy_tsign}"},"redirectConfig":{},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 1437114]
      - contains: [content.message, "当前用户不是流程参与人, 无权查看流程"]

- test:
    name: "获取签署链接-operator传签署oid&organization不传"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":"$account_id_2_tsign"},"organization":{"orgId":null},"redirectConfig":{},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 1437114]
      - contains: [content.message, "当前用户不是流程参与人, 无权查看流程"]

- test:
    name: "创建流程"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"signFlowConfig":{"autoFinish":true,"autoStart":false,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"$notifyUrl","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}}}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [content.code, 0]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "添加文档-成功"
    api: api/sign-flow/unsigned-files-add.yml
    variables:
      flowId: $flowId
      json: {"unsignedFiles":[{"fileEditPwd":"","fileId":"$file_id","fileName":"追加的文件7","neededPwd":false}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "添加企业签名域-传了企业名称，经办人传了手机号"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgName":"${name_dy}","transactorInfo":{"psnAccount":"${phone_2}"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - str_eq: [content.data.0.orgId, "${account_id_dy_tsign}"]
      - str_eq: [content.data.0.orgName, "${ENV(name_dy)}"]
      - contains: [content.data.0.psnAccount, "${phone_2}"]
      - contains: [content.data.0.psnId, "${account_id_2_tsign}"]

- test:
    name: "添加个人签名域-传了手机号"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnAccount":"${phone_2}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - str_eq: [content.data.0.orgId, None]
      - str_eq: [content.data.0.orgName, None]
      - contains: [content.data.0.psnAccount, "${phone_2}"]
      - contains: [content.data.0.psnId, "${account_id_2_tsign}"]


- test:
    name: "添加企业签名域-传了oid，经办人传了oid"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_sx_tsign}","transactorInfo":{"psnId":"${account_id_1_tsign}"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - str_eq: [content.data.0.orgId, "${account_id_sx_tsign}"]
      - str_eq: [content.data.0.orgName, None]
      - str_eq: [content.data.0.psnAccount, None]
      - contains: [content.data.0.psnId, "${account_id_1_tsign}"]

- test:
    name: "添加法人签名域-传了oid，经办人传了oid"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_sx_tsign}","transactorInfo":{"psnId":"${account_id_1_tsign}"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":2}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - str_eq: [content.data.0.orgId, "${account_id_sx_tsign}"]
      - str_eq: [content.data.0.orgName, None]
      - str_eq: [content.data.0.psnAccount, None]
      - contains: [content.data.0.psnId, "${account_id_1_tsign}"]

- test:
    name: "平台自动签时orgid传了企业oid"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_sx_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "平台自动签时orgid传空字符串"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":""},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "平台自动签时orgid传null"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":null},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "平台自动签时不传orgid"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "平台自动签时不传orgSignerInfo"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "平台自动签时orgSignerInfo传null"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":null,"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":true,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "获取签署链接-操作人为无关的人"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":"$account_id_2_tsign"},"redirectConfig":{},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 1437114]
      - contains: [content.message, "当前用户不是流程参与人, 无权查看流程"]

- test:
    name: "获取签署链接-operator传平台方oid&organization传平台方oid"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":"${account_id_sx_tsign}"},"organization":{"orgId":"${account_id_sx_tsign}"},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "获取签署链接-operator传平台方oid&organization不传"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":"${account_id_sx_tsign}"},"organization":{"orgId":""},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "获取签署链接-operator不传&organization传平台方oid"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":null},"organization":{"orgId":"${account_id_sx_tsign}"},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "获取签署链接-operator不传&organization不传"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":null,"organization":{},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "获取签署链接-operator传平台方oid&organization传平台方oid"
    api: api/sign-flow/sign-url.yml
    variables:
      flowId: $flowId
      json: {"appScheme":"","needLogin":false,"operator":{"psnId":"${account_id_sx_tsign}"},"organization":{"orgId":"${account_id_sx_tsign}"},"urlType":2,"clientType":"ALL"}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "appId不允许无指定企业签署经办人无法发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_dy_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code,  1435002]
      - contains: [content.message, "非自动签签署机构签署方经办人transactorInfo不能为空"]

- test:
    name: "企业手动签时orgid传空字符串"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":""},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 机构签署方orgId和OrgName不能同时为空或同时传值]

- test:
    name: "企业手动签时orgid传null"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":null},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 机构签署方orgId和OrgName不能同时为空或同时传值]

- test:
    name: "企业手动签时不传orgid"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 机构签署方orgId和OrgName不能同时为空或同时传值]

- test:
    name: "企业手动签时orgSignerInfo为null"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":null,"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 同一个签署方，个人和机构不能同时为空或同时传值]

- test:
    name: "企业手动签时不传orgSignerInfo"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":true},"signFlowTitle":"签署"},"signFlowInitiator":{"psnInitiator":{"psnId":"${account_id_1_tsign}"}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":1,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 同一个签署方，个人和机构不能同时为空或同时传值]

- test:
    name: "仅存在一个或签任务，无法发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"docs":[{"fileEditPwd":"","fileId":"$file_id"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1,"signTipsContent": "内容1","signTipsTitle": "标题1"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 或签任务需要指定多个签署方]

- test:
    name: "相同主体相同顺序，无法发起或签任务"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"docs":[{"fileEditPwd":"","fileId":"$file_id"}],"signFlowConfig":{"autoFinish":false,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1,"signTipsContent": "内容1","signTipsTitle": "标题1"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1,"signTipsContent": "内容1","signTipsTitle": "标题1"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 或签任务需要指定多个签署方]

- test:
    name: "同一个签署人有两个或签任务，无法发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"docs":[{"fileEditPwd":"","fileId":"$file_id"}],"signFlowConfig":{"autoFinish":false,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1,"signTipsContent": "内容1","signTipsTitle": "标题1"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0},{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 或签任务需要指定多个签署方]

- test:
    name: "不同主体相同签署顺序，可以发起或签"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"attachments": [{"fileId": "$file_id"}],"docs":[{"fileEditPwd":"","fileId":"$file_id"}],"signFlowConfig":{"autoFinish":false,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"签署${get_timestamp()}"},"signFlowInitiator":{"orgInitiator": {"orgId": $account_id_sx_tsign, "transactor": {"psnId": $account_id_1_tsign}}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1,"signTipsContent": "内容1","signTipsTitle": "标题1"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_2_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1,"signTipsContent": "内容2","signTipsTitle": "标题2"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}
    extract:
      - flowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "已有或签，无法添加会签"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${account_id_3_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 同一顺序签署任务类型必须一致]

- test:
    name: "已有或签，无法仅添加不同顺序单主体或签任务"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${account_id_1_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":2,"signTaskType": 1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 或签任务需要指定多个签署方]

- test:
    name: "已有或签，无法添加已存在的主体相同顺序的或签任务"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${account_id_1_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - str_eq: [content.data.0.psnId, "${account_id_1_tsign}"]

- test:
    name: "已有或签，可以添加相同顺序其他人的或签任务"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${account_id_3_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "已有或签，无法添加不同主体类型的或签任务"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"${account_id_dy_tsign}","transactorInfo":{"psnId":"${account_id_1_tsign}"}},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType": 1},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 或签任务需要指定同一类型的签署方]

- test:
    name: "已有或签，添加不同顺序的会签任务"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"${account_id_1_tsign}"},"signConfig":{"forcedReadingTime":0,"signOrder":2},"signFields":[{"fileId":"${file_id}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":0}]}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - contains: [content.message, 成功]

- test:
    name: "查询流程详情"
    api: api/sign-flow/detail.yml
    variables:
      flowId: $flowId
    extract:
      - signFieldId: content.data.signers.0.signFields.0.signFieldId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, 成功]
      - eq: [content.data.signers.0.signTaskType, 1]
      - eq: [content.data.signers.1.signTaskType, 1]
      - eq: [content.data.signers.2.signTaskType, 1]
      - eq: [content.data.signers.3.signTaskType, 0]

- test:
    name:  或签任务允许获取批量签链接
    variables:
      - json: {"clientType": "ALL","operatorId": $account_id_1_tsign,"redirectUrl": "","signFlowIds": [$flowId]}
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "成功"]
#      - contains: ["content.message", "暂不支持或签任务批量签署"]
#      - eq: [content.code, 1437199]

- test:
    name: "一步发起，所有信息为id"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"copiers":[{"copierPsnInfo":{"psnId":"$account_id_2_tsign"}},{"copierOrgInfo":{"orgId":"$account_id_dy_tsign"},"copierPsnInfo":{"psnId":"$account_id_2_tsign"}}],"docs":[{"fileEditPwd":"","fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"${notifyUrl}","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"所有信息为id"},"signFlowInitiator":{"psnInitiator":{"psnId":$account_id_1_tsign}},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"$account_id_sx_tsign","transactorInfo":{"psnId":"$account_id_1_tsign"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "签署声明文件和文案无法同时传入"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"${notifyUrl}","signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"所有信息为id"},"signers":[{"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTipsContent":"11","signTipsTitle":"22","signTipsFileId":"$file_id"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"$account_id_sx_tsign","transactorInfo":{"psnId":"$account_id_1_tsign"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435011]
      - contains: [content.message, 签署声明文件和文案无法同时传入]

- test:
    name: "传了签署声明文件和标题可以发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"${notifyUrl}","signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"所有信息为id"},"signers":[{"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTipsTitle":"22","signTipsFileId":"$file_id"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"$account_id_sx_tsign","transactorInfo":{"psnId":"$account_id_1_tsign"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 0]
      - contains: [content.message, 成功]
      - len_eq: [content.data.signFlowId, 32]

- test:
    name: "仅传签署声明文件不可以发起"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {"docs":[{"fileId":"$file_id","fileName":"签署文件.PDF","neededPwd":false}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"${notifyUrl}","signFlowExpireTime":"${get_timestamp(10)}","signFlowTitle":"所有信息为id"},"signers":[{"psnSignerInfo":{"psnId":"$account_id_1_tsign"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTipsFileId":"$file_id"},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgId":"$account_id_sx_tsign","transactorInfo":{"psnId":"$account_id_1_tsign"}},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"customBizNum":"","fileId":"$file_id","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":200,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 签署前提示弹框文案标题、签署前提示弹框文件必须配套使用]
