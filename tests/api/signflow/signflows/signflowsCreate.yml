#def: signflowsCreate($autoArchive, $businessScene, $configInfo, $contractValidity, $extend, $initiatorAccountId, $payerAccountId, $signValidity)
request:
  url: /v1/signflows
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    extend: $extend
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity


#查询流程
