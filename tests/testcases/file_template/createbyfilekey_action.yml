- config:
    name: 创建模板相关各种操作
    ###def: createbyfilekey_action($contentMd5,$contentType,$fileName,$fileSize,$file_path,$fileKey,$accountId,$name,$fileId,$pageNo, $pageSize)
    request:
      base_url: "${ENV(footstone_api_url)}"
      headers:
        Content-Type: "application/json"


- test:
    name: 获取上传文件url
    api: api/file_template/files/get_uploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 上传文件到oss
    variable_binds:
      - binary: ${open_file($file_path)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 根据本地文件创建文件
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    output:
      - $fileId

- test:
    name: 获取文件信息
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 根据fileKey获取文档预览地址
    api: api/file_template/files/get_filePreviewUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 分页获取指定PDF文档预览
    variables:
      - pageNum: 1
      - imageSize: preview
    api: api/file_template/files/get_imagepreview.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 获取模板列表
    api: api/file_template/templates/get_templates.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 指定fileKey分页获取指定PDF文档预览
    variables:
      - pageNum: 1
      - imageSize: thumb
    api: api/file_template/files/get_viewimages.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]