- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    base_url: ${ENV(base_url)}
    variables:
      - orgName: esigntest东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: ***********
      - path_pdf: 个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - idNo1: 130202199104016781
      - name1: esigntest-测试用户1
      - thirdPartyUserId1: esigntest-130202199104016781
      - idNo2: 230124200712193556
      - name2: esigntest-测试用户2
      - thirdPartyUserId2: esigntest-230124200712193556
      - idNo3: 230803201510286771
      - name3: esigntest-测试用户3
      - thirdPartyUserId3: esigntest-230803201510286771
      - client_id: pc

- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: 130202199104016781
      - name: esigntest-测试用户1
      - thirdPartyUserId: esigntest-130lkjhg9104016781
    api: api/user/account_third/create_account_appid.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: "用户1创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章1
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230124200712193556
      - name: esigntest-测试用户2
      - thirdPartyUserId: esigntest-23012443234c22vbnn1219
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建组织：个人账户2创建组织"
    variables:
      - accountId: $personOid2
      - orgType: CRED_ORG_CODE
      - orgCode: ********-X
      - legalIdNo:
      - legalName:
      - name: $orgName
    api: api/user/organizations/create_organizations.yml
    extract:
      - orgOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建企业模板印章"
    variables:
      - orgId: $orgOid1
      - color: RED
      - height: 150
      - width: 150
      - alias: 企业模板印章
      - type: TEMPLATE_ROUND
      - central: STAR
      - hText: 上弦文
      - qText: 下弦文
    api: api/seals/seals/create_c_template_seal_appid.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: 获取文件信息
    variables:
      - fileId: ${get_file_id($app_id,$path_pdf)}
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 单人单文档场景-个人空间创建流程:只填必填项
    variables:
      - autoArchive: False
      - businessScene: 单人单文档
      - initiatorAccountId:
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 单人单文档场景-更新流程：设置自动归档
    variables:
      - autoArchive: True
      - flowId: $flowId1
      - businessScene: 单人单文档
      - initiatorAccountId:
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-开启流程，预期结果：失败，流程需要添加文档才能发起
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",流程没有文档不允许开启流程]

- test:
    name: 单人单文档场景-添加流程文档：单文档
    variables:
      - flowId: $flowId1
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-开启流程，预期结果：失败，自动归档的流程需要添加签署区才能发起
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",流程没有签署区不允许开启流程]

- test:
    name: 单人单文档场景-添加流程签署区：签署人账号-个人账户1、手动签署、签约主体-个人、不指定签署区
    variables:
      - flowId: $flowId1
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId1,$flowId1,$posBean,0,$personOid1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-查询流程
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["${isContainKeyForApi($content_data,extend)}",False]
      - eq: ["${isContainKeyForApi($content_data,signerNames)}",False]
      - eq: ["${isContainKeyForApi($content_data,waitingSignerNames)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,billType)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,saleSchemaId)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,realNameCert)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,organRealnameTypes)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,personRealnameTypes)}",False]
      - eq: ["${isContainKeyForApi($content_data_configInfo,willingness)}",False]

- test:
    name: 单人单文档场景-查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 单人单文档场景-添加或更新签署区并执行签署
    variables:
      - flowId: $flowId1
      - accountId: $personOid1
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield: ${gen_signfield_data_V2(0,0,$fileId1,$flowId1,$posBean,1,$personOid1,$personOid1,$sealId,1)}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 单人单文档场景-查询流程：已归档
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsSelect.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 单人单文档场景-个人单文档签署-获取签署地址
    variables:
      - urlType: 0
      - flowId: $flowId1
      - accountId: $personOid1
      - organizeId:
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-企业空间创建流程-非自动归档
    variables:
      - autoArchive: False
      - businessScene: 企业空间创建流程
      - initiatorAuthorizedAccountId: $orgOid1
      - initiatorAccountId: $personOid1
    api: api/signflow/signflows/signflowsCreateForOrganize.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 单人单文档场景-添加流程文档：单文档
    variables:
      - flowId: $flowId2
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-添加流程签署区-指定位置
    variables:
      - flowId: $flowId2
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 200, posY: 600, qrcodeSign: False, width: null}
      - signfield1: ${gen_signfield_data_V2(0,0,$fileId1,$flowId2,$posBean,1,$personOid1,,$sealId,1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-执行流程签署区
    variables:
      - signfieldIds: [$signfieldId1]
      - flowId: $flowId2
      - accountId: $personOid1
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 单人单文档场景-添加流程签署区-骑缝签署
    variables:
      - flowId: $flowId2
      - posBean: {addSignTime: True, key: '', posPage: '1-2', posX: null, posY: 600, qrcodeSign: False, width: null}
      - signfield1: ${gen_signfield_data_V2(0,0,$fileId1,$flowId2,$posBean,2,$personOid1,,$sealId,1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-执行流程签署区
    variables:
      - signfieldIds: [$signfieldId1]
      - flowId: $flowId2
      - accountId: $personOid1
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 单人单文档场景-查询文档关键字
    variables:
      - keywords: 出借方
      - flowId: $flowId2
      - fileId: $fileId1
    api: api/signflow/flowDocuments/documentsKeywordSelect.yml
    extract:
      - posPage: content.data.0.positionList.0.pageIndex
      - posX: content.data.0.positionList.0.coordinateList.0.posx
      - posY: content.data.0.positionList.0.coordinateList.0.posy
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-添加流程签署区-关键字
    variables:
      - flowId: $flowId2
      - posBean: {addSignTime: True, keyword: '出借方', posPage: $posPage, posX: 10, posY: 10, qrcodeSign: False, width: null}
      - signfield1: ${gen_signfield_data_V2(0,0,$fileId1,$flowId2,$posBean,4,$personOid1,,$sealId,1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-执行流程签署区
    variables:
      - signfieldIds: [$signfieldId1]
      - flowId: $flowId2
      - accountId: $personOid1
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]

- test:
    name: 单人单文档场景-查询流程状态
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",1]
      #
      #- test:
      #    name: 单人单文档场景-归档流程
      #    api: api/signflow/signflows/signflowsArchive.yml
      #    validate:
#      - eq: ["content.code",0]
      #
      #- test:
      #    name: 单人单文档场景-查询流程：已归档
      #    api: api/signflow/signflows/signflowsSelect.yml
      #    setup_hooks:
      #      - ${sleep_N_secs(10)}
      #    validate:
#      - eq: ["content.data.flowStatus",2]
