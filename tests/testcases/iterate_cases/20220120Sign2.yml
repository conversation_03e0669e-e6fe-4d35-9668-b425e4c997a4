- config:
    name: "20220120迭代"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(xuanyuan_realname)}
      - fileId1: a152d745f521490aaedcadafed493b98
      - fileId2: a152d745f521490aaedcadafed493b98
      - mobile_fengjiu: ***********
      - mobile_caogu: ***********
      - sealId1: 8fa71acc-425d-4270-9fa7-b0e91f57fbef

- test:
    name: "创建草谷账号"
    variables:
      - app_id: $write_app_Id
      - group: $w_group
      - thirdPartyUserId: caogu201911141132
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - z<PERSON>an_accountId: content.data.accountId

- test:
    name: "创建凤九账号"
    variables:
      - thirdPartyUserId: fengjiu20191114199132191431252002
      - name: 曾艳
      - idNumber: 362324199301130629
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - fengjiu_accountId: content.data.accountId


- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: ckl201911141994
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

#
#
#

#流程创建时无抄送人，草稿中状态，更新3个抄送人
- test:
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    name: "流程无抄送人"
    variables:
      - doc: {encryption: 0,fileId: $fileId2, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $accountId_ckl, autoArchive: true, autoInitiate: false, businessScene: "流程无抄送人",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId2,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $accountId_ckl, autoArchive: true, autoInitiate: false, businessScene: "流程无抄送人",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]

    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
#
#
- test:
    name: "查询流程-无抄送人"
    variables:
      - flowId: $flowId1
    api: api/iterate_cases/queryFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]
      - str_eq: [content.data.recipients,None]
#
- test:
    name: "更新流程抄送人"
    variables:
      - flowId: $flowId1
      - recipients: [{
                       "recipientAccount": {
                         "accountGuid": "",
                         "accountOuid": $accountId_ckl,
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientName": "",
                       "recipientNickname": "",
                       "recipientSubjectAccount": {
                         "accountGuid": "",
                         "accountOuid": $accountId_ckl,
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientSubjectName": "",
                       "recipientSubjectType": 0
                     },
                     {
                       "recipientAccount": {
                         "accountGuid": "",
                         "accountOuid": $zhelan_accountId,
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientName": "",
                       "recipientNickname": "",
                       "recipientSubjectAccount": {
                         "accountGuid": "",
                         "accountOuid": $accountId_ckl,
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientSubjectName": "",
                       "recipientSubjectType": 0
                     },
                     {
                       "recipientAccount": {
                         "accountGuid": "",
                         "accountOuid": "",
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientName": "",
                       "recipientNickname": "",
                       "recipientSubjectAccount": {
                         "accountGuid": "",
                         "accountOuid": "",
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientSubjectName": "",
                       "recipientSubjectType": 0
                     },
                     {
                       "recipientAccount": {
                         "accountGuid": "",
                         "accountOuid": $fengjiu_accountId,
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientName": "",
                       "recipientNickname": "",
                       "recipientSubjectAccount": {
                         "accountGuid": "",
                         "accountOuid": $accountId_ckl,
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientSubjectName": "",
                       "recipientSubjectType": 0
                     }]
    api: api/iterate_cases/updateFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]

- test:
    name: "查询流程-3个抄送人"
    variables:
      - flowId: $flowId1
    api: api/iterate_cases/queryFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]
      - len_eq: ["content.data.recipients",3]

#
##开启流程后 更新抄送人为另外一个人
- test:
    name: 开启流程
    variables:
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
#
- test:
    name: "查询流程-3个抄送人"
    variables:
      - flowId: $flowId1
    api: api/iterate_cases/queryFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]
      - len_eq: ["content.data.recipients",3]


- test:
    name: "更新流程抄送人为另外一个人"
    variables:
      - flowId: $flowId1
      - recipients: [{
                       "recipientAccount": {
                         "accountGuid": "覆盖",
                         "accountOuid": "12345",
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientName": "",
                       "recipientNickname": "",
                       "recipientSubjectAccount": {
                         "accountGuid": "2345",
                         "accountOuid": "",
                         "accountRuid": "23456",
                         "accountUuid": ""
                       },
                       "recipientSubjectName": "",
                       "recipientSubjectType": 2
                     }]
    api: api/iterate_cases/updateFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]

- test:
    name: "查询流程-1个抄送人"
    variables:
      - flowId: $flowId1
    api: api/iterate_cases/queryFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]
      - len_eq: ["content.data.recipients",1]

#
#
#
#
##流程签署完成后 更新流程抄送人为null
- test:
    name: "根据登陆凭证获取accountId"
    variables:
      - idcard: ***********
      - type: MOBILE
    api: api/iterate_cases/getByIdcard.yml
    extract:
      - stu_accountId_ckl: content.data.items.0.accountId

- test:
    name: 查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $stu_accountId_ckl
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId1
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1_signfieldId: content.data.signfields.0.signfieldId

- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId_ckl
      - flowId: $flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId_ckl
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: "添加或更新签署区并执行签署"
    variables:
      - flowId: $flowId1
      - accountId: $stu_accountId_ckl
      - addSignfields:
      - updateSignfields: [{"sealId":$p1_sealId, authorizedAccountId: $accountId_ckl, "signerOperatorAuthorizerId":$stu_accountId_ckl,"signerOperatorId":$stu_accountId_ckl,"signfieldId":$flowId1_signfieldId}]
      - signfieldIds:
    api: api/iterate_cases/addUpdateExecute_sign.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "查询流程-1个抄送人"
    variables:
      - flowId: $flowId1
    api: api/iterate_cases/queryFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]
      - len_eq: ["content.data.recipients",1]
#
#
#
- test:
    name: "更新流程抄送人为null"
    variables:
      - flowId: $flowId1
      - recipients: [{
                       "recipientAccount": {
                         "accountGuid": "",
                         "accountOuid": "",
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientName": "",
                       "recipientNickname": "",
                       "recipientSubjectAccount": {
                         "accountGuid": "",
                         "accountOuid": "",
                         "accountRuid": "",
                         "accountUuid": ""
                       },
                       "recipientSubjectName": "",
                       "recipientSubjectType": 0
                     }]
    api: api/iterate_cases/updateFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]


- test:
    name: "查询流程-无抄送人"
    variables:
      - flowId: $flowId1
    api: api/iterate_cases/queryFlowRecipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]
      - len_eq: ["content.data.recipients",0]