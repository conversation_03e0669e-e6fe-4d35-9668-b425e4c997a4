config:
  name: 手绘方式校验+签署凭证
  base_url: ${ENV(footstone_api_url)}

testcases:

  手绘方式校验:
    testcase: testcases/signflow_check/handDrawnWay.yml

  签署凭证:
    testcase: testcases/signflow_check/voucher_check.yml

  归档锁定:
    testcase: testcases/signflow_check/archiveLock.yml

  签署过程查询是否有有效证书:
    testcase: testcases/signflow_check/realSignValidCert.yml

  审批过程查询是否有有效证书:
    testcase: testcases/signflow_approval/sealApprovalValidCert.yml

#    签署凭证异步签:
#      testcase: testcases/signflow_check/voucher_check_async.yml
