- config:
    name: "20220425附件下载"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id_origin: ${ENV(gan_appid_download)}
      - fileId1: ${get_file_id($app_id_origin,$path_pdf)}
      - fileId: ${get_file_id($app_id_origin,$path_pdf2)}
      - imagfileId: "5d134d1a5033458293a122bded604950"

- test:
    name: "创建签署人账号 - gan"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId
      - yuzan_accountId: content.data.accountId


- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: z111abcabcabcabcabc
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "使用caogu_accountId创建机构"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId



- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: 一步发起指定证书id用户手动签-成功
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '附件1', source: 0}
      - attachments: [{fileId: $fileId, attachmentName: "附件2.pdf"}]
      - docs: [$doc]
      - copiers: [{copierAccountId: $accountId_ckl,copierIdentityAccountId: "", copierIdentityAccountType: 0}]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "********附件下载",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      },
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $yuzan_accountId
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 0,
            "signerRoleType": "0",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 100,
                "posY": 100
              },
            "signDateBeanType": "2",
            "signDateBean": { "fontSize": 50,"posX": 220,"posY": 250,"posPage": "1" },
            "signType": 1
          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']


- test:
    name: 流程状态，查询附件列表-appid  gan_appid_download
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",1]

- test:
    name: 流程状态，查询附件列表  gan_appid_xuanyuan
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - eq: [ content.code,1435002 ]
      - contains: [ "content.message", "签署流程不存在" ]
      - eq: [ "status_code", 200 ]

- test:
    name: 流程状态，查询附件列表 app_id_in_wukong_ckl
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: ${ENV(app_id_in_wukong_ckl)}
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - eq: [ content.code,1435002 ]
      - contains: [ "content.message", "签署流程不存在" ]
      - eq: [ "status_code", 200 ]