- config:
    name: 单文档添加1个企业指定签名域
    ###def: one_businessSign()
    "request":
      "base_url": ""
      "headers":
        "Content-Type": "application/json"

- test:
    name: 创建流程企业空间发起（自动归档）
    variables:
      - autoArchive: true
      - businessScene: 单文档添加1个企业指定签名域
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - initiatorAccountId: $caogu
      - payerAccountId: ""
      - signValidity: *************
      - payerAccountId: ""
      - initiatorAuthorizedAccountId: $sixian1
    api: api/signflow/signflows/signflowsCreateForOrganize.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加两份流程文档
    variables:
      - doc: [{'fileHash': '', 'fileId': $fileId1, 'fileName': 'test1', 'filePassword': '', 'encryption': 0},{'fileHash': '', 'fileId': $fileId2, 'fileName': 'test2', 'filePassword': '', 'encryption': 0}]
      - docs: $doc
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加1个指定企业（sixian1）手动签署区（第一份文档）
    variables:
      - signfields: [{"fileId":$fileId1,"signerAccountId":$sunyang1,"actorIndentityType":"2","authorizedAccountId":$sixian1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 获取开放服务企业账号（sixian1）对应的实名组织详情
    api: api/signflow_approval/sign_accounts/getFinalRealnameOrgInfo.yml
    extract:
      - rea_sixian1: content.data.base.ouid
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.base.ouid",0]

- test:
    name: 获取开放服务企业账号对应的实名组织的印章
    api: api/signflow_approval/sign_accounts/getOrgSeals.yml
    extract:
      - sixian1_sealId0: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.seals.0.sealId",0]

- test:
    name: 获取开放服务个人账号属性
    api: api/signflow_approval/sign_accounts/getProperties.yml
    extract:
      - sunyang1_mobile: content.data.contacts.mobile
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.contacts.mobile",0]

- test:
    name: 根据登录凭证获取账号信息(手机号)
    api: api/signflow_approval/sign_accounts/getByIdcard.yml
    extract:
      - real_sunyang1: content.data.items.0.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.items.0.accountId",0]

- test:
    name: 签署人-运营商三要素意愿认证-意愿发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $sunyang1
      - flowId: $flowId
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 回填三要素验证码
    variables:
      - authcode: 123456
      - accountId: $sunyang1
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加或更新签名域并执行签署
    variables:
      - addSignfields: []
      - accountId: $real_sunyang1
      - needSignWilling: true
      - updateSignfields: [{"sealId":$sixian1_sealId0,"signerOperatorAuthorizerId":$rea_sixian1,"signerOperatorId":$real_sunyang1,"signfieldId":$signfieldId0}]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: ""
      - securityCode: null
    api: api/signflow_approval/flow_operate/addUpdateExecute_approval_sign.yml
    extract:
      - approveFlowId: content.data.signfieldSignResult.0.approveFlowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message","成功"]
#      - len_gt: ["content.data.signfieldSignResult.0.approveFlowId",0]

- test:
    name: 审批人-运营商三要素意愿认证-意愿发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $approval_accountid
      - flowId: $flowId
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 回填三要素验证码
    variables:
      - authcode: 123456
      - accountId: $approval_accountid
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 审批人-同意用印
    variables:
      - approvalId: $approveFlowId
      - accountId: $approval_accountid
      - securityCode: ""
    api: api/signflow_approval/approval/agreeApproval.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: 获取签署链接
    api: api/signflow/signflows/getManySignUrl.yml
    variables:
      - flowId: $flowId
      - accountId: $sunyang1
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian1
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.shortUrl",0]