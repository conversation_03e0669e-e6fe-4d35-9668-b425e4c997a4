- config:
    name: "0513接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id)}
      - app_id: ${ENV(X-Tsign-Open-App-Id)}
      - path_pdf1: 个人借贷合同.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf1)}
      - group: ''

- test:
    name: "创建appid下的签署人账号"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_id
      - idNo:
      - mobile: ***********
      - name: 卜伟强
      - thirdPartyUserId: weiqiang1983
    extract:
      - signer_accountId: content.data.accountId

- test:
    name: "一步到位创建流程-signValidity截止时间超出-大于'2038-01-19 03:14:07.999999'"
    variables:
      - app_id: $app_id
      - json:
          {
            "docs":[
            {
              "fileId":"$fileId1",
              "fileName":"个人借贷合同.pdf"
            }
            ],
            "flowInfo":{
              "autoArchive":true,
              "autoInitiate":true,
              "businessScene":"卜伟强+创建签署流程",
              "contractRemind":1,
              "contractValidity":*************,
              "flowConfigInfo":{
                "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}",
                "noticeType":"1,2,3",
                "redirectUrl":"",
                "signPlatform":"1,2"
              },
              "remark":"",
              "signValidity":*************
            },
            "signers":[
            {
              "platformSign":false,
              "signOrder":1,
              "signerAccount":{
                "authorizedAccountId":"",
                "signerAccountId":"$signer_accountId"
              },
              "signfields":[
              {
                "actorIndentityType": "0",
                "autoExecute":false,
                "fileId":"$fileId1",
                "posBean":{
                  "posPage":"1",
                  "posX":440,
                  "posY":440
                },
                "sealType":"",
                "signDateBean":{
                  "fontSize":12,
                  "format":"yyyy-MM-dd"
                },
                "signType":1,
                "width":150
              }
              ],
              "thirdOrderNo":"111"
            }
            ]
          }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437169]
      - eq: ["content.message","签署截止时间超出范围，建议调整截止时间不要超过发起后一年"]

- test:
    name: "一步到位创建流程-signValidity截止时间超出-小于'1970-01-01 00:00:01.000000'"
    variables:
      - app_id: $app_id
      - json:
          {
            "docs":[
            {
              "fileId":"$fileId1",
              "fileName":"个人借贷合同.pdf"
            }
            ],
            "flowInfo":{
              "autoArchive":true,
              "autoInitiate":true,
              "businessScene":"卜伟强+创建签署流程",
              "contractRemind":1,
              "contractValidity":*************,
              "flowConfigInfo":{
                "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}",
                "noticeType":"1,2,3",
                "redirectUrl":"",
                "signPlatform":"1,2"
              },
              "remark":"",
              "signValidity":-1
            },
            "signers":[
            {
              "platformSign":false,
              "signOrder":1,
              "signerAccount":{
                "authorizedAccountId":"",
                "signerAccountId":"$signer_accountId"
              },
              "signfields":[
              {
                "actorIndentityType": "0",
                "autoExecute":false,
                "fileId":"$fileId1",
                "posBean":{
                  "posPage":"1",
                  "posX":440,
                  "posY":440
                },
                "sealType":"",
                "signDateBean":{
                  "fontSize":12,
                  "format":"yyyy-MM-dd"
                },
                "signType":1,
                "width":150
              }
              ],
              "thirdOrderNo":"111"
            }
            ]
          }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437169]
      - eq: ["content.message","流程已过期"]

- test:
    name: "createFlowOneStep-signValidity传0 - autoInitiate传true'"
    variables:
      - app_id: $app_id
      - json:
          {
            "docs":[
            {
              "fileId":"$fileId1",
              "fileName":"个人借贷合同.pdf"
            }
            ],
            "flowInfo":{
              "autoArchive":true,
              "autoInitiate":true,
              "businessScene":"卜伟强+创建签署流程",
              "contractRemind":1,
              "contractValidity":*************,
              "flowConfigInfo":{
                "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}",
                "noticeType":"1,2,3",
                "redirectUrl":"",
                "signPlatform":"1,2"
              },
              "remark":"",
              "signValidity":0
            },
            "signers":[
            {
              "platformSign":false,
              "signOrder":1,
              "signerAccount":{
                "authorizedAccountId":"",
                "signerAccountId":"$signer_accountId"
              },
              "signfields":[
              {
                "actorIndentityType": "0",
                "autoExecute":false,
                "fileId":"$fileId1",
                "posBean":{
                  "posPage":"1",
                  "posX":440,
                  "posY":440
                },
                "sealType":"",
                "signDateBean":{
                  "fontSize":12,
                  "format":"yyyy-MM-dd"
                },
                "signType":1,
                "width":150
              }
              ],
              "thirdOrderNo":"111"
            }
            ]
          }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437169]
      - eq: ["content.message","流程已过期"]

- test:
    name: "createFlowOneStep-signValidity传0 - autoInitiate传false'"
    variables:
      - app_id: $app_id
      - json:
          {
            "docs":[
            {
              "fileId":"$fileId1",
              "fileName":"个人借贷合同.pdf"
            }
            ],
            "flowInfo":{
              "autoArchive":true,
              "autoInitiate":false,
              "businessScene":"卜伟强+创建签署流程",
              "contractRemind":1,
              "contractValidity":*************,
              "flowConfigInfo":{
                "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}",
                "noticeType":"1,2,3",
                "redirectUrl":"",
                "signPlatform":"1,2"
              },
              "remark":"",
              "signValidity":0
            },
            "signers":[
            {
              "platformSign":false,
              "signOrder":1,
              "signerAccount":{
                "authorizedAccountId":"",
                "signerAccountId":"$signer_accountId"
              },
              "signfields":[
              {
                "actorIndentityType": "0",
                "autoExecute":false,
                "fileId":"$fileId1",
                "posBean":{
                  "posPage":"1",
                  "posX":440,
                  "posY":440
                },
                "sealType":"",
                "signDateBean":{
                  "fontSize":12,
                  "format":"yyyy-MM-dd"
                },
                "signType":1,
                "width":150
              }
              ],
              "thirdOrderNo":"111"
            }
            ]
          }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建流程-截止时间超出-大于'2038-01-19 03:14:07.999999'"
    variables:
      - app_id: $app_id
      - autoArchive: false
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: null
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: *************
    api: api/signflow/signflows/signflowsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437169]
      - eq: ["content.message","签署截止时间超出范围，建议调整截止时间不要超过发起后一年"]

- test:
    name: "创建流程-截止时间超出-小于'1970-01-01 00:00:01.000000'"
    variables:
      - app_id: $app_id
      - autoArchive: false
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: null
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: -1
    api: api/signflow/signflows/signflowsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.code",1435012]
      - eq: ["content.message","服务异常"]


- test:
    name: "/v1/signflows - signValidity传0'"
    variables:
      - app_id: $app_id
      - autoArchive: false
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: null
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: 0
    api: api/signflow/signflows/signflowsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data",'flowId']

- test:
    name: "createWithSignDocs- signValidity传0"
    variables:
      - autoArchive: false
      - businessScene: "createWithSignDocs- signValidity传0 "
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":1}
      - contractValidity: ""
      - initiatorAccountId: $signer_accountId
      - createWay: "normal"
      - payerAccountId: null
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - signValidity: 0
    api: api/iterate_cases/createWithSignDocs_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "createWithSignDocs- signValidity不传"
    variables:
      - autoArchive: false
      - businessScene: "createWithSignDocs- signValidity不传 "
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":1}
      - contractValidity: ""
      - initiatorAccountId: $signer_accountId
      - createWay: "normal"
      - payerAccountId: null
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - signValidity:
    api: api/iterate_cases/createWithSignDocs_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data",'flowId']
    extract:
      - flowId1: content.data.flowId


- test:
    name: "createWithSignDocs- signValidity传2030年"
    variables:
      - autoArchive: false
      - businessScene: "createWithSignDocs- signValidity传2030年 "
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":1}
      - contractValidity: ""
      - initiatorAccountId: $signer_accountId
      - createWay: "normal"
      - payerAccountId: null
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - signValidity: *************
    api: api/iterate_cases/createWithSignDocs_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data",'flowId']

- test:
    name: "createWithSignDocs- signValidity传2039年"
    variables:
      - autoArchive: false
      - businessScene: "createWithSignDocs- signValidity传2039年 "
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":1}
      - contractValidity: ""
      - initiatorAccountId: $signer_accountId
      - createWay: "normal"
      - payerAccountId: null
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - signValidity: *************
    api: api/iterate_cases/createWithSignDocs_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437169]
      - eq: ["content.message", "签署截止时间超出范围，建议调整截止时间不要超过发起后一年"]


- test:
    name: "createAllAtOnce - signValidity传0"
    variables:
      - json: {"accountId":$signer_accountId,"initiatorAccountId": $signer_accountId,"createWay":"normal","autoInitiate":false,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"0","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$signer_accountId,"signerAccountName":"","signerAuthorizerId":$signer_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "createAllAtOnce - signValidity不传"
    variables:
      - json: {"accountId":$signer_accountId,"initiatorAccountId": $signer_accountId,"createWay":"normal","autoInitiate":false,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$signer_accountId,"signerAccountName":"","signerAuthorizerId":$signer_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data",'flowIds']

- test:
    name: "createAllAtOnce - signValidity传2030年"
    variables:
      - json: {"accountId":$signer_accountId,"initiatorAccountId": $signer_accountId,"createWay":"normal","autoInitiate":false,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"*************","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$signer_accountId,"signerAccountName":"","signerAuthorizerId":$signer_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data",'flowIds']

- test:
    name: "createAllAtOnce - signValidity传2039年"
    variables:
      - json: {"accountId":$signer_accountId,"initiatorAccountId": $signer_accountId,"createWay":"normal","autoInitiate":false,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"*************","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$signer_accountId,"signerAccountName":"","signerAuthorizerId":$signer_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437169]
      - eq: ["content.message", "签署截止时间超出范围，建议调整截止时间不要超过发起后一年"]


- test:
    name: "/v1/signflows/{flowId}/updateWithScratch - signValidity传0"
    variables:
      - flowId: $flowId1
      - autoArchive: true
      - businessScene: "updateWithScratch - signValidity传0"
      - configInfo: {"mobileShieldWay":1}
      - initiatorAccountId2: $signer_accountId
      - operatorId: $signer_accountId
      - signValidity: 0
    api: api/iterate_cases/updateWithScratch_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "/v1/signflows/{flowId}/updateWithScratch - signValidity不传"
    variables:
      - flowId: $flowId1
      - autoArchive: true
      - businessScene: "updateWithScratch - signValidity不传"
      - configInfo: {"mobileShieldWay":1}
      - initiatorAccountId2: $signer_accountId
      - operatorId: $signer_accountId
      - signValidity:
    api: api/iterate_cases/updateWithScratch_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "/v1/signflows/{flowId}/updateWithScratch - signValidity传2030年"
    variables:
      - flowId: $flowId1
      - autoArchive: true
      - businessScene: "updateWithScratch - signValidity传2030年"
      - configInfo: {"mobileShieldWay":1}
      - initiatorAccountId2: $signer_accountId
      - operatorId: $signer_accountId
      - signValidity: *************
    api: api/iterate_cases/updateWithScratch_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "/v1/signflows/{flowId}/updateWithScratch - signValidity传2040年"
    variables:
      - flowId: $flowId1
      - autoArchive: true
      - businessScene: "updateWithScratch - signValidity传2040年"
      - configInfo: {"mobileShieldWay":1}
      - initiatorAccountId2: $signer_accountId
      - operatorId: $signer_accountId
      - signValidity: *************
    api: api/iterate_cases/updateWithScratch_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435012]
      - eq: ["content.message", "服务异常"]

- test:
    name: "/v2/signflows/{flowId}/updateAllAtOnce - signValidity传0"
    variables:
      - flowId: $flowId1
      - autoArchive: false
      - businessScene: "/v2/signflows/{flowId}/updateAllAtOnce - signValidity传0"
      - configInfo: {"mobileShieldWay":1}
      - accountId: $signer_accountId
      - docs: [{"fileId": $fileId1}]
      - signers: null
      - signValidity: 0
    api: api/iterate_cases/updateAllAtOnce_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "/v2/signflows/$flowId/updateAllAtOnce - signValidity不传"
    variables:
      - flowId: $flowId1
      - autoArchive: false
      - businessScene: "/v2/signflows/$flowId/updateAllAtOnce - signValidity不传"
      - configInfo: {"mobileShieldWay":1}
      - accountId: $signer_accountId
      - docs: [{"fileId": $fileId1}]
      - signers: null
      - signValidity:
    api: api/iterate_cases/updateAllAtOnce_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "/v2/signflows/$flowId/updateAllAtOnce - signValidity传2030年"
    variables:
      - flowId: $flowId1
      - autoArchive: false
      - businessScene: "/v2/signflows/$flowId/updateAllAtOnce - signValidity传2030年"
      - configInfo: {"mobileShieldWay":1}
      - accountId: $signer_accountId
      - docs: [{"fileId": $fileId1}]
      - signers: null
      - signValidity: *************
    api: api/iterate_cases/updateAllAtOnce_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "/v2/signflows/{flowId}/updateAllAtOnce - signValidity传2040年"
    variables:
      - flowId: $flowId1
      - autoArchive: false
      - businessScene: "/v2/signflows/{flowId}/updateAllAtOnce - signValidity传2040年"
      - configInfo: {"mobileShieldWay":1}
      - accountId: $signer_accountId
      - docs: [{"fileId": $fileId1}]
      - signers: null
      - signValidity: *************
    api: api/iterate_cases/updateAllAtOnce_0513.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435012]
      - eq: ["content.message", "服务异常"]


- test:
    name: /v1/signflows/{flowId} - signValidity传0
    variables:
      - autoArchive: True
      - flowId: $flowId1
      - businessScene: 单人单文档
      - initiatorAccountId:
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - signValidity: 0
      - contractValidity:
      - extend:
      - payerAccountId:
    api: api/signflow/signflows/signflowsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: /v1/signflows/{flowId} - signValidity不传
    variables:
      - autoArchive: True
      - flowId: $flowId1
      - businessScene: 单人单文档
      - initiatorAccountId:
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - signValidity:
      - contractValidity:
      - extend:
      - payerAccountId:
    api: api/signflow/signflows/signflowsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name:  put /v1/signflows/{flowId}/ - signValidity传2030年
    variables:
      - autoArchive: True
      - flowId: $flowId1
      - businessScene: 单人单文档
      - initiatorAccountId:
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - signValidity: *************
      - contractValidity:
      - extend:
      - payerAccountId:
    api: api/signflow/signflows/signflowsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: put /v1/signflows/{flowId} - signValidity传2039年
    variables:
      - autoArchive: True
      - flowId: $flowId1
      - businessScene: 单人单文档
      - initiatorAccountId:
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - signValidity: *************
      - contractValidity:
      - extend:
      - payerAccountId:
    api: api/signflow/signflows/signflowsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435012]
      - eq: ["content.message", "服务异常"]