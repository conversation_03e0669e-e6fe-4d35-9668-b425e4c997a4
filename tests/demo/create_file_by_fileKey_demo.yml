- config:
    name: "0723迭代测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - group: ''
      - batchSign:
      - path_pdf: 个人借贷合同.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US

- test:
    name: "创建签署人账号"
    variables:
      - thirdPartyUserId: autotest-wyj-320721199305232634
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId: content.data.accountId


- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:

      - accountId: $accountId
      - color: "RED"
      - height: 2
      - alias: "某某的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 获取文件信息
    variables:
      - fileId: $fileId1
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件2
    variables:
      - accountId: $accountId
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件3
    variables:
      - accountId: $accountId
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId3: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件4
    variables:
      - accountId: $accountId
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId4: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 获取文件列表，fileId1不在返回结果中，因为fileId1不属于accountId
    variables:
      - accountId: $accountId
      - pageNo: 1
      - pageSize: 10
      - fileIds: $fileId1,$fileId2,$fileId3,$fileId4
    api: api/file_template/files/get_files.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]