- config:
    name: "新下载分享用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - fileId1: eef906cff97549f6b6b9f66dd2e00aa6
      - imagfileId: "5d134d1a5033458293a122bded604950"

- test:
    name: "创建签署人账号 - gan"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId
      - yuzan_accountId: content.data.accountId


- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: z111abcabcabcabcabc
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "使用caogu_accountId创建机构"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId



- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: "8fbe5926547e40149978fb8c5a448394"
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - accountId: $yuzan_accountId
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 一步发起指定证书id用户手动签-成功
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: [{copierAccountId: $accountId_ckl,copierIdentityAccountId: "", copierIdentityAccountType: 0}]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "********新分享下载",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      },
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $yuzan_accountId
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 0,
            "signerRoleType": "0",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 100,
                "posY": 100
              },
            "signDateBeanType": "2",
            "signDateBean": { "fontSize": 50,"posX": 220,"posY": 250,"posPage": "1" },
            "signType": 1
          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: "获取签署链接"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询签署区
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200] #content.data.signfields.0.signerAccountId
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId
      - signfieldId1: content.data.signfields.1.signfieldId


#签署人维度    #签署人维度       #签署人维度
- test:
    name:  查询流程配置
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - querySpaceAccountId:
      - resourceShareId:
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[{'name': '查看', 'code': 'see', 'type': 'text'}, {'name': '签署', 'code': 'sign', 'type': 'text'}, {'name': '下载', 'code': 'download', 'type': 'text'}, {'name': '拒签', 'code': 'refuse', 'type': 'text'}, {'name': '法律咨询', 'code': 'legalAdvice', 'type': 'text'}, {'name': '语言切换', 'code': 'languageSwitch', 'type': 'text'}, {'name': '返回列表', 'code': 'list', 'type': 'text'}, {'name': '下载APP', 'code': 'downloadApp', 'type': 'text'}, {'name': '签署完成文案提示', 'code': 'signDoneDesc', 'type': 'text'}]]

- test:
    name:  " $sixian_organize_id 查询流程配置"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $sixian_organize_id
      - querySpaceAccountId:
      - resourceShareId:
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[]]

- test:
    name:  发起人和签署人是同一人 无拒签  有撤回
    variables:
      - app_id: "**********"
      - flowId: "f2610b19d97340c1ba294e00c86224f5"
      - queryAccountId: "3986d7544d9848f186ee9c5dcd342de1"
      - resourceShareId:
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[{'name': 'app引流', 'code': 'appDrainage', 'type': 'text'}, {'name': '法律咨询', 'code': 'legalAdvice', 'type': 'text'}, {'name': '返回', 'code': 'back', 'type': 'text'}, {'name': '上传附件', 'code': 'attachment', 'type': 'text'}, {'name': '扫码签署', 'code': 'scanSign', 'type': 'text'}, {'name': '语言切换', 'code': 'languageSwitch', 'type': 'text'}, {'name': '返回列表', 'code': 'list', 'type': 'text'}, {'name': '下载APP', 'code': 'downloadApp', 'type': 'text'}, {'name': '签署完成文案提示', 'code': 'signDoneDesc', 'type': 'text'}]
]


- test:
    name: "流程参与人查询 - simpleconfig"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - resourceShareId: ""
    api: api/share/simpleconfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[{'name': '查看', 'code': 'see', 'type': 'text'}]]

- test:
    name: " v1/signflows/{flowId}/detail  企业合同查看权限"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - operatorid: $yuzan_accountId
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]


      #- test:
      #    name: pdf转图片 查看权限
      #    variables:
      #      - app_id: ${ENV(gan_appid_download)}
      #      #      - flowId: $sign_flow_one
      #      - pageNum:
      #      - imageSize:
      #      - accountId: $yuzan_accountId
      #    api: api/file_template/files/get_viewimages.yml
      #    validate:
#      - eq: ["status_code",200]
#      - eq: ["content.data.signBillingWay",2]


- test:
    name: 企业合同获取流程签署或预览地址
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - urlType: 0
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - organizeId: $sixian_organize_id
    api: api/iterate_cases/getShowUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: "根据flowId查询打包流程文档，返回下载地址  不加账号 也可以下载？？？"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/signflow/signflows/documentsPack.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.code",0]

- test:
    name: "签署中 签署详情页下载 /v1/signflows/{flowId}/documents/pack"
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - querySpaceAccountId: $yuzan_accountId
      - menuId: ''
      - operator_id: $yuzan_accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]

- test:
    name: "签署完成后 签署详情页下载 /v1/signflows/{flowId}/documents/pack"
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: "def5a1af61944932bd1f9bf312c3f4a0"
      - queryAccountId: $yuzan_accountId
      - querySpaceAccountId: $yuzan_accountId
      - menuId: ''
      - operator_id: $yuzan_accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]

- test:
    name: "签署完成后 签署详情页下载 异步下载"
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - querySpaceAccountId: $yuzan_accountId
      - menuId: '&asyncDownload=true'
      - operator_id: $yuzan_accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_eq: ["content.data.downloadUrl",32]
      - eq: ["content.data.asyncDownload",true]
      - contains: ["content.message",成功]
    extract:
      - downloadCode: content.data.downloadUrl

- test:
    name: "查询详情，主要查询processId"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - operatorid: $yuzan_accountId
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
    extract:
      - processId: content.data.processId
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}

- test:
    name: "经办合同-批量合同列表-子任务下载    /v1/processes/{processId}/documents/pack"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
    api: api/signflow/signflows/processIdDocumentsPackWithPar.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.code",0]


#使用已注销的人  #使用已注销的人   #使用已注销的人    #使用已注销的人
- test:
    name:  发起人和签署人是同一人 无拒签  有撤回
    variables:
      - app_id: "**********"
      - flowId: "6569cb4510e147c196efffd7022effbb"
      - queryAccountId: "f6ce2c48a4424fec8e84f6fde4abb371"
      - resourceShareId:
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[{'name': 'app引流', 'code': 'appDrainage', 'type': 'text'}, {'name': '下载', 'code': 'download', 'type': 'text'}, {'name': '查看证据链', 'code': 'showEvidence', 'type': 'text'}, {'name': '法律咨询', 'code': 'legalAdvice', 'type': 'text'}, {'name': '返回', 'code': 'back', 'type': 'text'}, {'name': '上传附件', 'code': 'attachment', 'type': 'text'}, {'name': '扫码签署', 'code': 'scanSign', 'type': 'text'}, {'name': '语言切换', 'code': 'languageSwitch', 'type': 'text'}, {'name': '返回列表', 'code': 'list', 'type': 'text'}, {'name': '下载APP', 'code': 'downloadApp', 'type': 'text'}, {'name': '签署完成文案提示', 'code': 'signDoneDesc', 'type': 'text'}]]

- test:
    name: "已注销-个人签署完成后 签署详情页下载 /v1/signflows/{flowId}/documents/pack"
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: "**********"
      - flowId: "6569cb4510e147c196efffd7022effbb"
      - queryAccountId: "f6ce2c48a4424fec8e84f6fde4abb371"
      - querySpaceAccountId: "f6ce2c48a4424fec8e84f6fde4abb371"
      - menuId: ''
      - operator_id: "f6ce2c48a4424fec8e84f6fde4abb371"
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435208]
      - contains: ["content.message","当前用户无权限"]

- test:
    name: "已注销-企业主体 签署完成后 签署详情页下载 /v1/signflows/{flowId}/documents/pack"
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: "**********"
      - flowId: "6569cb4510e147c196efffd7022effbb"
      - queryAccountId: "f6ce2c48a4424fec8e84f6fde4abb371"
      - querySpaceAccountId: "bb4364e663ad43ee9381e65232b5ae1f"
      - menuId: ''
      - operator_id: "bb4364e663ad43ee9381e65232b5ae1f"
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437188]
      - contains: ["content.message","无下载权限，请联系企业管理员"]

#抄送人   #抄送人   #抄送人   #抄送人   #抄送人   #抄送人
- test:
    name:  抄送人-查询流程配置
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $accountId_ckl
      - querySpaceAccountId:
      - resourceShareId:
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[{'name': '查看', 'code': 'see', 'type': 'text'}, {'name': '下载', 'code': 'download', 'type': 'text'}, {'name': '法律咨询', 'code': 'legalAdvice', 'type': 'text'}, {'name': '语言切换', 'code': 'languageSwitch', 'type': 'text'}, {'name': '返回列表', 'code': 'list', 'type': 'text'}, {'name': '下载APP', 'code': 'downloadApp', 'type': 'text'}, {'name': '签署完成文案提示', 'code': 'signDoneDesc', 'type': 'text'}]]

- test:
    name: "抄送人-签署中 签署详情页下载 /v1/signflows/{flowId}/documents/pack"
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - queryAccountId: $accountId_ckl
      - querySpaceAccountId: $accountId_ckl
      - menuId: ''
      - operator_id: $yuzan_accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]

#使用明秀账号下完全不相关的flowid  去查看和下载23e2b52b950b41068e6ecb8804dd2fcf
- test:
    name:  用明秀账号的flowid 查询流程配置
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: "23e2b52b950b41068e6ecb8804dd2fcf"
      - queryAccountId: $yuzan_accountId
      - querySpaceAccountId:
      - resourceShareId:
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-1437184]
      - eq: ["content.message",无权限操作]

- test:
    name: "用明秀账号的flowid 签署完成后 签署详情页下载 /v1/signflows/{flowId}/documents/pack"
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: "23e2b52b950b41068e6ecb8804dd2fcf"
      - queryAccountId: $yuzan_accountId
      - querySpaceAccountId: $yuzan_accountId
      - menuId: ''
      - operator_id: $yuzan_accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435208]
      - contains: ["content.message",当前用户无权限]



- test:
    name: "V2异步下载"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - request: {
        "appId": "**********",
        "accountId": "3986d7544d9848f186ee9c5dcd342de1",
        "authorizerId": "bb4364e663ad43ee9381e65232b5ae1f",
        "flowIdList": [
        {
          "processId": "ba45652f6eb047628e1238cab168ad88",
          "flowId": "6d2ca7a8c4494832a6b240a8460c2d7d"
        },
        {
          "processId": "fdc3aa504b9f4ee3afc67ea91432c20f",
          "flowId": "0d554a86ee904e0eb81a7f13380ea8d5"
        }
        ],
        "packageName": "批量下载-********.zip",
        "menuId": "MENU_ALL"
      }
    api: api/share/asyncPackFlow.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "执行成功"]

#0323  线上悟空-平台自动签 打开报错的问题
- test:
    name:  线上悟空-平台自动签获取config
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: "e9a417bda4d3426a9d02d753b05678f8"
      - queryAccountId: "8fbe5926547e40149978fb8c5a448394"
      - querySpaceAccountId: "8fbe5926547e40149978fb8c5a448394"
      - resourceShareId:
    api: api/share/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.pageConfig.configList",[
      {
        "name": "查看",
        "code": "see",
        "type": "text"
      },
      {
        "name": "下载",
        "code": "download",
        "type": "text"
      },
      {
        "name": "上传附件",
        "code": "attachment",
        "type": "text"
      }, {'name': '语言切换', 'code': 'languageSwitch', 'type': 'text'},

      {
        "name": "返回列表",
        "code": "list",
        "type": "text"
      },
      {
        "name": "下载APP",
        "code": "downloadApp",
        "type": "text"
      },
      {
        "name": "签署完成文案提示",
        "code": "signDoneDesc",
        "type": "text"
      }
      ]]
      - eq: ["status_code",200]
      - eq: ["content.code",0]

- test:
    name: 异步获取下载地址
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - params: downloadCode=$downloadCode
    api: api/v1/signflows/flowId/documents/download.yml
    validate:
      - eq: [status_code, 200]
      - contains: [content.data.downloadUrl, https]