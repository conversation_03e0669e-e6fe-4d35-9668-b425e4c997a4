- config:
    name: "获取免登签署链接"
    base_url: ${ENV(footstone_signflow_initiate_url)}
    variables:
      - app_id1: ${ENV(mobile_shield_xuanyuan_appid)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - authkey_A: ${ENV(authkey_A)}
      - authkey_B: ${ENV(authkey_B)}
      - expire_flowid: ${ENV(authkey_expire_flowid)}
      - expire_oid: ${ENV(authkey_expire_oid)}

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying2019111411321231
      - creator: $accountId_ckl
      - idNumber: 1111111111111111171
      - idType: CRED_ORG_UNKNOWN
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId

- test:
    name: "创建发起人梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: "创建签署流程"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.processFlows.0.flowId


- test:
    name: "添加企业签署区"
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':400, 'posY':400}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'2', 'authorizedAccountId':$dongying_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "获取免登链接 - 流程未开启"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: ""
      - authorizedAccountId: ""
      - needShortLink: true
      - urlType: ""
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437103]
      - eq: ["content.message", "流程未开启"]


- test:
    name: 开启流程
    variables:
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "获取中间页长链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: ""
      - authorizedAccountId: ""
      - needShortLink: false
      - urlType: ""
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url.url","http"]
      - eq: ["content.data.url.shortUrl", null]
      - eq: ["content.data.urlWeb", null]
      - eq: ["content.data.urlH5", null]


- test:
    name: "获取中间页免登长链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: $authkey_A
      - authorizedAccountId: ""
      - needShortLink: false
      - urlType: ""
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url.url",$authkey_A]
      - eq: ["content.data.url.shortUrl", null]
      - eq: ["content.data.urlWeb", null]
      - eq: ["content.data.urlH5", null]

- test:
    name: "获取中间页长链+短链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: ""
      - authorizedAccountId: ""
      - needShortLink: true
      - urlType: ""
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url.url","http"]
      - contains: ["content.data.url.shortUrl","http"]
      - eq: ["content.data.urlWeb", null]
      - eq: ["content.data.urlH5", null]

- test:
    name: "获取中间页免登长链+免登短链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: $authkey_A
      - authorizedAccountId: ""
      - needShortLink: true
      - urlType: ""
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url.url",$authkey_A]
      - contains: ["content.data.url.shortUrl","http"]
      - eq: ["content.data.urlWeb", null]
      - eq: ["content.data.urlH5", null]


- test:
    name: "获取签署页长链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: ""
      - authorizedAccountId: ""
      - needShortLink: false
      - urlType: 1
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.url", null]
      - contains: ["content.data.urlWeb.url",http]
      - eq: ["content.data.urlWeb.shortUrl", null]
      - contains: ["content.data.urlH5.url",http]
      - eq: ["content.data.urlH5.shortUrl",null]


- test:
    name: "获取签署页免登长链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: $authkey_A
      - authorizedAccountId: ""
      - needShortLink: false
      - urlType: 1
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.url", null]
      - contains: ["content.data.urlWeb.url",$authkey_A]
      - eq: ["content.data.urlWeb.shortUrl", null]
      - contains: ["content.data.urlH5.url",$authkey_A]
      - eq: ["content.data.urlH5.shortUrl",null]


- test:
    name: "获取签署页长链+短链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: ""
      - authorizedAccountId: ""
      - needShortLink: true
      - urlType: 1
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.url", null]
      - contains: ["content.data.urlWeb.url",http]
      - contains: ["content.data.urlWeb.shortUrl", http]
      - contains: ["content.data.urlH5.url",http]
      - contains: ["content.data.urlH5.shortUrl",http]

- test:
    name: "获取签署页免登长链+免登短链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: ""
      - authKey: $authkey_A
      - authorizedAccountId: ""
      - needShortLink: true
      - urlType: 1
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.url", null]
      - contains: ["content.data.urlWeb.url",$authkey_A]
      - contains: ["content.data.urlWeb.shortUrl", http]
      - contains: ["content.data.urlH5.url",$authkey_A]
      - contains: ["content.data.urlH5.shortUrl",http]

- test:
    name: "获取指定签署主体、指定appScheme、签署页免登长链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $accountId_ckl
      - needShortLink: false
      - urlType: 1
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.url", null]
      - contains: ["content.data.urlWeb.url",$authkey_A]
      - eq: ["content.data.urlWeb.shortUrl", null]
      - contains: ["content.data.urlH5.url",$authkey_A]
      - eq: ["content.data.urlH5.shortUrl",null]


- test:
    name: "获取指定签署主体、指定appScheme、中间页长链+短链"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $dongying_organize_id
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url.url",$authkey_A]
      - contains: ["content.data.url.shortUrl","http"]
      - eq: ["content.data.urlWeb", null]
      - eq: ["content.data.urlH5", null]


- test:
    name: "异常case - 获取中间页免登短链，flowid不存在"
    variables:
      - flowId: 1
      - signerAccountId: $accountId_ckl
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $dongying_organize_id
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message", "流程不存在:1"]

- test:
    name: "异常case - 获取中间页免登短链，signerAccountId不存在"
    variables:
      - flowId: $flowId1
      - signerAccountId: 1
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $dongying_organize_id
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435203]
      - eq: ["content.message", "用户账号不存在或已注销"]


- test:
    name: "异常case - 获取中间页免登短链，signerAccountId存在，但不是流程签署人"
    variables:
      - flowId: $flowId1
      - signerAccountId: $dongying_organize_id
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $dongying_organize_id
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437114]
      - eq: ["content.message", "当前用户不是流程参与人, 无权查看流程"]


- test:
    name: "异常case - 获取中间页免登短链，signerAccountId存在，但是与flowid不关联"
    variables:
      - flowId: $flowId1
      - signerAccountId: $caogu_accountId
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $dongying_organize_id
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437114]
      - eq: ["content.message", "当前用户不是流程参与人, 无权查看流程"]


- test:
    name: "异常case - 获取中间页免登短链，authorizedAccountId不存在"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: 1
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435203]
      - eq: ["content.message", "主体账号不存在或已注销"]

- test:
    name: "异常case - 获取中间页免登短链，authorizedAccountId存在，但是与signerAccountId不关联"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $caogu_accountId
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建签署流程"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId2: content.data.processFlows.0.flowId

- test:
    name: "添加个人签署区"
    variables:
      - flowId: $flowId2
      - posBean: {'posPage':'1','posX':400, 'posY':400}
      - signfield_file1: { 'fileId':$fileId1, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'0', 'authorizedAccountId':$accountId_ckl,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId


- test:
    name: 开启流程
    variables:
      - flowId: $flowId2
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 拒签流程
    variables:
      - flowId: $flowId2
      - refuseReason: '拒签不需要理由No Reason'
      - signfieldId: $signfieldId2
    api: api/signflow/signfields/signfieldsRefuse.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "异常case - 获取中间页免登短链，flowid已拒签"
    variables:
      - flowId: $flowId2
      - signerAccountId: $accountId_ckl
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $dongying_organize_id
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建签署流程"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId3: content.data.processFlows.0.flowId


- test:
    name: "删除流程"
    api: api/signflow/signflows/signflowsDelete.yml
    variables:
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "异常case - 获取中间页免登短链，flowid已删除"
    variables:
      - flowId: $flowId3
      - signerAccountId: $accountId_ckl
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $dongying_organize_id
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437105]
      - eq: ["content.message", "流程已删除"]


- test:
    name: "发起人撤回流程"
    variables:
      - flowId: $flowId1
      - operatorId: $accountId_ckl
      - revokeReason: 撤回
    api: api/saas_api_miandeng/revoke.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",'成功']

- test:
    name: "异常case - 获取中间页免登短链，flowid已撤回"
    variables:
      - flowId: $flowId1
      - signerAccountId: $accountId_ckl
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: $dongying_organize_id
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: "创建签署流程"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay":"normal","autoInitiate":true,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity": "${get_timestamp_13(507)}","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId_expire: content.data.processFlows.0.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}
      #
      #- test:
      #    name: 开启流程
      #    variables:
      #      - flowId: $flowId_expire
      #    api: api/signflow/signflows/signflowsStart.yml
      #    validate:
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
#    teardown_hooks:
#      - ${hook_sleep_n_secs(5)}


- test:
    name: "异常case - 获取中间页免登短链，flowid已过期"
    variables:
      - flowId: $expire_flowid
      - signerAccountId: $expire_oid
      - appScheme: esign://demo/signBack
      - authKey: $authkey_A
      - authorizedAccountId: ""
      - needShortLink: true
      - urlType: 0
    api: api/saas_api_miandeng/getUrlWithAuthkey.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]









