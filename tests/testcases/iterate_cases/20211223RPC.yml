- config:
    name: "********"
    base_url: ${ENV(item_url)}
    variables:
      - group: ${ENV(envCode)}
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - fileId: ${ENV(file_id_in_tengqing_PaaS_id_without_real_with_willingness)}
      - account_id1: ${ENV(account_id1_in_tengqing_PaaS_id_without_real_with_willingness)}
      - account_id2: ${ENV(account_id2_in_tengqing_PaaS_id_without_real_with_willingness)}

- test:
    name: "发起流程，包含两个非实名签"
    api: api/iterate_cases/createFlowOneStep.yml
    variables:
      json: {"docs":[{"fileId":"$fileId","fileName":"2页.pdf"}],"flowInfo":{"flowConfigItemBean":{"buttonConfig":{"propButton":"1,2,6,11"}},"autoArchive":true,"autoInitiate":true,"businessScene":"两个非实名签","contractRemind":65,"initiatorAccountId":"$account_id1","initiatorAuthorizedAccountId":"$account_id1","flowConfigInfo":{"noticeType":"","redirectUrl":"","signPlatform":"","willTypes":[],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1","authorizedAccountId":"$account_id1","noticeType":""},"signfields":[{"fieldType":0,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":400,"posY":400},"signTimeFormat":"yyyy年MM月dd日","sealType":"","signType":1,"width":150}],"thirdOrderNo":"11111"},{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id2","authorizedAccountId":"$account_id2","noticeType":""},"signfields":[{"fieldType":0,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$fileId","posBean":{"posPage":"2","posX":400,"posY":400},"signTimeFormat":"yyyy年MM月dd日","sealType":"","signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]

- test:
    name: "查询该流程的实名记录，包含实名id"
    api: api/realname/identityRecord.yml
    variables:
      accountId: $account_id1
      flowId: $flow_id
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.realNameRecord.0.authId, 19]

- test:
    name: "查询签署区"
    api: api/iterate_cases/Search_signfields_2.yml
    variables:
      signfieldIds: ''
      flowId: $flow_id
    extract:
      - signfieldId1: content.data.signfields.0.signfieldId
      - signfieldId2: content.data.signfields.1.signfieldId
    validate:
      - eq: [status_code, 200]

- test:
    name: "查询用户2的印章列表"
    api: api/iterate_cases/getSignSeals.yml
    variables:
      accountId: $account_id2
      flowId: $flow_id
      authorizerId: ''
      signerAccountId: ''
    validate:
      - eq: [status_code, 200]
    extract:
      - personalSealId: content.data.personalSeals.0.sealId
      - personalSealFileKey: content.data.personalSeals.0.sealFileKey

- test:
    name: "获取用户2的短信验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      accountId: $account_id2
      flowId: $flow_id
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    extract:
      - bizId: content.data.bizId

- test:
    name: "校验用户2的短信验证码"
    api: api/realname/realname/checkSmsa.yml
    variables:
      accountId: $account_id2
      bizId: $bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: "用户2签署"
    api: api/signflow/v3/addUpdateExecute.yml
    variables:
      flowId: $flow_id
      json: {"async":true,"accountId":"$account_id2","updateSignfields":[{"sealId":"$personalSealId","sealFileKey":"$personalSealFileKey","signfieldId":"$signfieldId2","actorIndentityType":0,"assignedItem":false,"assignedPosbean":true,"assignedSeal":false,"authorizedAccountId":"$account_id2","autoExecute":false,"signDateBeanType":1,"extendFieldDatas":{},"fileId":"$fileId","order":1,"posBean":{"addSignTime":false,"posPage":"2","posX":131,"posY":233,"qrcodeSign":false,"width":200,"signDateBean":{"posPage":"1","fontName":"simsun","format":"yyyy年MM月dd日","fontSize":12,"posX":9,"posY":186},"sealType":1,"signType":1,"signerAccountId":"$account_id2"}}],"approvalPolicy":0,"dingUser":{}}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: "查询用户1的印章列表"
    api: api/iterate_cases/getSignSeals.yml
    variables:
      accountId: $account_id1
      flowId: $flow_id
      authorizerId: ''
      signerAccountId: ''
    validate:
      - eq: [status_code, 200]
    extract:
      - personalSealId: content.data.personalSeals.0.sealId
      - personalSealFileKey: content.data.personalSeals.0.sealFileKey

- test:
    name: "获取用户1的短信验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      accountId: $account_id1
      flowId: $flow_id
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    extract:
      - bizId: content.data.bizId

- test:
    name: "校验用户2的短信验证码"
    api: api/realname/realname/checkSmsa.yml
    variables:
      accountId: $account_id1
      bizId: $bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: "用户1签署"
    api: api/signflow/v3/addUpdateExecute.yml
    variables:
      flowId: $flow_id
      json: {"async":true,"accountId":"$account_id1","updateSignfields":[{"sealId":"$personalSealId","sealFileKey":"$personalSealFileKey","signfieldId":"$signfieldId1","actorIndentityType":0,"assignedItem":false,"assignedPosbean":true,"assignedSeal":false,"authorizedAccountId":"$account_id1","autoExecute":false,"signDateBeanType":1,"extendFieldDatas":{},"fileId":"$fileId","order":1,"posBean":{"addSignTime":false,"posPage":"2","posX":131,"posY":233,"qrcodeSign":false,"width":200,"signDateBean":{"posPage":"1","fontName":"simsun","format":"yyyy年MM月dd日","fontSize":12,"posX":9,"posY":186},"sealType":1,"signType":1,"signerAccountId":"$account_id1"}}],"approvalPolicy":0,"dingUser":{}}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: "查询该流程意愿记录，包含意愿id"
    api: api/realname/identityRecord.yml
    variables:
      accountId: $account_id1
      flowId: $flow_id
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.willingRecord.0.authId, 32]

- test:
    name: "查詢流程存证信息，operatorRealNameId和subjectRealNameId为null"
    api: api/iterate_cases/queryFlowEvi.yml
    variables:
      flowId: $flow_id
    validate:
      - eq: [status_code, 200]
      - str_eq: [content.data.signInfos.0.operatorRealNameId, None]
      - str_eq: [content.data.signInfos.0.subjectRealNameId, None]
      - str_eq: [content.data.signInfos.1.operatorRealNameId, None]
      - str_eq: [content.data.signInfos.1.subjectRealNameId, None]

- test:
    name: "查詢流程和文档存证信息，operatorRealNameId和creatorRealNameId为null"
    api: api/iterate_cases/queryFlowInfoWithDocEvi.yml
    variables:
      flowId: $flow_id
      step: 0
    validate:
      - eq: [status_code, 200]
      - str_eq: [content.data.operatorRealNameId, None]
      - str_eq: [content.data.creatorRealNameId, None]

- test:
    name: "查询流程签署存证信息，operatorRealNameId和subjectRealNameId为null"
    api: api/iterate_cases/queryFlowSignEvi.yml
    variables:
      flowId: $flow_id
      signSerialId: ''
    validate:
      - eq: [status_code, 200]
      - str_eq: [content.data.signInfoList.0.operatorRealNameId, None]
      - str_eq: [content.data.signInfoList.1.operatorRealNameId, None]
      - str_eq: [content.data.signInfoList.0.subjectList.0.subjectRealNameId, None]
      - str_eq: [content.data.signInfoList.1.subjectList.0.subjectRealNameId, None]
