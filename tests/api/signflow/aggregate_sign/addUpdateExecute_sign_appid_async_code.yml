#def: addUpdateExecute_sign($flowId,$addSignfields,$updateSignfields,$signfieldIds,$approvalPolicy,$dingUser,$accountId)
request:
  url: ${ENV(item_url)}/v3/signflows/$flowId/signfields/add-Update-Execute
  method: PUT
  #      headers: $${get_headers($app_id1)}
  headers: ${get_headers($app_id1,$operator_id)}
  json:
    async: true
    accountId: $accountId
    addSignfields: $addSignfields
    updateSignfields: $updateSignfields
    approvalPolicy: $approvalPolicy
    dingUser: $dingUser
    securityCode: $securityCode

# 异步添加或更新签署区并执行签署
