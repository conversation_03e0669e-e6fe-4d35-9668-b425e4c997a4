- config:
    name: 校验当前登录人是否有审批操作权限
    base_url: ${ENV(item_url)}



- test:
    name: 旧版审批
    api: api/queryApprovalQueryPermission/queryApprovalQueryPermission.yml
    variables:
      approvalId: 7d1d2218232540cfaf31752ad815f346
      queryAccountId: e489bdb1a78347b6aaeaa5a280b2ed60
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 执行成功]
      - eq: ["content.data.canApprove", true]

- test:
    name: 旧版审批-审批人与实际不符
    api: api/queryApprovalQueryPermission/queryApprovalQueryPermission.yml
    variables:
      approvalId: 7d1d2218232540cfaf31752ad815f346
      queryAccountId: 9c27b69717534e68857f9fb09c9dbb0b
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 执行成功]
      - eq: ["content.data.canApprove", false]



- test:
    name: 旧版审批-审批id不存在
    api: api/queryApprovalQueryPermission/queryApprovalQueryPermission.yml
    variables:
      approvalId: 11111111111111111111111111111111
      queryAccountId: e489bdb1a78347b6aaeaa5a280b2ed60

    validate:
      - eq: [status_code, 500]
      - contains: ["content.message", 获取标准审批信息失败.     运行时关键参数：获取标准审批信息失败]
      - eq: ["content.success", false]

- test:
    name: 旧版审批-已经审批完成的审批id
    api: api/queryApprovalQueryPermission/queryApprovalQueryPermission.yml
    variables:
      approvalId: ad79a55959a8459aaf19ae9202c4f84f
      queryAccountId: 9c27b69717534e68857f9fb09c9dbb0b

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 执行成功]
      - eq: ["content.data.canApprove", false]

- test:
    name: 旧版审批-审批拒绝了的的审批id
    api: api/queryApprovalQueryPermission/queryApprovalQueryPermission.yml
    variables:
      approvalId: 07f3ce6846e245b4b08ad7cc5ff6b853
      queryAccountId: 9c27b69717534e68857f9fb09c9dbb0b

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 执行成功]
      - eq: ["content.data.canApprove", false]




