- config:
    name: "悟空轩辕整合 - 验证企业-法人-经办人签署区"    #需要打开链接验证  查看是否有法人签
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "**********"
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - group: ''
      - fileId: ${ENV(fileId)}
      - oid_linjue_tsign: "34a2789ee9d348128fbfe01bf16f714d"  #linjue

- test:
    name: v3一步发起 多主体经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: '', noticeType: '', redirectUrl: '', signPlatform: ''}
      - json: {
        "docs":  $docs,
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "v3一步发起 多主体经办人签",
          "flowConfigInfo": {
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": ""
            },
            "signConfig": {
              "redirectUrl": "",
              "signPlatform": ""
            }
          },
          "hashSign": false,
          "remark": "印章平台",
          "signValidity": *************
        },
        "signers": [
        {
          "signOrder": 1,
          "signerAccount": {
            "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
            "authorizedAccountId": "969c37538edc4631a61d45d637affce8"
          },
          "signfields": [
          {
            "autoExecute": false,
            "actorIndentityType": 4,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 100,
              "posY": 100
            },
            "signType": 1
          },
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "signType": 1
          }
          ],
          "thirdOrderNo": "xyz"
        }
        ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $oid_linjue_tsign
      - urlType: 0
      - multiSubject: true
      - organizeId:
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: v3一步发起 多主体经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: '', noticeType: '', redirectUrl: '', signPlatform: ''}
      - json: {
        "docs":  $docs,
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "v3一步发起 多主体经办人签",
          "flowConfigInfo": {
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": ""
            },
            "signConfig": {
              "redirectUrl": "",
              "signPlatform": ""
            }
          },
          "hashSign": false,
          "remark": "印章平台",
          "signValidity": *************
        },
        "signers": [
        {
          "signOrder": 1,
          "signerAccount": {
            "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
            "authorizedAccountId": "969c37538edc4631a61d45d637affce8"
          },
          "signfields": [
          {
            "autoExecute": false,
            "actorIndentityType": 4,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 100,
              "posY": 100
            },
            "signType": 1
          },
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "signType": 1
          }
          ],
          "thirdOrderNo": "xyz"
        }
        ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: v3一步发起 多主体经办人签 - 东营
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - json: {
        "docs":  $docs,
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "v3一步发起 多主体经办人签",
          "flowConfigInfo": {
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": ""
            },
            "signConfig": {
              "redirectUrl": "",
              "signPlatform": ""
            }
          },
          "hashSign": false,
          "remark": "印章平台",
          "signValidity": *************
        },
        "signers": [
        {
          "signOrder": 1,
          "signerAccount": {
            "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
            "authorizedAccountId": "969c37538edc4631a61d45d637affce8"
          },
          "signfields": [
          {
            "autoExecute": false,
            "actorIndentityType": 4,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 100,
              "posY": 100
            },
            "signType": 1
          },
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "certId": "",
            "fileId": $fileId,
            "sealType": "",
            "posBean": {
              "addSignTime": true,
              "keyword": "乙方",
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "signType": 0
          }
          ],
          "thirdOrderNo": "xyz"
        }
        ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: v3一步发起 多主体经办人签 - 指定印章类型
    variables:
      - json: {
        "docs": [
        {
          "encryption": 0,
          "fileId": "2ef966f873444a59b7447de4c3823131",
          "fileName": "1111.pdf",
          "source": 0
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "v3非注册流程=手绘章",
          "flowConfigInfo": {
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": ""
            },
            "signConfig": {
              "redirectUrl": "",
              "signPlatform": "1,2,3,4"
            }
          },

          "hashSign": false,

          "remark": "印章平台",

          "signValidity": *************
        },

        "signers": [
        {
          "signOrder": 1,
          "signerAccount": {
            "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
            "authorizedAccountId": "34a2789ee9d348128fbfe01bf16f714d"
          },
          "signfields":
            [
            {
              "autoExecute": false ,
              "actorIndentityType": 0,
              "sealId": "",
              "fileId": "2ef966f873444a59b7447de4c3823131",
              "posBean": {
                "addSignTime": true,
                "posPage": "5",
                "posX": 444,
                "posY": 270
              },
              "assignedPosbean": "true",
              "sealType": "",
              "signDateBeanType": "1",
              "signDateBean": {
                "format": "yyyy       MM       dd",
                "posPage": "5",
                "fontSize": 14,
                "posX": 343,
                "posY": 200,
                "fontName": "simsun"
              },
              "signType": 1,
              "width": 136
            } ,
            {
              "autoExecute": false ,
              "actorIndentityType": 0,
              "sealId": "",
              "fileId": "2ef966f873444a59b7447de4c3823131",
              "posBean": {
                "addSignTime": true,
                "posPage": "5",
                "posX": 444,
                "posY": 370
              },
              "assignedPosbean": "true",
              "sealType": "",
              "signDateBeanType": "1",
              "signDateBean": {
                "format": "yyyy-MM-dd",
                "posPage": "5",
                "fontSize": 14,
                "posX": 343,
                "posY": 100,
                "fontName": "simsun"
              },
              "signType": 1,
              "width": 136
            },
            {
              "autoExecute": false ,
              "actorIndentityType": 0,
              "sealId": "",
              "fileId": "2ef966f873444a59b7447de4c3823131",
              "posBean": {
                "addSignTime": true,
                "posPage": "5",
                "posX": 444,
                "posY": 470
              },
              "assignedPosbean": "true",
              "sealType": "",
              "signDateBeanType": "1",
              "signDateBean": {
                "format": "yyyy-MM-dd",
                "posPage": "5",
                "fontSize": 14,
                "posX": 343,
                "posY": 200000,
                "fontName": "simsun"
              },
              "signType": 1,
              "width": 136
            }
            ],
          "thirdOrderNo": "企业2"
        }
        ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name:  获取批量签署url
    variables:
      - json: {
        "operatorId": $oid_linjue_tsign,
        "redirectUrl": "",
        "signFlowIds": [$sign_flowId1,
                        $sign_flowId2,$sign_flowId3,$sign_flowId4,"02d954d32d4543c0914f14d08187d2b3"
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]
    teardown_hooks:
      - ${teardown_re_find($response)}
    extract:
      - batchSerialId: batchSerialId