- config:
    name: "0820接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf: data/个人借贷合同.pdf
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US
      - app_Id1: ${ENV(wukong_forcewill)}
      - random_16: ${get_randomNo_16()}
      - random_32: ${get_randomNo_32()}
      - random_letter: ${random_letter()}
      - random_str: ${random_str()}

- test:
    name: "创建签署人账号"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_1
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId: content.data.accountId

- test:
    name: "文件直传创建一个文件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 一步发起rpc接口-flowId为空
    variables:
      - flowId: ''
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ],
                   "remarkFields": [
                   {
                     "fileId": $fileId1,
                     "inputType": "2",
                     "posX": 100,
                     "posY": 100,
                     "posPage": 1,
                     "remarkContent": "你好\n你好",
                     "remarkFieldWidth": 100,
                     "remarkFieldHeight": 100,
                     "aiCheck": 2
                   }
                   ]
                  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name: 一步发起rpc接口-flowId不存在,纯数字小于32位
    variables:
      - flowId: $random_16
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'参数错误: 指定的flowId非法']

- test:
    name: 一步发起rpc接口-flowId32位纯数字
    variables:
      - flowId: $random_32
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name: 一步发起rpc接口-flowId32位数字+英文字母
    variables:
      - flowId: $random_str
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name: 一步发起rpc接口-flowId32位纯英文字母
    variables:
      - flowId: $random_letter
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name: 一步发起rpc接口-flowId不存在-超过32位
    variables:
      - flowId: '111122233344455566677788899900011'
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'参数错误: 指定的flowId非法']

- test:
    name: 一步发起rpc接口-flowId不存在-中文
    variables:
      - flowId: '测试接口'
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'参数错误: 指定的flowId非法']

- test:
    name: 一步发起rpc接口-flowId不存在-包含特殊字符
    variables:
      - flowId: '！@#￥%……&*（）123456'
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'参数错误: 指定的flowId非法']

- test:
    name: 一步发起rpc接口-flowId不存在-32位包含特殊字符
    variables:
      - flowId: '123456789012345678901234567890！@'
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'参数错误: 指定的flowId非法']

- test:
    name: 创建id
    api: api/iterate_cases/generateFlowId.yml
    extract:
      - sign_flowId1: content.data.flowId

- test:
    name: 一步发起rpc接口-正确的flowId
    variables:
      - flowId: $sign_flowId1
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowId", $sign_flowId1]
      - eq: ["content.message",执行成功]

- test:
    name: 一步发起rpc接口-已存在的flowId
    variables:
      - flowId: $sign_flowId1
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0820迭代"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",流程已存在]
      - eq: ["content.code",1435012]

- test:
    name: v2-RPC创建流程-指定印章列表
    variables:
      - json: {"appId":"${app_id}","clientId":"111","docs":[{"fileId":"${fileId1}"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署流程","configInfo":{"archiveLock":true,"batchDropSeal":true,"countdown":0},"createByApi":true,"flowConfigItemDTO":{"buttonConfig":{"propButton":"1"}},"flowId":"","initiatorAccountId":"${accountId}","initiatorAuthorizedAccountId":"${accountId}","payerAccountId":""},"signers":[{"signFields":[{"autoExecute":false,"fileId":"${fileId1}","posBean":{"posPage":"1","posX":100,"posY":100},"sealFileKey":"","sealId":"","sealIds":["111"],"sealType":"0,1","signType":1,"signerRoleType":"2"}],"signOrder":1,"signerAccount":{"signerAccountId":"${accountId}","signerAuthorizedAccountId":"${pOid1}"}}]}
    api: api/signflow/signflows/createFlowV2Input.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署主体不是企业]
      - eq: ["content.code",1435002]

- test:
    name: v2-RPC创建流程-backToRedirectUrl为true
    variables:
      - json: {"appId":"${app_id}","clientId":"111","docs":[{"fileId":"${fileId1}"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署流程","configInfo":{"archiveLock":true,"batchDropSeal":true,"countdown":0,"backToRedirectUrl": true,"redirectUrl":"https://www.baidu.com"},"createByApi":true,"flowConfigItemDTO":{"buttonConfig":{"propButton":"1"}},"flowId":"","initiatorAccountId":"${accountId}","initiatorAuthorizedAccountId":"${accountId}","payerAccountId":""},"signers":[{"signFields":[{"autoExecute":false,"fileId":"${fileId1}","posBean":{"posPage":"1","posX":100,"posY":100},"sealType":"0,1","signType":1,"signerRoleType":"0"}],"signOrder":1,"signerAccount":{"signerAccountId":"${accountId}","signerAuthorizedAccountId":"${accountId}"}}]}
    api: api/signflow/signflows/createFlowV2Input.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询config，返回backToRedirectUrl为true
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.backToRedirectUrl",true]




- test:
    name: v2-RPC创建流程-backToRedirectUrl为true
    variables:
      - json: {
        "appId": "${app_id}",
        "clientId": "111",
        "flowInfo": {
          "autoInitiate": true,
          "autoArchive": true,
          "businessScene": "1页",
          "initiatorAccountId": "${accountId}",
          "initiatorAuthorizedAccountId": "${accountId}",
          "signValidity": null,

          "configInfo": {
            "archiveLock": true,
            "batchDropSeal": true,
            "countdown": 0,
            "backToRedirectUrl": true,
            "redirectUrl": "https://www.baidu.com"
          }
        },
        "docs": [
        {
          "bizFileId": null,
          "fileId": "${fileId1}",
          "fileName": "1页.pdf",
          "fileHash": null,
          "filePassword": null,
          "encryption": 0,
          "source": 0
        }
        ],
        "attachments": null,
        "signers": [
        {
          "signOrder": 1,
          "forcedReadingTime": null,
          "forceRead": false,
          "signTaskType": 0,
          "signerAccount": {
            "signerRoleLabel": "签署方2",
            "signerAccountId": "091bc7d9666146a2b44f699620623a48",
            "signerAccount": null,
            "signerAccountName": "陈凯丽",
            "signerNickname": null,
            "signerAuthorizedAccountId": "091bc7d9666146a2b44f699620623a48",
            "signerAuthorizedAccountName": "陈凯丽",
            "allowedUploadAttachment": false,
            "willTypes": [
              "FACE",
              "CODE_SMS",
              "EMAIL",
              "SIGN_PWD"
            ],
            "noticeType": "5",
            "attachmentConfigs": null,
            "signTipsTitle": null,
            "signTipsContent": null,
            "requiredWilling": true,
            "authWay": "REAL_NAME_AUTH",
            "accessToken": null,
            "requiredRealName": true,
            "signerLicense": "362323199201061068",
            "signerLicenseType": "CRED_PSN_CH_IDCARD"
          },
          "participantStartType": "PARTICIPANT_ASSIGN_ACCOUNT",
          "participantStartValue": null,
          "licenseStart": true,
          "signFields": [
          {
            "signerRoleType": "0",
            "fileId": "${fileId1}",
            "sealType": "0,1",
            "handDrawnWay": "0,1",
            "signType": 1,
            "autoExecute": false,
            "posBean": {
              "posPage": "1",
              "posX": 145.5,
              "posY": 3337.77,
              "signDateBeanType": 2
            },
            "mustSign": true
          }
          ],
          "remarkFields": null
        }
        ],
        "recipients": null
      }
    api: api/signflow/signflows/createFlowV2Input.yml
    extract:
      - sign_flowId1_1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]