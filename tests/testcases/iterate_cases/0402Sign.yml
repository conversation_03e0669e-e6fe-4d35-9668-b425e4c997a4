- config:
    name: "0402接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}

- test:
    name: "X-Tsign-Open-App-Id-NO-REALNAME下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber:
      - orgLegalName:
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "文件直传创建一个文件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: "一步到位创建流程-对内-代泗县深泉纯净水有限公司经办人签-单页签署-模板印章"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - json: {"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"经办人签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"2","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一步到位创建流程-对内-代泗县深泉纯净水有限公司经办人签-单页签署-模板印章"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - json: {"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"经办人签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":0,"signerRoleType":"2","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "查询签署区"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - accountId: $yuzan_accountId
      - signfieldIds: None
      - flowId: $sign_flowId
    #    extract:
    #       - sign_flowId: content.data.flowIds.0
    api: api/iterate_cases/search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.signfields.0.actorIndentityType",4]

- test:
    name: "根据登录凭证获取账号信息"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - idcard: ***********
      - type: MOBILE
    extract:
      - standard_accountId: content.data.items.0.accountId
    api: api/iterate_cases/getByIdcard.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "查询签署可用印章列表"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - accountId: $standard_accountId
      - flowId: $sign_flowId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $standard_accountId
    #    extract:
    #       - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]