- config:
    name: "审批相关的测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(xuanyuan_realname)}
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - mobile_fengjiu: ***********
      - mobile_caogu: ***********
      - sealId1: 8fa71acc-425d-4270-9fa7-b0e91f57fbef

- test:
    name: "创建草谷账号"
    variables:
      - app_id: $write_app_Id
      - group: $w_group
      - thirdPartyUserId: caogu201911141132
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - zhelan_accountId: content.data.accountId

- test:
    name: "创建机构东营伟信建筑安装工程有限公司"
    variables:
      - app_id: $write_app_Id
      - group: $w_group
      - thirdPartyUserId: dongying2019111411321231
      - creator: $zhelan_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId

- test:
    name: "获取实名组织"
    variables:
      - orgId: $dongying_organize_id
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    extract:
      - realname_oid: content.data.base.ouid
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询实名组织下的企业印章"
    variables:
      - orgId: $realname_oid
    api: api/seals/seals/select_c_seals.yml
    extract:
      - sealId1: content.data.seals.1.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建签署人凤九账号"
    variables:
      - thirdPartyUserId: fengjiu20191114199132191431252002
      - name: 曾艳
      - idNumber: 362324199301130629
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - fengjiu_accountId: content.data.accountId


- test:
    name: "创建签署流程"
    variables:
      - autoArchive: true
      - businessScene: 您有一份待签署的任务
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $zhelan_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: $write_app_Id
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "企业签名域-单页签署"
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$fengjiu_accountId,"actorIndentityType":"2","authorizedAccountId":$dongying_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "根据登陆凭证获取accountId"
    variables:
      - idcard: $mobile_fengjiu
      - type: MOBILE
    api: api/iterate_cases/getByIdcard.yml
    extract:
      - stu_accountId: content.data.items.0.accountId

- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId
      - flowId: $sign_flowId
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加或更新签署区并执行签署"
    variables:
      - flowId: $sign_flowId
      - accountId: $stu_accountId
      - addSignfields:
      - updateSignfields: [{"sealId":$sealId1,"signerOperatorAuthorizerId":$realname_oid,"signerOperatorId":$stu_accountId,"signfieldId":$signfieldId1}]
      - signfieldIds:
    api: api/iterate_cases/addUpdateExecute_sign.yml
    extract:
      - approveFlowId1: content.data.signfieldSignResult.0.approveFlowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "印章审批状态及列表提供（前端用）"
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $stu_accountId
      - authorizerIds: $realname_oid
    api: api/iterate_cases/getSealApprovals.yml
    extract:
      - approval_accountId: content.data.sealApprovals.0.approvals.0.approvers.0.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.sealApprovals.0.approvals.0.status", 1]
      - eq: ["content.data.status", 1]

- test:
    name: "根据审批id与操作人获取审批链接"
    variables:
      - approvalId: $approveFlowId1
      - queryAccountId: $stu_accountId
    api: api/iterate_cases/getApprovalUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - len_gt: ["content.data.shortUrl",0]

- test:
    name: "审批列表-审批中"
    variables:
      - flowId: $sign_flowId
      - params: order=1&queryAccountId=$stu_accountId&querySubjectId=$realname_oid
    api: api/v3/signflows/flowId/sealApproval/list.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.status", 1]

- test:
    name: "审批详情-一级审批审批中-二级审批等待开始"
    variables:
      - flowId: $sign_flowId
      - params: approvalId=$approveFlowId1&operatorId=$stu_accountId&operatorSubjectId=$realname_oid
    api: api/v3/signflows/flowId/sealApproval/detail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.taskInfoList.0.taskStatus", 1]
      - eq: ["content.data.taskInfoList.1.taskStatus", 0]