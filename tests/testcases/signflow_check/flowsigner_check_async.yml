- config:
    name: 签署oid校验
    base_url: ${ENV(footstone_api_url)}
    variables:
      - orgName: 东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: 13344445555
      - path_pdf: 个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - personOid1: 4468fe9323bc4bfeaf5f659af14b6754
      - personOid2: 824708b5195546eab28565ebb0203a1e
      - personOid3: 310913bf68594f8bb7c457215e0b4831
      - orgOid1: 4d4352cd0de849ab9daa507397086bf1
      - fileId1: 37492a3be0c64282b834a48f77db3c89
      - fileId: ${get_file_id($app_id,$path_pdf)}
      - fileId2: 33333a3be0c64282b834a48f77db3c89
      - m1: "19922222222"
      - m2: "198********"
      - e1: <EMAIL>
      - e2: <EMAIL>
      - pOid1: ${getOid($m1)}
      - pOid2: ${getOid($m2)}
      - pOid3: ${getOid($e1)}
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:
      - m3: "13260881366"
      - pOid_m: ${getOid($m3)}
      - tPUserId1: autotest1${get_randomNo()}
      - appid_xuanyuan: ${ENV(appId_xuanyuan)}
      - m4: "***********"
      - pOid_m1: ${getOid($m4)}

    "request":
    "base_url":

    "headers":
      "Content-Type": "application/json"

- test:
    name: 使用第三方userId 创建账号
    variables:
      - json:
          {
            "name": 梁贤红,
            "thirdPartyUserId": $tPUserId,
            "idNumber":"331082199211223080",
            "idType":"CRED_PSN_CH_IDCARD",
            "mobile":"***********"
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - oId1: content.data.accountId
    output:
      - $oId1



- test:
    name: 创建流程
    variables:
      - autoArchive: true
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $oId1
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]



- test:
    name: 添加流程文档：单文档
    variables:
      - flowId: $flowId1
      - doc1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: 添加用户手动签署-用户oid不存在
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId, 'signerAccountId':'1111111', 'actorIndentityType':'1', 'authorizedAccountId':'1111111','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: signfieldsCreate_handSign($flowId, $signfields)
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437165]
      - eq: ["status_code", 200]
      - contains: ["content.message", "当前项目不存在该账号，请检查账号id是否准确"]

- test:
    name: 添加用户手动签署-企业oid不存在
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId, 'signerAccountId':$oId1, 'actorIndentityType':'1', 'authorizedAccountId':'0000000','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: signfieldsCreate_handSign($flowId, $signfields)
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437165]
      - eq: ["status_code", 200]
      - contains: ["content.message", "当前项目不存在该账号，请检查账号id是否准确"]

- test:
    name: 用户自动签署_oid不存在
    variables:
      - flowId: $flowId1
      - app_id: $app_Id2
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId, 'authorizedAccountId':'********', 'order':1,  'posBean':$posBean, 'sealId':null,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437165]
      - eq: ["status_code", 200]
      - contains: ["content.message", "当前项目不存在该账号，请检查账号id是否准确"]


- test:
    name: 添加用户手动签署-fileid不存在
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId2, 'signerAccountId':$oId1, 'actorIndentityType':'1', 'authorizedAccountId':$oId1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
      - message: ${ENV(message2)}
    api: signfieldsCreate_handSign($flowId, $signfields)
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["status_code", 200]
      - contains: ["content.message",$message]

- test:
    name: 静默授权
    variables:
      - accountId: $oId1
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 用户自动签署_fileid不存在
    variables:
      - flowId: $flowId1
      - app_id: $app_Id2
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId2, 'authorizedAccountId':$oId1, 'order':1,  'posBean':$posBean, 'sealId':null,'signType':1}
      - message: ${ENV(message2)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["status_code", 200]
      - contains: ["content.message",$message]

- test:
    name: 创建流程
    variables:
      - autoArchive: false
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $oId1
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: 添加流程文档：单文档
    variables:
      - flowId: $flowId2
      - doc1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: 添加流程签署区
    variables:
      - flowId: $flowId2
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId,$flowId,$posBean,0,$oId1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $oId1
    extract:
      - sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]


- test:
    name: 添加或更新签署区并执行签署
    variables:
      - flowId: $flowId2
      - accountId: $oId1
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield: ${gen_signfield_data_V2(0,0,$fileId,$flowId2,$posBean,1,$oId1,$oId1,$sealId,1)}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $oId1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    extract:
      - serviceId: content.data.serviceId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 查询异步签署结果
    api: api/signflow/signflows/requsetResult.yml
    variables:
      - app_id: $app_Id2
      - service_Id: $serviceId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 开启流程，预期结果：失败，该流程已完结，无法继续签署，请重新创建新流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",该流程已完结，无法继续签署，请重新创建新流程]
      - eq: ["status_code", 200]

- test:
    name: 静默授权
    variables:
      - accountId: $oId1
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 一步发起用户自动签_fileid未添加
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ { autoExecute: true, actorIndentityType: 0, fileId: '1111111',   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",文件id不在签署流程中，请先添加文件到流程中，即可添加签署区]
      - eq: ["status_code", 200]



- test:
    name: 一步发起平台自动签_fileid未添加
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{platformSign: true,signOrder: 1,signerAccount: {  signerAccountId: $orgOid1,authorizedAccountId: $orgOid1}, signfields: [ { autoExecute: true, actorIndentityType: 2, fileId: '1111111',   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",文件id不在签署流程中，请先添加文件到流程中，即可添加签署区]
      - eq: ["status_code", 200]

- test:
    name: 一步发起用户手动签_fileid未添加
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ { autoExecute: false, actorIndentityType: 0, fileId: '1111111',   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",文件id不在签署流程中，请先添加文件到流程中，即可添加签署区]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-3668：签署兜底：创建账号
    variables:
      - app_id: $appid_xuanyuan
      - idNo: "331082199211223080"
      - mobile: null
      - name: "测试"
      - thirdPartyUserId: $tPUserId1
    api: api/user/account_third/create_account_appid.yml
    extract:
      - oid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $oid1


- test:
    name: ONLINEBUG-3668：签署兜底：创建流程
    variables:
      - app_id: $appid_xuanyuan
      - autoArchive: true
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $oid1
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-3668：签署兜底：添加流程文档：单文档
    variables:
      - app_id: $appid_xuanyuan
      - flowId: $flowId3
      - doc1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: ONLINEBUG-3668：签署兜底：添加流程签署区
    variables:
      - app_id: $appid_xuanyuan
      - flowId: $flowId3
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId,$flowId3,$posBean,0,$oid1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-3668：签署兜底：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $appid_xuanyuan
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-3668：签署兜底：查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $pOid_m
    extract:
      - sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]


- test:
    name: ONLINEBUG-3668：签署兜底：执行签署
    variables:
      - app_id: $appid_xuanyuan
      - flowId: $flowId3
      - accountId: $pOid_m
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield: {actorIndentityType: 0,assignedPosbean: true,authorizedAccountId: $pOid_m, autoExecute: false,  autoExecuteType: 0,fileId: $fileId,  flowId: $flowId3,  order: 1,posBean: $posBean ,sealId: $sealId1,  sealType: 1,  signType: 1,  signerAccountId: $pOid_m,  signerOperatorAuthorizerId: $pOid_m, signerOperatorId: $pOid_m,
                       synchExecute: true}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId1]
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $pOid_m
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.message", "签署人信息校验失败"]