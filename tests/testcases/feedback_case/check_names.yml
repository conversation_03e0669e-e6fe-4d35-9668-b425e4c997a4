- config:
    name: "线上问题补充集测脚本 - - 指定位置骑缝签"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_Id11: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US
      - app_Id1: ${ENV(wukong_forcewill)}
      - random_16: ${get_randomNo_16()}
      - random_32: ${get_randomNo_32()}
      - random_letter: ${random_letter()}
      - random_str: ${random_str()}
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}

- test:
    name: "一个列表-发起签署前-批量参与人二要素校验 - 1个用户，用户不存在"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"三二","uniqueId":"<EMAIL>"}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "一个列表-发起签署前-批量参与人二要素校验 - 1个用户，用户存在"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王瑶济","uniqueId":null}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]


- test:
    name: "一个列表两条数据-发起签署前-批量参与人二要素校验 一 2个参与人，一个已存在用户，一个不存在用户"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王瑶济","uniqueId":"***********"},{"name":"钟2","uniqueId":"***********"}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]


- test:
    name: "一个列表两条数据-发起签署前-批量参与人二要素校验 一 2个参与人，2个用户均不存在"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王0","uniqueId":"***********"},{"name":"钟2","uniqueId":"***********"}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]



- test:
    name: "一个列表两条数据-发起签署前-批量参与人二要素校验 一 2个参与人，2个用户均存在"
    variables:
      - app_id: $app_Id1
      - json: {"signers":[{"accounts":[{"name":"王瑶济","uniqueId":"***********"},{"name":"陈凯丽","uniqueId":"***********"}],"label":""}]}
    api: api/iterate_cases/checkNames.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]


