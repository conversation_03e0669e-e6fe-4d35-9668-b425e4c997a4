import logging
import os
import time

try:
    from elasticsearch import Elasticsearch
except Exception as e:
    val = os.system('pip install elasticsearch -i https://mirrors.aliyun.com/pypi/simple/')

logging.getLogger().setLevel(logging.DEBUG)


class ElasticSearch(object):

    def __init__(self):
        self._elastic_search = None
        self._index = None

    def build(self, url, port, username, password, use_ssl=False):
        pass

    def set_index(self, index):
        self._index = index

    def get_elastic_search(self):
        pass

    def get_sign_detail_by_process_id(self, process_id):
        return self._elastic_search.search(index=self._index,
                                           body='{"query": {"term": {"processId": "' + process_id + '"}}, "size": 1}')

    def process_id_exist(self, process_id):
        flag = False
        count = 1
        detail = self.get_sign_detail_by_process_id(process_id)
        while count <= 5:
            if detail.get('hits').get('total') > 0:
                flag = True
                break
            else:
                logging.debug("ElasticSearch中未查找到process_id为%s的流程，将在5秒后重试，当前第%s次，共5次", process_id, count)
                count += 1
                time.sleep(5)
                continue
        return flag

    def wait_till_finish(self, process_id, status=8):
        flag = False
        count = 1
        while count <= 5:
            detail = self.get_sign_detail_by_process_id(process_id)
            if detail.get('hits').get('hits')[0].get('_source').get('processStatus') == status:
                flag = True
                logging.debug("在第%s次ElasticSearch中查找到process_id为%s的流程状态为8", count, process_id)
                break
            else:
                logging.debug("ElasticSearch中查找到process_id为%s的流程状态不为8，当前状态为%s，将在5秒后重试，当前第%s次，共5次", process_id,
                              detail.get('hits').get('hits')[0].get('_source').get('processStatus'), count)
                count += 1
                time.sleep(5)
                continue
        return flag


class TestElasticSearch(ElasticSearch):

    def build(self, url='es-cn-i7m2b7kmp0038b1zn.elasticsearch.aliyuncs.com', port=9200, username='qianyi',
              password='i7m2b7kmp0038b1zn', use_ssl=False):
        self._elastic_search = Elasticsearch([url], http_auth=(username, password), port=port, use_ssl=use_ssl)
        self._index = 'process_info'
        return self

    def get_elastic_search(self):
        return self._elastic_search


class SmlElasticSearch(ElasticSearch):
    def build(self, url='es-cn-zvp2bzhk80001mlgz.elasticsearch.aliyuncs.com', port=9200, username='qianyi',
              password='zDrAKuNGAslKQe1o', use_ssl=False):
        self._elastic_search = Elasticsearch([url], http_auth=(username, password), port=port, use_ssl=use_ssl)
        self._index = 'process_info_alias'
        return self

    def get_elastic_search(self):
        return self._elastic_search


class OpenElasticSearch(ElasticSearch):
    def build(self, url='es-cn-7mz2dzpb0000utcdp.elasticsearch.aliyuncs.com', port=9200, username='xuanyuan',
              password='tPE05YvIYtY3yRK7', use_ssl=False):
        self._elastic_search = Elasticsearch([url], http_auth=(username, password), port=port, use_ssl=use_ssl)
        self._index = 'process_info_alias'
        return self

    def get_elastic_search(self):
        return self._elastic_search


class ElasticSearchBuilder(object):
    def get_builder(self):
        pass


class TestElasticSearchBuilder(ElasticSearchBuilder):
    def get_builder(self):
        return TestElasticSearch()


class SmlElasticSearchBuilder(ElasticSearchBuilder):
    def get_builder(self):
        return SmlElasticSearch()


class OpenElasticSearchBuilder(ElasticSearchBuilder):
    def get_builder(self):
        return OpenElasticSearch()


class ElasticSearchFactory(object):

    @staticmethod
    def build(env):
        builders = {"TEST": TestElasticSearchBuilder(), "SML": SmlElasticSearchBuilder(),
                    "OPEN": OpenElasticSearchBuilder()}
        return builders.get(env.upper()).get_builder().build()


if __name__ == '__main__':
    es1 = ElasticSearchFactory().build('TEST')
    print(es1.wait_till_finish('87c8bb425c984d44a41f4aa6f36261f5'))
