- config:
    name: "0917迭代测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - group: ''

- test:
    name: 查询流程详情
    api: api/iterate_cases/flowsDetailV2.yml
    variables:
      - flowId: ${ENV(dingFlowId)}
      - queryAccountId: ${ENV(dingFlowIdOperatorId)}
      - operatorid: ${ENV(dingFlowIdOperatorId)}
      - clientid:  DING_TALK
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 查询流程详情
    api: api/iterate_cases/queryAccountPrivilegeInput.yml
    variables:
      - flowId: ${ENV(dingFlowId)}
      - accountId: ${ENV(dingFlowIdOperatorId)}
      - clientid:  DING_TALK
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "执行成功"]