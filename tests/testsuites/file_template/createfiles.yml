- config:
    name: 文件操作相关的测试
    parameters:
      - contentMd5-contentType-fileName-file_path:
      - ["${get_file_base64_md5($path_pdf)}", "application/pdf", "个人借贷合同.pdf", $path_pdf]
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - accountId: "6bef1e4d74f645a2a6c7541cec8db5b8"
      - path_pdf: "data/个人借贷合同.pdf"
      - fileSize: 111
      - templateName: "测试模板"
      - fileMimeType: "application/pdf"
      - pageNo: 1
      - pageSize: 100
      - chunkedFields:
      - {"mimeType": "text/html","chunkedData": "PGh0bWw+Cgo8aGVhZD4KPHRpdGxlPui2heeugOWNlUhUTUwg6aG16Z2iPC90aXRsZT4KPC9oZWFkPgoKPGJvZHk+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjwvYm9keT4KCjwvaHRtbD4K", "sequence": 0, "pagePos": 5}
      - simpleFormFields: {"出借方": "1","借款方": "2018-12-21"}

    request:
      base_url: ${ENV(footstone_api_url)}
      headers:
        Content-Type: "application/json"

- test:
    name: 根据本地文件创建文件,包括上传文件，根据文件创建文件，获取文件信息，获取模板列表
    variables:
      contentMd5: $contentMd5
      contentType: $contentType
      fileName: $fileName
      fileSize: $fileSize
      file_path: $file_path
      fileKey: $fileKey
      accountId: $accountId
      name: $fileName
      fileId: $fileId
      pageNo: $pageNo
      pageSize: $pageSize
    testcase: testcases/file_template/createbyfilekey_action.yml

- test:
    name: 根据文件Hash创建文件,包括上传文件，根据文件创建文件，获取文件信息，获取模板列表
    variables:
      contentMd5: $contentMd5
      contentType: $contentType
      fileName: $fileName
      fileSize: $fileSize
      file_path: $file_path
      fileKey: $fileKey
      fileHash: $contentMd5
      accountId: $fileName
      name: $fileId
      fileId: $pageNo
      pageNo: $pageSize
      pageSize:
    testcase: testcases/file_template/createbyhash_action.yml

- test:
    name: 根据文件模板创建文件,包括上传文件，创建模板，根据模板创建文件，获取文件信息，获取模板列表
    variables:
      contentMd5: $contentMd5
      contentType: $contentType
      fileName: $fileName
      fileSize: $fileSize
      file_path: $file_path
      fileKey: $fileKey
      templateName: $templateName
      accountId: $chunkedFields
      chunkedFields: $fileName
      name: $simpleFormFields
      simpleFormFields: $templateId
      templateId: $fileId
      fileId: $pageNo
      pageNo: $pageSize
      pageSize:
    testcase: testcases/file_template/createbytemplate_action.yml

- test:
    name: 根据外部下载地址创建文件，包含上传文件，获取文件下载地址，根据外部下载地址创建文件，获取文件信息，获取模板列表
    variables:
      contentMd5: $contentMd5
      contentType: $contentType
      fileName: $fileName
      fileSize: $fileSize
      file_path: $file_path
      fileKey: $fileKey
      accountId: $fileName
      name: $url
      url: $fileId
      fileId: $pageNo
      pageNo: $pageSize
      pageSize:
    testcase: testcases/file_template/createbyurl_action.yml

