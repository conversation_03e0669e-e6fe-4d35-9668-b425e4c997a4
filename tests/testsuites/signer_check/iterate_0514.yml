config:
  name: 迭代case
#    variables:
testcases:

  0514迭代case-权限校验:
    testcase: testcases/signer_check/signer_checkV2.yml

  0514迭代case-二维码:
    testcase: testcases/signflow_check/signQr.yml

  0709迭代case-不开启附件:
    testcase: testcases/iterate_cases/0709notallowedUploadAttachment.yml

  0709迭代case-开启附件:
    testcase: testcases/iterate_cases/0709allowedUploadAttachment.yml

  0723迭代case:
    testcase: testcases/iterate_cases/0723Sign.yml

  0903迭代case-添加文档记录验签结果:
    testcase: testcases/iterate_cases/0903Sign.yml

  1015迭代case-签署人不存在gid，标准签签署完成后生成gid:
    testcase: testcases/iterate_cases/1015Sign.yml

  20220428附件下载-标准签:
    testcase: testcases/iterate_cases/20220428附件下载-标准签.yml

  20220428附件下载-非标准签:
    testcase: testcases/iterate_cases/20220428附件下载-非标准签.yml

  flow-manager调用频次优化:
    testcase: testcases/iterate_cases/flow-manager调用频次优化.yml

  20220412rpc自动签署:
    testcase: testcases/iterate_cases/20220412rpc自动签署.yml
