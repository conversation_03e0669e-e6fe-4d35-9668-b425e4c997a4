- config:
    name: "20220425附件下载"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id_origin: ${ENV(BZQ-App-Id)}
      - fileId1: ${get_file_id($app_id_origin,$path_pdf)}
      - fileId: ${get_file_id($app_id_origin,$path_pdf2)}
      - imagfileId: "5d134d1a5033458293a122bded604950"
#      - sixian: "8fbe5926547e40149978fb8c5a448394"
      - sixian: "5d49fc96e74a4cb993a108e2250c8b70" #miao0
      - dongying: "${ENV(orgid_dy)}"
      - mingxiu: "5e8899b894954a83af3dfd9924a10d32"
      - shenz<PERSON>tiangu: "09d83d8687b74df292862c0cd8cc2e98"
#      - ganning: "${ENV(gn_oid_tsign)}"
      - ganning: "8a48c66df52e40b4a5b29320eaa87117"
      - tengqing: "${ENV(account_id1_in_tsign)}"
#      - app_id_authorized: ${ENV(app_id_for_esigntest浙江浙农实业投资有限公司)}
#      - app_id_authorized: "**********"
      - app_id_authorized: "**********"
      # 注：需要有主子企业关联授权，主企业appId设置subOrganizeCheckSwitch为1，主企业appId调用子企业流程

- test:
    name: 一步发起指定证书id用户手动签-成功 - 签署主体-泗县  抄送主体-明绣测试企业  发起主体-深圳天谷
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '附件1', source: 0}
      - attachments: [{fileId: $fileId, attachmentName: "附件2.pdf"}]
      - docs: [$doc]
      - copiers: [{copierAccountId: $ganning,copierIdentityAccountId: $mingxiu, copierIdentityAccountType: 1}]
      - flowInfo: {initiatorAccountId: $ganning ,initiatorAuthorizedAccountId: $shenzhentiangu, autoArchive: true, autoInitiate: true, businessScene: "********附件下载",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $ganning,
            "authorizedAccountId": $sixian
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']


- test:
    name: 流程状态，查询附件列表-appid  BZQ-App-Id
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",1]

- test:
    name: 流程状态，查询附件列表-appid  东营不能下载
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - eq: [content.code,1435002]
      - contains: ["content.message", "无权限下载文件"]

- test:
    name: 流程状态，查询附件列表-appid  泗县可以下载
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
#      - app_id: "7876625185"
      - app_id: "$app_id_authorized"
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",1]

- test:
    name: 流程状态，查询附件列表-appid  明绣测试企业可以下载
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: "7876619359"
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",1]

- test:
    name: 流程状态，查询附件列表-appid 深圳天谷可以下载
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: "7876628730"
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",1]

- test:
    name: 一步发起指定证书id用户手动签-成功 - 签署主体-泗县 发起主体-深圳天谷
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '附件1', source: 0}
      - attachments: [{fileId: $fileId, attachmentName: "附件2.pdf"}]
      - docs: [$doc]
      - copiers: []
      - flowInfo: {initiatorAccountId: $ganning ,initiatorAuthorizedAccountId: $sixian, autoArchive: true, autoInitiate: true, businessScene: "********附件下载",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $ganning,
            "authorizedAccountId": $sixian
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']


- test:
    name: 流程状态，查询附件列表-appid  BZQ-App-Id
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",1]

- test:
    name: 被授权企业可以下载附件
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - app_id: $app_id_authorized
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: 未完结状态，被授权企业不可下载文件
    api: api/signflow/flowDocuments/documentsDownload.yml
    variables:
      - app_id: $app_id_authorized
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437135]
      - contains: ["content.message", 流程非归档状态]

- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - accountId: $dongying
    api: api/seals/seals/select_p_seals.yml
    extract:
      - seal_id_org: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询泗县印章"
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - accountId: $sixian
    api: api/seals/seals/select_p_seals.yml
    extract:
      - seal_id_sx: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询签名域"
    variables:
      - flowId: $sign_flow_one
      - app_id: ${ENV(BZQ-App-Id)}
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId1: content.data.signfields.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - accountId: $ganning
      - flowId: $sign_flow_one
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - authcode: 123456
      - accountId: $ganning
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 执行签名域-成功
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - flowId: $sign_flow_one
      - accountId: $ganning
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield: {"fileId":$fileId1,"signerAccountId":$ganning,"actorIndentityType":"2","authorizedAccountId":$sixian,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$seal_id_sx,"signType":1,"signfieldId":"$signfieldId1"}
      - addSignfields: []
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: 已完结状态，被授权企业可以下载
    api: api/signflow/flowDocuments/documentsDownload.yml
    variables:
      - app_id: $app_id_authorized
      - flowId: $sign_flow_one
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.docs",1]

- test:
    name: "流程文件查看"
    api: api/sign-flow/create-by-file.yml
    variables:
      - app_id: ${ENV(app_id_for_esigntest浙江浙农实业投资有限公司)}
      - json:   {
          "docs": [

            {

              "fileEditPwd": "",

              "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",

              "fileName": "A.pdf",

              "neededPwd": false

            },

            {

              "fileEditPwd": "",

              "fileId": "a35b8739398d4cb8aecfad17fdae0e6c",

              "fileName": "B.pdf",

              "neededPwd": false

            },

            {

              "fileEditPwd": "",

              "fileId": "ef9d0afe79a74aacb791ea00f3d8e665",

              "fileName": "C.pdf",

              "neededPwd": false

            },

            {

              "fileId": "232d475467a84ce7b5c42926440a2779",

              "fileName": "D.pdf",

              "neededPwd": false

            },

            {

              "fileEditPwd": "",

              "fileId": "7abbabb4c7b74d3f8e49e2e5ec4e31bd",

              "fileName": "E.pdf",

              "neededPwd": false

            },

            {

              "fileEditPwd": "",

              "fileId": "ce0f8f9123f840f8bd9cef42fb329cc6",

              "fileName": "F.pdf",

              "neededPwd": false

            },

            {

              "fileId": "1c1ab235407d4759b04c4104a1d52412",

              "fileName": "G.pdf",

              "neededPwd": false

            },

            {

              "fileEditPwd": "",

              "fileId": "ac7e45a98bae42388e2d1f6d5146bdb9",

              "fileName": "H.pdf",

              "neededPwd": false

            },

            {

              "fileEditPwd": "",

              "fileId": "8a322686a8d349a991c0d61b6fffeb98",

              "fileName": "I.pdf",

              "neededPwd": false

            },

            {

              "fileId": "46250eff33d1472fa90dae3cdc035381",

              "fileName": "J.pdf",

              "neededPwd": false

            },

            {

              "fileEditPwd": "",

              "fileId": "c2684711d8374d6688cbb9b243f21248",

              "fileName": "K.pdf",

              "neededPwd": false

            },

            {

              "fileEditPwd": "",

              "fileId": "7cf71423956a447e98dbc6f54f5bdc4a",

              "fileName": "H.pdf",

              "neededPwd": false

            },

            {

              "fileId": "4c94a1856b924493a1b5088eadef3cb8",

              "fileName": "J.pdf",

              "neededPwd": false

            },

            {

              "fileId": "3f35e49b69b641fca72eaeef6f56982b",

              "fileName": "k.pdf",

              "neededPwd": false

            },

            {

              "fileId": "1512c13dbe0e4a3ea5a058509908fa85",

              "fileName": "k.pdf",

              "neededPwd": false

            },

            {

              "fileId": "e549ece7182f4a88833f9f8f12b20cf2",

              "fileName": "L.pdf",

              "neededPwd": false

            }

          ],

          "copiers": [

            {

              "copierPsnInfo": {

                "psnAccount": "***********"

              },

              "copierOrgInfo": {

                "orgName": "泗县深泉纯净水有限公司"

              }

            }

          ],

          "signFlowInitiator": {

            "orgInitiator": {

              "orgId": "705789a4bc6545e7b9cdc4ebadafc177",

              "transactor": {

                "psnId": "ab9e503fb5b24b988da20e2a718a552a"

              }

            }

          },

          "signFlowConfig": {

            "authConfig": {

              "audioVideoTemplateId": "",

              "orgAvailableAuthModes": [ ],

              "orgEditableFields": [ ],

              "psnAvailableAuthModes": [ ],

              "psnEditableFields": [ ],

              "willingnessAuthModes": [ ]

            },

            "autoFinish": true,

            "autoStart": false,

            "chargeConfig": {

              "chargeMode": 1

            },

            "noticeConfig": {

              "noticeTypes": "1"

            },

            "notifyUrl": "http://libaohui.com.cn/callback/ding",

            "redirectConfig": {

              "redirectDelayTime": null,

              "redirectUrl": ""

            },

            "signConfig": {

              "availableSignClientTypes": "",

              "showBatchDropSealButton": true

            },

            "contractConfig": {

              "contractSecrecy": 2

            },

            "signFlowTitle": "文件可见性测试",

            "signFlowExpireTime": "",

            "docsViewLimited": true

          },

          "signers": [

            {

              "noticeConfig": {

                "noticeTypes": ""

              },

              "orgSignerInfo": null,

              "psnSignerInfo": {

                "psnId": "",

                "psnAccount": "***********"

              },

              "signConfig": {

                "signOrder": 1,

                "docsViewType": 2,

                "viewableFileIds": [

                  "1d5d95daa3124dbeb774b82910bcbfb8",

                  "4c94a1856b924493a1b5088eadef3cb8"

                ]

              },

              "signFields": [

                {

                  "customBizNum": "",

                  "fileId": "ef9d0afe79a74aacb791ea00f3d8e665",

                  "normalSignFieldConfig": {

                    "assignedSealId": "",

                    "autoSign": false,

                    "availableSealIds": [ ],

                    "freeMode": false,

                    "movableSignField": false,

                    "orgSealBizTypes": "",

                    "psnSealStyles": "",

                    "signFieldPosition": {

                      "acrossPageMode": "",

                      "positionPage": "1",

                      "positionX": 200,

                      "positionY": 100

                    },

                    "signFieldSize": null,

                    "signFieldStyle": 1

                  },

                  "remarkSignFieldConfig": null,

                  "signDateConfig": {

                    "dateFormat": "",

                    "fontSize": 0,

                    "showSignDate": 1,

                    "signDatePositionPage": 2,

                    "signDatePositionX": 100,

                    "signDatePositionY": 100

                  },

                  "signFieldType": 0

                },

                {

                  "customBizNum": "",

                  "fileId": "232d475467a84ce7b5c42926440a2779",

                  "normalSignFieldConfig": {

                    "assignedSealId": "",

                    "autoSign": false,

                    "availableSealIds": [ ],

                    "freeMode": false,

                    "movableSignField": false,

                    "orgSealBizTypes": "",

                    "psnSealStyles": "",

                    "signFieldPosition": {

                      "acrossPageMode": "",

                      "positionPage": "1",

                      "positionX": 200,

                      "positionY": 100

                    },

                    "signFieldSize": null,

                    "signFieldStyle": 1

                  },

                  "remarkSignFieldConfig": null,

                  "signDateConfig": {

                    "dateFormat": "",

                    "fontSize": 0,

                    "showSignDate": 1,

                    "signDatePositionPage": 2,

                    "signDatePositionX": 100,

                    "signDatePositionY": 100

                  },

                  "signFieldType": 0

                }

              ],

              "signerType": 0

            }

          ]

        }

    extract:
      - flowId1: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, '成功']