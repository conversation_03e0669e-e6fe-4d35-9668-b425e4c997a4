- config:
    name: "国密改造项目接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/关键字文档.pdf
      - group: ''

- test:
    name: "pdf_SM2验签"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - fileId: ${ENV(SM2_fileId)}
      - flowId: ${ENV(SM2_flowId)}
    api: api/iterate_cases/documentsVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfos.0.signature.modify", False]

- test:
    name: "pdf_RSA验签"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - fileId: ${ENV(RSA_fileId)}
      - flowId: ${ENV(RSA_flowId)}
    api: api/iterate_cases/documentsVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfos.0.signature.modify", False]



