<!DOCTYPE html>
<!-- saved from url=(0067)http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********* -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                            <title>SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科</title>
    
        

                        
    
                        
    

                
    
    <meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=IE7">

<meta id="confluence-context-path" name="confluence-context-path" content="">
<meta id="confluence-base-url" name="confluence-base-url" content="http://wiki.timevale.cn:8081">

<meta id="atlassian-token" name="atlassian-token" content="c77c6148172317aaca6271580e8a47f2dc64efc9">


<meta id="confluence-space-key" name="confluence-space-key" content="productreq">
<script type="text/javascript">
        var contextPath = '';
</script>

    

    <meta name="confluence-request-time" content="1753757284171">
        
    
        
            <meta name="ajs-is-space-admin" content="false"> <meta name="ajs-has-space-config" content="false">
            <meta name="ajs-show-space-welcome-dialog" content="true">
            <style>.ia-fixed-sidebar, .ia-splitter-left {width: 285px;}.theme-default .ia-splitter #main {margin-left: 285px;}.ia-fixed-sidebar {visibility: hidden;}</style>
            <meta name="ajs-use-keyboard-shortcuts" content="true">
            <meta name="ajs-discovered-plugin-features" content="{&quot;com.atlassian.confluence.plugins.confluence-page-banner&quot;:[&quot;recently-work-on-contributor-lozenge&quot;],&quot;com.atlassian.confluence.plugins.confluence-dashboard&quot;:[&quot;recently-worked-on-drafts&quot;]}">
            <meta name="ajs-keyboardshortcut-hash" content="5d0be2eb1aace87b32328c0177e65e28">
            <meta name="ajs-is-confluence-admin" content="false">
            <meta name="ajs-connection-timeout" content="10000">
            
    
    
            <meta name="ajs-page-title" content="SaaS及API支持FDA电子签名">
            <meta name="ajs-latest-published-page-title" content="SaaS及API支持FDA电子签名">
            <meta name="ajs-space-name" content="1.产品中心">
            <meta name="ajs-page-id" content="*********">
            <meta name="ajs-latest-page-id" content="*********">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-parent-page-title" content="0724迭代">
            <meta name="ajs-parent-page-id" content="217674377">
            <meta name="ajs-space-key" content="productreq">
            <meta name="ajs-max-number-editors" content="12">
            <meta name="ajs-macro-placeholder-timeout" content="5000">
            <meta name="ajs-jira-metadata-count" content="0">
            <meta name="ajs-from-page-title" content="">
            <meta name="ajs-can-remove-page" content="false">
            <meta name="ajs-can-remove-page-hierarchy" content="false">
            <meta name="ajs-browse-page-tree-mode" content="view">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-user-display-name" content="誊青">
            <meta name="ajs-context-path" content="">
            <meta name="ajs-base-url" content="http://wiki.timevale.cn:8081">
            <meta name="ajs-version-number" content="6.13.4">
            <meta name="ajs-build-number" content="7901">
            <meta name="ajs-remote-user" content="tengqing">
            <meta name="ajs-remote-user-key" content="2c9d8351798a3e55017a12b77bda0047">
            <meta name="ajs-remote-user-has-licensed-access" content="true">
            <meta name="ajs-remote-user-has-browse-users-permission" content="true">
            <meta name="ajs-current-user-fullname" content="誊青">
            <meta name="ajs-current-user-avatar-url" content="/download/attachments/128361700/user-avatar">
            <meta name="ajs-current-user-avatar-uri-reference" content="/download/attachments/128361700/user-avatar">
            <meta name="ajs-static-resource-url-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/_">
            <meta name="ajs-global-settings-attachment-max-size" content="209715200">
            <meta name="ajs-user-locale" content="zh_CN">
            <meta name="ajs-enabled-dark-features" content="site-wide.shared-drafts,site-wide.synchrony,confluence.view.edit.transition,confluence-inline-comments-resolved,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareContentEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.mentions.api.ConfluenceMentionEvent,frontend.editor.v4.compatibility,notification.plugin.api.enabled.com.atlassian.confluence.event.events.security.ForgotPasswordEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.tasklist.event.SendTaskEmailEvent,file-annotations,confluence.efi.onboarding.rich.space.content,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.comment.CommentCreateEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.requestaccess.events.AccessGrantedEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageMoveEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.follow.FollowEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentReplyEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostCreateEvent,lucene.caching.filter,confluence.table.resizable,notification.batch,confluence-inline-comments-rich-editor,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostUpdateEvent,site-wide.synchrony.opt-in,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageCreatedEvent,mobile.supported.version,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentMentionUpdateEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.hipchat.api.events.HipChatUserMapped,quick-reload-inline-comments-flags,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.blogpost.BlogPostMovedEvent,clc.quick.create,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageUpdateEvent,cql.search.screen,nps.survey.inline.dialog,confluence.efi.onboarding.new.templates,pdf-preview,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageMovedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareCustomEvent,previews.sharing,previews.versions,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentUpdateEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.attachment.AttachmentBatchUploadCompletedEvent,collaborative-audit-log,notification.plugin.api.enabled.com.atlassian.confluence.efi.emails.events.OnboardingLessUsersEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.files.notifications.event.FileContentRemoveEvent,confluence.wrap.macro,previews.conversion-service,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.comment.CommentUpdateEvent,editor.ajax.save,graphql,read.only.mode,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.async.PageEditedEvent,previews.trigger-all-file-types,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentResolveEvent,notification.plugin.api.enabled.com.atlassian.confluence.event.events.like.LikeCreatedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.inlinecomments.events.InlineCommentCreateEvent,attachment.extracted.text.extractor,notification.plugin.api.enabled.com.atlassian.confluence.plugins.requestaccess.events.AccessRequestedEvent,previews.sharing.pushstate,file-annotations.likes,v2.content.name.searcher,notification.plugin.api.enabled.com.atlassian.confluence.event.events.content.page.PageCreateEvent,notification.plugin.api.enabled.com.atlassian.confluence.efi.emails.events.OnboardingNoSpaceCreatedEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareDraftEvent,notification.plugin.api.enabled.com.atlassian.confluence.plugins.sharepage.api.ShareAttachmentEvent,confluence-inline-comments,confluence-inline-comments-dangling-comment">
            <meta name="ajs-atl-token" content="c77c6148172317aaca6271580e8a47f2dc64efc9">
            <meta name="ajs-confluence-flavour" content="VANILLA">
            <meta name="ajs-user-date-pattern" content="yyyy-M-d">
            <meta name="ajs-access-mode" content="READ_WRITE">
            <meta name="ajs-render-mode" content="READ_WRITE">
            <meta name="ajs-date.format" content="MMM dd, yyyy">
    
    <link rel="shortcut icon" href="http://wiki.timevale.cn:8081/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/18/_/favicon.ico">
    <link rel="icon" type="image/x-icon" href="http://wiki.timevale.cn:8081/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/18/_/favicon.ico">

<link rel="search" type="application/opensearchdescription+xml" href="http://wiki.timevale.cn:8081/opensearch/osd.action" title="天谷百科">
    
                    
            <meta name="ajs-create-issue-metadata-show-discovery" content="true">
            

    <script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path"]="\u0022\u0022";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-hipchat-integration-plugin:discovery-javascript-data.link-active"]="{\u0022linkActive\u0022:false,\u0022conditionsMet\u0022:true,\u0022admin\u0022:false}";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-feature-discovery-plugin:confluence-feature-discovery-plugin-resources.test-mode"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider"]="false";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.applinks-help-paths"]="{\u0022entries\u0022:{\u0022applinks.docs.root\u0022:\u0022https://confluence.atlassian.com/display/APPLINKS-054/\u0022,\u0022applinks.docs.diagnostics.troubleshoot.sslunmatched\u0022:\u0022SSL+and+application+link+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthsignatureinvalid\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthtimestamprefused\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.delete.entity.link\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.adding.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.administration.guide\u0022:\u0022Application+Links+Documentation\u0022,\u0022applinks.docs.oauth.security\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.troubleshoot.application.links\u0022:\u0022Troubleshoot+application+links\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unknownerror\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.trusted.apps\u0022:\u0022Configuring+Trusted+Applications+authentication+for+an+application+link\u0022,\u0022applinks.docs.diagnostics.troubleshoot.authlevelunsupported\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.ssluntrusted\u0022:\u0022SSL+and+application+link+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unknownhost\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.delete.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.link.applications\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.diagnostics.troubleshoot.oauthproblem\u0022:\u0022OAuth+troubleshooting+guide\u0022,\u0022applinks.docs.diagnostics.troubleshoot.migration\u0022:\u0022Update+application+links+to+use+OAuth\u0022,\u0022applinks.docs.relocate.application.link\u0022:\u0022Link+Atlassian+applications+to+work+together\u0022,\u0022applinks.docs.administering.entity.links\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.upgrade.application.link\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.diagnostics.troubleshoot.connectionrefused\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.oauth\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.insufficient.remote.permission\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.configuring.application.link.auth\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.diagnostics\u0022:\u0022Application+links+diagnostics\u0022,\u0022applinks.docs.configured.authentication.types\u0022:\u0022OAuth+security+for+application+links\u0022,\u0022applinks.docs.adding.entity.link\u0022:\u0022Create+links+between+projects\u0022,\u0022applinks.docs.diagnostics.troubleshoot.unexpectedresponse\u0022:\u0022Network+and+connectivity+troubleshooting+guide\u0022,\u0022applinks.docs.configuring.auth.basic\u0022:\u0022Configuring+Basic+HTTP+Authentication+for+an+Application+Link\u0022,\u0022applinks.docs.diagnostics.troubleshoot.authlevelmismatch\u0022:\u0022OAuth+troubleshooting+guide\u0022}}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.applinks-types"]="{\u0022crowd\u0022:\u0022\u4eba\u7fa4\u0022,\u0022confluence\u0022:\u0022Confluence\u0022,\u0022fecru\u0022:\u0022Fisheye / Crucible\u0022,\u0022stash\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u0022,\u0022jira\u0022:\u0022Jira\u0022,\u0022refapp\u0022:\u0022\u76f8\u5173\u5e94\u7528\u7a0b\u5e8f\u0022,\u0022bamboo\u0022:\u0022\u7af9\u0022,\u0022generic\u0022:\u0022\u901a\u7528\u5e94\u7528\u7a0b\u5e8f\u0022}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.entity-types"]="{\u0022singular\u0022:{\u0022refapp.charlie\u0022:\u0022Charlie\u0022,\u0022fecru.project\u0022:\u0022Crucible\u9879\u76ee\u0022,\u0022fecru.repository\u0022:\u0022FishEye \u4ed3\u5e93\u0022,\u0022stash.project\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u9879\u76ee\u0022,\u0022generic.entity\u0022:\u0022\u901a\u7528\u9879\u76ee\u0022,\u0022confluence.space\u0022:\u0022Confluence\u7a7a\u95f4\u0022,\u0022bamboo.project\u0022:\u0022Bamboo\u9879\u76ee\u0022,\u0022jira.project\u0022:\u0022Jira \u9879\u76ee\u0022},\u0022plural\u0022:{\u0022refapp.charlie\u0022:\u0022\u67e5\u7406\u65af\u0022,\u0022fecru.project\u0022:\u0022Crucible\u9879\u76ee\u0022,\u0022fecru.repository\u0022:\u0022FishEye \u4ed3\u5e93\u96c6\u0022,\u0022stash.project\u0022:\u0022Bitbucket\u670d\u52a1\u5668\u9879\u76ee\u0022,\u0022generic.entity\u0022:\u0022\u901a\u7528\u9879\u76ee\u0022,\u0022confluence.space\u0022:\u0022Confluence\u7a7a\u95f4\u0022,\u0022bamboo.project\u0022:\u0022Bamboo\u9879\u76ee\u0022,\u0022jira.project\u0022:\u0022Jira \u9879\u76ee\u0022}}";
WRM._unparsedData["com.atlassian.applinks.applinks-plugin:applinks-common-exported.authentication-types"]="{\u0022com.atlassian.applinks.api.auth.types.BasicAuthenticationProvider\u0022:\u0022\u57fa\u672c\u8bbf\u95ee\u0022,\u0022com.atlassian.applinks.api.auth.types.TrustedAppsAuthenticationProvider\u0022:\u0022\u4fe1\u4efb\u7684\u5e94\u7528\u65e0\u6548\u0022,\u0022com.atlassian.applinks.api.auth.types.CorsAuthenticationProvider\u0022:\u0022\u6b4c\u73e5\u0022,\u0022com.atlassian.applinks.api.auth.types.OAuthAuthenticationProvider\u0022:\u0022Oauth\u0022,\u0022com.atlassian.applinks.api.auth.types.TwoLeggedOAuthAuthenticationProvider\u0022:\u0022Oauth\u0022,\u0022com.atlassian.applinks.api.auth.types.TwoLeggedOAuthWithImpersonationAuthenticationProvider\u0022:\u0022Oauth\u0022}";
WRM._unparsedData["com.atlassian.confluence.plugins.synchrony-interop:************************loader.synchrony-status"]="false";
WRM._unparsedData["com.atlassian.confluence.plugins.confluence-license-banner:confluence-license-banner-resources.license-details"]="{\u0022daysBeforeLicenseExpiry\u0022:0,\u0022daysBeforeMaintenanceExpiry\u0022:0,\u0022showLicenseExpiryBanner\u0022:false,\u0022showMaintenanceExpiryBanner\u0022:false,\u0022renewUrl\u0022:null,\u0022salesEmail\u0022:null}";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<link type="text/css" rel="stylesheet" href="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch.css" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<!--[if lt IE 9]>
<link type="text/css" rel="stylesheet" href="/s/5c9655652dfcca5b3167610679db4200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/ec85741cad785658e5d334cee8aab0ba/_/download/contextbatch/css/_super/batch.css?conditionalComment=lt+IE+9" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<!--[if lte IE 9]>
<link type="text/css" rel="stylesheet" href="/s/5c9655652dfcca5b3167610679db4200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/ec85741cad785658e5d334cee8aab0ba/_/download/contextbatch/css/_super/batch.css?conditionalComment=lte+IE+9" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<link type="text/css" rel="stylesheet" href="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(1).css" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context" media="all">
<!--[if lt IE 9]>
<link type="text/css" rel="stylesheet" href="/s/d29c9770d1d3dafb92b965ac248fc207-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/bcb04377762ea4527933daff33201ece/_/download/contextbatch/css/atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super/batch.css?conditionalComment=lt+IE+9&amp;confluence.table.resizable=true&amp;highlightactions=true&amp;hostenabled=true" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context" media="all">
<![endif]-->
<script type="text/javascript" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch.js.下载" data-wrm-key="_super" data-wrm-batch-type="context"></script>
<script type="text/javascript" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(1).js.下载" data-wrm-key="atl.confluence.plugins.pagetree-desktop,main,viewcontent,page,atl.general,atl.comments,-_super" data-wrm-batch-type="context"></script><style type="text/css">/* Applied to body so that the parent document does not scroll while the full screen IFrame dialog is open. */
.spark-no-scroll {
  overflow: hidden; }

.YvpxiV5x7vy1i-QG5gfAe {
  /*
     * Styles for iframe (full screen) dialog emulating Atlassian Connect
     */
  /*
     * Styling to imitate dialog chrome styling from Atlassian Connect (at least
     * one specific version).
     * If an add-on would like to override these styles, it could be done by
     * prefixing with the predictable 'app-id' that will be added as an id
     * to the main dialog wrapper div.
     */ }
  .YvpxiV5x7vy1i-QG5gfAe.spark-app-iframe {
    border: none; }
  .YvpxiV5x7vy1i-QG5gfAe.spark-iframe {
    width: 100%;
    min-height: 100%;
    border: none;
    padding: 0;
    margin: 0;
    overflow: hidden; }
  .YvpxiV5x7vy1i-QG5gfAe.spark-fullscreen-wrapper {
    position: fixed;
    z-index: 9000;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    margin: 0;
    padding: 0;
    overflow: hidden;
    /*
         * Change background on fullscreen dialogs to grey.
         * There are two types of fullscreen dialogs in Atlassian Connect:
         * Dialogs with width/height set to 100% have a half-transparent black background,
         * dialogs with size set to "fullscreen" have a grey background.
         */ }
    .YvpxiV5x7vy1i-QG5gfAe.spark-fullscreen-wrapper.spark-fullscreen-dialog {
      background-color: #f5f5f5; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-scroll-wrapper {
    position: static;
    width: 100%;
    height: 100%;
    overflow: auto;
    margin: 0;
    padding: 0;
    line-height: 0;
    font-size: 0; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-iframe {
    width: 100%;
    min-height: 100%;
    border: none;
    padding: 0;
    margin: 0;
    overflow: hidden; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-scroll-wrapper.spark-fullscreen-haschrome {
    height: calc(100% - 51px); }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome {
    width: 100%;
    background: black;
    height: 50px;
    margin: 0;
    padding: 0;
    border: none;
    border-bottom: 1px solid #cccccc; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap {
    float: right;
    margin: 0;
    padding: 0;
    height: 50px; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap .aui-icon-small::before {
    margin-left: 16px; }
  .YvpxiV5x7vy1i-QG5gfAe .spark-fullscreen-chrome-btnwrap .aui-button {
    height: 50px;
    width: 50px;
    border: none;
    border-left: 1px solid #333;
    border-radius: 0;
    float: left;
    margin-left: 0;
    background: black;
    color: white; }
</style>

    

        
    

        
        <meta name="ajs-site-title" content="天谷百科">
            
    

    
                <link rel="canonical" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
        <link rel="shortlink" href="http://wiki.timevale.cn:8081/x/5mr5D">
    <meta name="wikilink" content="[productreq:SaaS及API支持FDA电子签名]">
    <meta name="page-version" content="34">
    <meta name="ajs-page-version" content="34">

<script type="text/javascript" charset="utf-8" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(2).js.下载"></script><link rel="stylesheet" type="text/css" href="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(2).css"><script type="text/javascript" charset="utf-8" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(3).js.下载"></script><link rel="stylesheet" type="text/css" href="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(3).css"><script type="text/javascript" charset="utf-8" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(4).js.下载"></script>
            <meta name="ajs-use-watch" content="true">
            <meta name="ajs-attachment-source-content-id" content="*********">
            <meta name="ajs-use-inline-tasks" content="true">
            <meta name="ajs-heartbeat" content="true">
            <meta name="ajs-action-locale" content="zh_CN">
            <meta name="ajs-editor-plugin-resource-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_">
            <meta name="ajs-edit-mode" content="collaborative">
            <meta name="ajs-user-watching-own-content" content="true">
            <meta name="ajs-new-page" content="false">
            <meta name="ajs-editor-mode" content="richtext">
            <meta name="ajs-auto-start" content="false">
            <meta name="ajs-conf-revision" content="confluence$content$*********.214">
            <meta name="ajs-sync-revision-source" content="synchrony">
            <meta name="ajs-draft-id" content="217672604">
            <meta name="ajs-draft-share-id" content="a3f94197-67c5-419c-9245-a2ca4fb8f55b">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-collaborative-editor-status" content="">
            <meta name="ajs-existing-draft-id" content="0">
            <meta name="ajs-content-id" content="*********">
            <meta name="ajs-form-name" content="inlinecommentform">
            <meta name="ajs-can-attach-files" content="true">
            <meta name="ajs-show-draft-message" content="false">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-collaborative-content" content="true">
            <meta name="ajs-min-editor-height" content="150">
            <meta name="ajs-version-comment" content="">
            <meta name="ajs-draft-type" content="page">
                
                <meta name="ajs-synchrony-token" content="eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJodHRwOlwvXC93aWtpLnRpbWV2YWxlLmNuOjgwODFcL3N5bmNocm9ueS1wcm94eVwvdjEiLCJzdWIiOiIyYzlkODM1MTc5OGEzZTU1MDE3YTEyYjc3YmRhMDA0NyIsImFjY2VzcyI6eyJcL2RhdGFcL1N5bmNocm9ueS1hMWM2MDA2Mi1iYmYxLTMxZDItODBmYS1kNTVhZjhiNGU1MGZcL2NvbmZsdWVuY2UtMjE3NjcyNDIyIjoiZnVsbCIsIlwvZGF0YVwvU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZlwvY29uZmx1ZW5jZS0yMTc2NzI0MjItdGl0bGUiOiJmdWxsIn0sInJldmlzaW9uTWV0YSI6eyJ1c2VyS2V5IjoiMmM5ZDgzNTE3OThhM2U1NTAxN2ExMmI3N2JkYTAwNDcifSwic2Vzc2lvbiI6eyJhdmF0YXJVUkwiOiJcL2Rvd25sb2FkXC9hdHRhY2htZW50c1wvMTI4MzYxNzAwXC91c2VyLWF2YXRhciIsIm5hbWUiOiJ0ZW5ncWluZyIsImZ1bGxuYW1lIjoi6KqK6Z2SIn0sImlzcyI6IlN5bmNocm9ueS1hMWM2MDA2Mi1iYmYxLTMxZDItODBmYS1kNTVhZjhiNGU1MGYiLCJleHAiOjE3NTM4NDM2ODUsImlhdCI6MTc1Mzc1NzI4NX0.USbLN3y2j4vFHuaI_XkH5S93qPbsTJblPk3H7iToNHs">
    <meta name="ajs-synchrony-base-url" content="http://wiki.timevale.cn:8081/synchrony-proxy,http://wiki.timevale.cn:8081/synchrony-proxy">
    <meta name="ajs-synchrony-app-id" content="Synchrony-a1c60062-bbf1-31d2-80fa-d55af8b4e50f">
    <meta name="ajs-synchrony-expiry" content="**********">
    <meta name="ajs-use-xhr-fallback" content="true">

            		    <meta name="ajs-max-thumb-width" content="300">
		    <meta name="ajs-max-thumb-height" content="300">
		    <meta name="ajs-can-send-email" content="false">
		    <meta name="ajs-is-dev-mode" content="false">
		    <meta name="ajs-draft-save-interval" content="30000">
		    <meta name="ajs-show-hidden-user-macros" content="false">
		    <meta name="ajs-can-view-profile" content="true">
		    <meta name="ajs-is-admin" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autocomplete" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autoformat" content="false">
		    <meta name="ajs-heartbeat-interval" content="30000">
	
    <link rel="stylesheet" type="text/css" href="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(4).css"><script type="text/javascript" charset="utf-8" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(5).js.下载"></script><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><style type="text/css" media="print">.tableFloatingHeader{display:none !important;}.tableFloatingHeaderOriginal{position:static !important;}</style><link rel="stylesheet" type="text/css" href="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/com.atlassian.confluence.plugins.confluence-previews_confluence-previews-css.css"><link rel="stylesheet" type="text/css" href="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(5).css"><script type="text/javascript" charset="utf-8" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/batch(6).js.下载"></script><style type="text/css">.tooltip {
  text-indent: initial;
  width: auto;
  display: inline-block;
  text-transform: none;
  font-family: Arial, sans-serif;
  font-size: 12px;
  line-height: 20px;
  border-radius: 3px;
  background: rgba(51, 51, 51, 0.9);
  color: #fff;
  left: 50%;
  white-space: nowrap;
  margin-bottom: 15px;
  opacity: 0;
  padding: 5px 10px;
  pointer-events: none;
  position: absolute;
  z-index: 999;
  max-width: 200px;
  -webkit-transition: all 0s 0.3s ease-out;
  -moz-transition: all 0s 0.3s ease-out;
  -ms-transition: all 0s 0.3s ease-out;
  -o-transition: all 0s 0.3s ease-out;
  transition: all 0s 0.3s ease-out;
  /* This bridges the gap so you can mouse into the tooltip without it disappearing */
  /* CSS Triangles */
}
.tooltip:before {
  bottom: -20px;
  content: " ";
  display: block;
  height: 20px;
  left: 0;
  position: absolute;
  width: 100%;
}
.tooltip:after {
  border-left: solid transparent 4px;
  border-right: solid transparent 4px;
  content: " ";
  height: 0;
  left: 50%;
  margin-left: -4px;
  position: absolute;
  width: 0;
}
.tooltip.tooltip-s {
  bottom: 100%;
  -webkit-transform: translateY(10px) translateX(-50%);
  -moz-transform: translateY(10px) translateX(-50%);
  -ms-transform: translateY(10px) translateX(-50%);
  -o-transform: translateY(10px) translateX(-50%);
  transform: translateY(10px) translateX(-50%);
}
.tooltip.tooltip-s:after {
  border-top: solid rgba(51, 51, 51, 0.9) 4px;
  bottom: -4px;
}
.tooltip.tooltip-n {
  top: 100%;
  -webkit-transform: translateY(0) translateX(-50%);
  -moz-transform: translateY(0) translateX(-50%);
  -ms-transform: translateY(0) translateX(-50%);
  -o-transform: translateY(0) translateX(-50%);
  transform: translateY(0) translateX(-50%);
}
.tooltip.tooltip-n:after {
  border-bottom: solid rgba(51, 51, 51, 0.9) 4px;
  top: -4px;
}
*:hover > .tooltip {
  opacity: 1;
  pointer-events: auto;
}
*:hover > .tooltip.tooltip-s {
  -webkit-transform: translateY(0px) translateX(-50%);
  -moz-transform: translateY(0px) translateX(-50%);
  -ms-transform: translateY(0px) translateX(-50%);
  -o-transform: translateY(0px) translateX(-50%);
  transform: translateY(0px) translateX(-50%);
}
*:hover > .tooltip.tooltip-n {
  -webkit-transform: translateY(10px) translateX(-50%);
  -moz-transform: translateY(10px) translateX(-50%);
  -ms-transform: translateY(10px) translateX(-50%);
  -o-transform: translateY(10px) translateX(-50%);
  transform: translateY(10px) translateX(-50%);
}
/*
    Header
 */
.cp-header {
  position: relative;
  z-index: 99;
  background-color: #000000;
  padding-left: 10px;
}
.cp-header.cp-header-group {
  display: table;
  box-sizing: border-box;
  border-spacing: 0;
  table-layout: fixed;
  width: 100%;
}
.cp-header.cp-header-group .cp-header-item {
  display: table-cell;
  vertical-align: top;
}
.cp-header.cp-header-group > .cp-header-item {
  margin: 0;
  box-sizing: border-box;
}
.cp-title-container {
  max-width: 70%;
  display: block;
  float: left;
  height: 50px;
}
.cp-title-container div {
  color: #fff;
  line-height: 50px;
  white-space: nowrap;
  overflow: hidden;
}
.cp-title-container .cp-file-icon {
  width: 45px;
  height: 50px;
  display: inline-block;
  float: left;
  background: no-repeat 10px center;
}
.cp-file-controls {
  padding-left: 0;
  text-align: right;
  float: right;
  max-width: 30%;
  display: block;
  height: 50px;
}
.cp-file-controls > span {
  display: inline-block;
}
/*
    Body
 */
.cp-body {
  height: 100%;
  margin: 0;
}
.cp-error-message {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  min-width: 490px;
  max-width: 640px;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}
.cp-error-message .file-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background: no-repeat center center;
  background-size: contain;
}
.cp-error-message p {
  margin: 10px 0;
  color: #fff;
  line-height: 1.4em;
}
.cp-error-message p.message {
  margin: 10px 0 0 0;
  word-wrap: break-word;
}
.cp-preview-password {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-preview-password .cp-password-lock-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background: url("data:image/svg+xml;base64,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") no-repeat center center;
  background-size: contain;
}
.cp-preview-password p {
  margin: 0;
  color: #fff;
}
.cp-preview-password p.message {
  line-height: 1.4em;
  margin-top: 10px;
}
.cp-preview-password .cp-password-fullscreen {
  display: none;
}
.cp-preview-password .cp-password-base .cp-password-input {
  font-size: 14px;
  text-align: left;
  height: 28px;
  border: 1px solid #ccc;
  border-radius: 2px;
  outline: none;
  padding: 0 6px;
  margin: 10px 10px 0 0;
}
.cp-preview-password .cp-password-base .cp-password-button:focus {
  outline: none !important;
}
.cp-file-body .cp-baseline-extension {
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
.cp-file-body.presentation {
  background: #000;
  top: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  left: 0 !important;
  height: 100% !important;
  width: 100% !important;
  z-index: 100;
}
.cp-container {
  font-family: Arial, sans-serif;
  font-size: 16px;
  background-color: rgba(51, 51, 51, 0.95);
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  /* CONFDEV-26487: z-index is greater than an AUI inline dialog, whose default is 100. */
  z-index: 101;
}
.cp-container .hidden {
  display: none;
}
.cp-container *[role='button'] {
  position: relative;
  -webkit-transform: translateZ(0);
  /* webkit flicker fix */
  -webkit-font-smoothing: antialiased;
  /* webkit text rendering fix */
}
.cp-container .cp-icon {
  padding: 0;
  margin: 0;
  background-color: transparent;
  background-position: center !important;
  background-repeat: no-repeat !important;
  height: 50px;
  float: left;
  text-align: left;
  text-indent: -999em;
  width: 50px;
  border-radius: 0;
}
.cp-container a.cp-icon {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.cp-container a.cp-icon:hover {
  background-color: #707070;
}
.cp-container a.cp-icon:focus {
  outline: none;
  background-color: #707070;
}
.cp-container button.cp-icon {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: none;
  outline: none;
  cursor: pointer;
}
.cp-container button.cp-icon:hover {
  background-color: #707070;
}
.cp-container button.cp-icon:focus {
  outline: none;
  background-color: #707070;
}
*[id^='cp-container-'][data-embedded='true'],
*[id^='cp-container-'][data-contained='true'] {
  position: relative;
  background: none;
  z-index: auto;
}
*[id^='cp-container-'][data-embedded='true'] .cp-body .cp-file-body,
*[id^='cp-container-'][data-contained='true'] .cp-body .cp-file-body {
  position: relative;
  overflow: auto;
  top: 0;
}
*[id^='cp-container-'][data-embedded='true'] .cp-footer,
*[id^='cp-container-'][data-contained='true'] .cp-footer {
  display: none;
}
*[id^='cp-container-'][data-embedded='true'] .cp-file-controls .fv-close-button,
*[id^='cp-container-'][data-contained='true'] .cp-file-controls .fv-close-button {
  display: none;
}
*[id^='cp-container-'][data-contained='true'] .cp-header {
  display: none;
}
*[id^='cp-container-'][data-contained='true'] .cp-body .cp-file-body {
  height: 100%;
}
.cp-small-icon {
  background: no-repeat left center;
  height: 20px;
  float: left;
  text-align: left;
  text-indent: -999em;
  width: 25px;
}
.cp-file-body {
  bottom: 0;
  box-sizing: border-box;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  text-align: center;
  top: 50px;
  vertical-align: middle;
  height: calc(100% - 50px);
}
.cp-viewer-layer {
  height: 100%;
}
.cp-file-body.narrow {
  right: 350px;
}
.cp-file-body.short {
  bottom: 160px;
  /* to account for the thumbnail pane */
  height: calc(100% - 210px);
}
.no-scroll {
  overflow: hidden !important;
}
.cp-nav {
  cursor: pointer;
  outline: none;
  border: none;
  background: transparent no-repeat center center;
  color: #fff;
  position: absolute;
  top: 50%;
  margin-top: -50px;
  width: 65px;
  height: 100px;
  text-indent: -999em;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  transition: background-color 0.2s ease-in-out;
  -moz-transition: background-color 0.2s ease-in-out;
  -webkit-transition: background-color 0.2s ease-in-out;
}
.cp-nav:after {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -0.25em;
}
.cp-nav:hover,
.cp-nav:focus {
  background-color: rgba(0, 0, 0, 0.8);
  transition: background-color 0.1s ease-in-out;
  -moz-transition: background-color 0.1s ease-in-out;
  -webkit-transition: background-color 0.1s ease-in-out;
}
.cp-nav.disabled {
  opacity: 0.5;
  cursor: default;
}
.cp-nav.disabled:hover {
  background-color: transparent;
}
.cp-nav-left {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  left: 0;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvcHJldmlvdXMtbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xMCAyNC41MTR2LTEuMDI4TDI1LjMyMyA2Ljc0Yy4zNzQtLjQxLjk4NC0uNDI1IDEuMzczLS4wMjRsMS4xMDggMS4xNGMuMzg0LjM5NS4zOTMgMS4wMzguMDE2IDEuNDRMMTQgMjRsMTMuODIgMTQuNzA1Yy4zNzYuNC4zNzMgMS4wNC0uMDE2IDEuNDRsLTEuMTA4IDEuMTRjLS4zODQuMzk0LTEgLjM4LTEuMzczLS4wMjVMMTAgMjQuNTE0eiIgZmlsbD0iIzk5OSIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+");
}
.cp-nav-right {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  right: 0;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvbmV4dC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PHBhdGggZD0iTTM4IDI0LjUxNHYtMS4wMjhMMjIuNjc3IDYuNzRjLS4zNzQtLjQxLS45ODQtLjQyNS0xLjM3My0uMDI0bC0xLjEwOCAxLjE0Yy0uMzg0LjM5NS0uMzkzIDEuMDM4LS4wMTYgMS40NEwzNCAyNCAyMC4xOCAzOC43MDVjLS4zNzYuNC0uMzczIDEuMDQuMDE2IDEuNDRsMS4xMDggMS4xNGMuMzg0LjM5NCAxIC4zOCAxLjM3My0uMDI1TDM4IDI0LjUxNHoiIGZpbGw9IiM5OTkiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==");
}
.cp-error-layer,
.cp-waiting-layer,
.cp-password-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
#inline-dialog-sharePreviewPopup {
  z-index: 120;
}
.cp-share-dialog-spinner {
  position: relative;
  top: 12px;
  left: 8px;
}
.cp-waiting-message {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-waiting-message .file-icon {
  display: inline-block;
  width: 80px;
  height: 80px;
  background: no-repeat center center;
  background-size: contain;
}
.cp-waiting-message p {
  margin: 0;
}
/*
    Sidebar
 */
.cp-sidebar {
  background-color: #fff;
  height: calc(100% - 40px);
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  right: 0;
  /* CONFDEV-30274: Delegate rendering of this element to the GPU to avoid painting issues in latest Chrome/Safari */
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}
.cp-sidebar.meta-infobar {
  height: calc(100% - 100px);
}
.cp-sidebar.meta-minimode-toggle {
  height: calc(100% - 90px);
}
.cp-sidebar.meta-minimode-toggle.short {
  height: calc(100% - 250px);
}
a.cp-button {
  box-sizing: border-box;
  background: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 3.01px;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: inherit;
  font-size: 14px;
  font-variant: normal;
  font-weight: 400;
  height: 2.14285714em;
  line-height: 1.42857143;
  margin: 0;
  padding: 4px 10px;
  vertical-align: baseline;
  white-space: nowrap;
  text-decoration: none;
  margin: 20px 10px 0 10px;
}
a.cp-button:hover {
  background: #e9e9e9;
  border-color: #999;
}
a.cp-button span.cp-button-icon {
  background-position: 0 0;
  border: none;
  margin: 0;
  padding: 0;
  text-indent: -999em;
  vertical-align: text-bottom;
  display: inline-block;
  text-align: left;
  line-height: 0;
  position: relative;
  vertical-align: text-top;
  height: 16px;
  width: 16px;
}
a.cp-button span.cp-button-icon.icon-download {
  position: relative;
  top: 3px;
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+VW50aXRsZWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xIDEydi0xaDEwdjFIMXptNS4yLTNoLS4zOUwxIDQuMTZsMS4xMi0xLjEzTDUgNS45MlYxLjVDNS4wOS41IDUuNDUgMCA2IDBzLjg5LjM3IDEgMS41djQuNDFsMi45MS0yLjg1TDExIDQuMTYgNi4yIDl6IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=") no-repeat 0 0;
}
/*
    Footer
 */
.cp-footer {
  background-color: #000000;
  position: absolute;
  width: 100%;
  bottom: 0;
}
.cp-footer a {
  color: #fff;
  text-decoration: none;
}
.cp-file-body.meta-infobar {
  bottom: 50px;
  /* when no footer is present */
  height: calc(100% - 100px);
}
.cp-file-body.meta-minimode-toggle {
  bottom: 40px;
  /* when no footer is present */
  height: calc(100% - 90px);
}
.cp-file-body.meta-minimode-toggle.short {
  bottom: 200px;
  height: calc(100% - 250px);
}
.cp-sidebar.meta-minimode-toggle {
  height: calc(100% - 90px);
}
.cp-sidebar.meta-minimode-toggle.short {
  height: calc(100% - 250px);
}
</style><style type="text/css">.cp-image-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDFfZmlsZS1pbWctbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTggMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAzIDI0IDI0IDIzLjEwMyAyNCAyMS45OTZWMi4wMDVDMjQgLjg5OCAyMy4xMDQgMCAyMS45OTUgMHpNMjMgMjEuOTk1QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTZWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5QzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OXpNMTcgMTNsLTMuNzMgMy43M0w4IDlsLTUgNSAuNSAxczMuMDA1LTIuNzggNC41LTQuNWMuNDUzLjU1MyA3IDEwLjUgNyAxMC41aDFsLTIuMTItMy4zNzggMy42Mi0zLjYyIDMgMyAuNS0xLTMtM2gtMXptLTQuMzU3LTQuMDdsLS4yIDIuOCAyLjAyNC0xLjkwNSAyLjAyNCAxLjkwNS0uMi0yLjggMi43MjYtLjQyMy0yLjI3NC0xLjU4NSAxLjM3My0yLjQzNC0yLjYzNi44MjMtMS4wMTMtMi42MS0xLjAxMyAyLjYxLTIuNjM2LS44MjIgMS4zNzQgMi40MzQtMi4yNzUgMS41ODUgMi43MjYuNDI0em0xLjgyNC0zLjM4N2MxLjAzIDAgMS44NjcuODUzIDEuODY3IDEuOTA3IDAgMS4wNTUtLjgzOCAxLjkwOC0xLjg2NyAxLjkwOC0xLjAzIDAtMS44NjctLjg1My0xLjg2Ny0xLjkwOCAwLTEuMDU0LjgzOC0xLjkwNyAxLjg2Ny0xLjkwN3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-image-icon.size-48,
.cp-image-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDBfZmlsZS1pbWFnZS14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00My45OSAxQzQ1LjY1MyAxIDQ3IDIuMzQ3IDQ3IDQuMDF2MzkuOThjMCAxLjY2My0xLjM0NyAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjUzIDEgNDMuOTlWNC4wMUMxIDIuMzQ3IDIuMzQ3IDEgNC4wMSAxaDM5Ljk4ek0yOS45IDE5LjU2bC0xLjY4IDMgLjQtMy40MTVjLS42NzQtLjMxMy0xLjI3Ny0uNzU0LTEuNzc2LTEuMjkzTDIzLjcyIDE5LjI5bDIuMzMyLTIuNTI2Yy0uMzU0LS42MzMtLjU5LTEuMzQtLjY4LTIuMDlMMjIgMTRsMy4zNzMtLjY3M2MuMDktLjc1Mi4zMjUtMS40NTguNjgtMi4wOUwyMy43MTggOC43MWwzLjEyNCAxLjQzOGMuNS0uNTQgMS4xMDItLjk4IDEuNzc3LTEuMjkzbC0uNC0zLjQxNSAxLjY4IDNjLjM1Ni0uMDcuNzI0LS4xMDcgMS4xLS4xMDcuMzc2IDAgLjc0NC4wMzcgMS4xLjEwN2wxLjY4LTMtLjQgMy40MTVjLjY3NC4zMTMgMS4yNzcuNzU0IDEuNzc2IDEuMjkzTDM4LjI4IDguNzFsLTIuMzMyIDIuNTI2Yy4zNTQuNjMzLjU5IDEuMzQuNjggMi4wOUw0MCAxNGwtMy4zNzMuNjczYy0uMDkuNzUyLS4zMjUgMS40NTgtLjY4IDIuMDlsMi4zMzQgMi41MjctMy4xMjQtMS40MzhjLS41LjU0LTEuMTAyLjk4LTEuNzc3IDEuMjkzbC40IDMuNDE1LTEuNjgtM2MtLjM1Ni4wNy0uNzI0LjEwNy0xLjEuMTA3LS4zNzYgMC0uNzQ0LS4wMzctMS4xLS4xMDd6TTI2LjMzNCAxNGMwLTIuNTc3IDIuMDktNC42NjcgNC42NjctNC42NjdzNC42NjcgMi4wOSA0LjY2NyA0LjY2Ny0yLjA5IDQuNjY3LTQuNjY3IDQuNjY3LTQuNjY3LTIuMDktNC42NjctNC42Njd6bTEuNDg0IDIwLjY4M0wzMyA0MmwtLjUgMUwxNiAxOS41IDUuNSAzMiA1IDMxbDExLTEzIDExLjE5NSAxNS44MDVMMzUgMjZsOCAxMC0uNSAxLTcuNS05LjUtNy4xODMgNy4xODN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-pdf-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDJfZmlsZS1wZGYtbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTggMCAyLjAwNXYxOS45OUMwIDIzLjEwMy44OTYgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDQgMCAyMS45OTUgMHpNMSAyMS45OTVWOC45NkM1LjE3NSA4Ljc1OCA5LjQ0MyA3LjMxNyAxNSA1djE4SDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NXpNMTYgOWgydjE0aC0yVjl6bTcgMTIuOTk1QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gxOVY4aC0zVjMuNUMxMC40MzQgNS44MiA1LjMwNyA3LjY5IDEgNy45NTJWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5QzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OXpNMTIgMTkuNXYtMWMtMyAxLTYgMi05IDIuNXYxYzMtLjUgNi0xLjUgOS0yLjV6TTMgMThjMy0uNSA2LTEuNSA5LTIuNXYtMWMtMyAxLTYgMi05IDIuNXYxem0wLTV2MWMzLS41IDYtMS41IDktMi41di0xYy0zIDEtNiAyLTkgMi41eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-pdf-icon.size-48,
.cp-pdf-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDFfZmlsZS1wZGYteGw8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDB6TTQuMDEgNDdDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVYxNC45ODVjOS4zODQtLjA3NyAxOS4xMzItMi4zMjUgMjktNi43MTJWNDdINC4wMXpNNDAgNDdoLTRWMTZoMy41MDJjLjI3NCAwIC40OTguMjIuNDk4LjQ5M1Y0N3ptLTUgMGgtNFYxMmgzLjUwMmMuMjc0IDAgLjQ5OC4yMjQuNDk4LjVWNDd6bTguOTkgMEg0MVYxNi40OTNDNDEgMTUuNjcgNDAuMzI4IDE1IDM5LjUwMiAxNUgzNnYtMi41YzAtLjgyNy0uNjcyLTEuNS0xLjQ5OC0xLjVIMzFWNi43MjRsLS43MDcuMzJjLTkuOTc4IDQuNTM3LTE5LjgzIDYuODYyLTI5LjI5MyA2Ljk0VjQuMDFDMSAyLjM0NyAyLjM0OCAxIDQuMDEgMWgzOS45OEM0NS42NTMgMSA0NyAyLjM0NyA0NyA0LjAxdjM5Ljk4YzAgMS42NjMtMS4zNDggMy4wMS0zLjAxIDMuMDF6TTUgMjRjOC41LS41IDEyLTEgMjAtNHYtMWMtOCAzLTEyIDMuNS0yMCA0djF6bTAgNmM4LjUtLjUgMTItMSAyMC00di0xYy04IDMtMTIgMy41LTIwIDR2MXptMCA2YzguNS0uNSAxMi0xIDIwLTR2LTFjLTggMy0xMiAzLjUtMjAgNHYxem0wIDZjOC41LS41IDEyLTEgMjAtNHYtMWMtOCAzLTEyIDMuNS0yMCA0djF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-text-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDNfZmlsZS10ZXh0LWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0zIDE0djFoMTh2LTFIM3ptMC04djFoMTVWNkgzem0wIDR2MWgxMXYtMUgzem0wIDh2MWgxM3YtMUgzeiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-text-icon.size-48,
.cp-text-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDJfZmlsZS10ZXh0LXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTUgMzF2MWgzOHYtMUg1em0wLTE0djFoMzN2LTFINXptMC03djFoMjh2LTFINXptMCAxNHYxaDI0di0xSDV6bTAgMTR2MWgyOHYtMUg1eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-document-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDRfZmlsZS1kb2N1bWVudC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMjEuOTk1IDBIMi4wMDVDLjg5OCAwIDAgLjg5OCAwIDIuMDA1djE5Ljk5QzAgMjMuMTAzLjg5NiAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwNCAwIDIxLjk5NSAwek0yMyAyMS45OTVDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTlDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5ek0zIDEwaDd2MUgzdi0xem0wLTRoOXYxSDNWNnptMCA4aDE2djFIM3YtMXptMCA0aDE0djFIM3YtMXpNMTkuOTk3IDVoLTQuOTk0QzE0LjQ1IDUgMTQgNS40NSAxNCA1Ljk5djQuMDJjMCAuNTQ2LjQzNy45ODggMSAuOTloNC45OTdjLjU1NCAwIDEuMDAzLS40NSAxLjAwMy0uOTlWNS45OWMwLS41NDctLjQzOC0uOTktMS4wMDMtLjk5em0tNC4wMDcgNWwyLjQ3Ni0yLjUgMS41MzYuOTFjMCAuODUtLjAwMiAxLjYtLjAwMiAxLjYgMC0uMDEtMi41NTUtLjAxLTQuMDEtLjAxek0xNSA5LjU0M2MtLjAwNC0xLjA0OCAwLTMuNTUyIDAtMy41NTIgMCAuMDEyIDQuOTk3LjAxIDQuOTk3LjAxLjAwNCAwIC4wMDUuNjEyLjAwNSAxLjM1N2wtMS41MzYtLjk5MkwxNSA5LjU0M3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-document-icon.size-48,
.cp-document-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDNfZmlsZS1kb2N1bWVudC14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00My45OSAxQzQ1LjY1MyAxIDQ3IDIuMzQ3IDQ3IDQuMDF2MzkuOThjMCAxLjY2NC0xLjM0OCAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjU0IDEgNDMuOTlWNC4wMUMxIDIuMzQ3IDIuMzQ4IDEgNC4wMSAxaDM5Ljk4ek01IDM5aDI4di0xSDV2MXptMC03aDMydi0xSDV2MXptMC0yMWgxN3YtMUg1djF6bTAgN2gxNHYtMUg1djF6bTAgN2gxOXYtMUg1djF6bTM2LjAwOC0xNUgyOS45OTJjLTEuMSAwLTEuOTkyLjg5NC0xLjk5MiAxLjk5MnYxMS4wMTZjMCAxLjEuODk0IDEuOTkyIDEuOTkyIDEuOTkyaDExLjAxNmMxLjEgMCAxLjk5Mi0uODkzIDEuOTkyLTEuOTkyVjExLjk5MmMwLTEuMS0uODk0LTEuOTkyLTEuOTkyLTEuOTkyek0yOS45OTIgMjRjLS41NDcgMC0uOTkyLS40NDQtLjk5Mi0uOTkyVjIxbDMtMy41IDUgNi41aC03LjAwOHptMTEuMDE2IDBoLTIuODlsLTEuMS0xLjQzNSAyLjQ2Ni0yLjA2NSAyLjQzMyAyLjljLS4xNTMuMzUzLS41MDIuNi0uOTEuNnpNNDIgMjEuOTJMMzkuNDg0IDE5bC0zLjEzIDIuNjk1TDMyIDE2bC0zIDMuNXYtNy41MDhjMC0uNTQ3LjQ0NS0uOTkyLjk5Mi0uOTkyaDExLjAxNmMuNTQ3IDAgLjk5Mi40NDUuOTkyLjk5MnY5LjkyOHptLTEuNzY0LTkuMTAzbC0xLjk3Ni42MDYtLjc2LTEuOTIzLS43NiAxLjkyMy0xLjk3Ni0uNjA2IDEuMDMgMS43OTQtMS43MDYgMS4xNyAyLjA0NC4zMS0uMTUgMi4wNjRMMzcuNSAxNi43NWwxLjUyIDEuNDA0LS4xNTItMi4wNjMgMi4wNDQtLjMxLTEuNzA2LTEuMTcgMS4wMy0xLjc5M3pNMzcuNSAxNi4zYy0uNzE4IDAtMS4zLS41ODItMS4zLTEuMyAwLS43MTguNTgyLTEuMyAxLjMtMS4zLjcxOCAwIDEuMy41ODIgMS4zIDEuMyAwIC43MTgtLjU4MiAxLjMtMS4zIDEuM3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-spreadsheet-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDVfZmlsZS1zcHJlYWRzaGVldC1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDMgMCAyMS45OTUgMEgyLjAwNUMuODk4IDAgMCAuODk3IDAgMi4wMDV6TTIxLjk5NSAxQzIyLjU1IDEgMjMgMS40NSAyMyAyLjAwNXYxOS45OUMyMyAyMi41NSAyMi41NSAyMyAyMS45OTUgMjNIMi4wMDVDMS40NSAyMyAxIDIyLjU1IDEgMjEuOTk1VjIuMDA1QzEgMS40NSAxLjQ1IDEgMi4wMDUgMWgxOS45OXpNMTYgMTh2LTRoN3YtMWgtN1Y5aDdWOEg4VjFIN3Y3SDF2MWg2djE0aDFWOWg3djRIOHYxaDd2NEg4djFoN3Y0aDF2LTRoN3YtMWgtN3oiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-spreadsheet-icon.size-48,
.cp-spreadsheet-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDRfZmlsZS1zcHJlYWRzaGVldC14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCA0LjAxdjM5Ljk4QzAgNDYuMjA1IDEuNzk0IDQ4IDQuMDEgNDhoMzkuOThjMi4yMTUgMCA0LjAxLTEuNzk0IDQuMDEtNC4wMVY0LjAxQzQ4IDEuNzk1IDQ2LjIwNiAwIDQzLjk5IDBINC4wMUMxLjc5NSAwIDAgMS43OTQgMCA0LjAxek00NyAxNlY0LjAxQzQ3IDIuMzQ3IDQ1LjY1MyAxIDQzLjk5IDFIMTZ2MTVoMzF6bTAgMXYxMEgzMlYxN2gxNXptMCAxMXYxMEgzMlYyOGgxNXptMCAxMXY0Ljk5YzAgMS42NjMtMS4zNDcgMy4wMS0zLjAxIDMuMDFIMzJ2LThoMTV6bS0xNiA4SDE2di04aDE1djh6bS0xNiAwSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVYxN2gxNHYzMHpNMSAxNlY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFIMTV2MTVIMXptMTUgMTJoMTV2MTBIMTZWMjh6bTAtMTFoMTV2MTBIMTZWMTd6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-presentation-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDZfZmlsZS1wcmVzZW50YXRpb24tbGFyZ2U8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTAgMi4wMDV2MTkuOTlDMCAyMy4xMDIuODk3IDI0IDIuMDA1IDI0aDE5Ljk5QzIzLjEwMiAyNCAyNCAyMy4xMDMgMjQgMjEuOTk1VjIuMDA1QzI0IC44OTggMjMuMTAzIDAgMjEuOTk1IDBIMi4wMDVDLjg5OCAwIDAgLjg5NyAwIDIuMDA1ek0yMS45OTUgMUMyMi41NSAxIDIzIDEuNDUgMjMgMi4wMDV2MTkuOTlDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTl6TTE2IDd2MWg1VjdoLTV6bTAgM3YxaDV2LTFoLTV6bS0xIDN2MWg2di0xaC02ek0zIDE3djFoMTh2LTFIM3pNOC41IDQuNWMtMi43NiAwLTUgMi4yMzgtNSA1czIuMjQgNSA1IDUgNS0yLjIzOCA1LTUtMi4yNC01LTUtNXptLTQgNWMwLTIuMDQgMS41MjctMy43MjIgMy41LTMuOTd2My42MjNsLTMuMzkzIDEuMjczYy0uMDctLjI5Ny0uMTA3LS42MDgtLjEwNy0uOTI2em00IDRjLTEuNTM3IDAtMi44Ny0uODY3LTMuNTQtMi4xMzhsMy40MTQtMS4yOCAyLjU4IDIuNTc4Yy0uNjguNTI3LTEuNTMuODQtMi40NTQuODR6bTMuMTYtMS41NDdMOSA5LjI5M1Y1LjUzYzEuOTczLjI0OCAzLjUgMS45MyAzLjUgMy45NyAwIC45MjUtLjMxNCAxLjc3Ni0uODQgMi40NTN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-presentation-icon.size-48,
.cp-presentation-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDVfZmlsZS1wcmVzZW50YXRpb24teGw8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTAgNC4wMXYzOS45OEMwIDQ2LjIwNSAxLjc5NCA0OCA0LjAxIDQ4aDM5Ljk4YzIuMjE1IDAgNC4wMS0xLjc5NCA0LjAxLTQuMDFWNC4wMUM0OCAxLjc5NSA0Ni4yMDYgMCA0My45OSAwSDQuMDFDMS43OTUgMCAwIDEuNzk0IDAgNC4wMXpNNDMuOTkgMUM0NS42NTMgMSA0NyAyLjM0NyA0NyA0LjAxdjM5Ljk4YzAgMS42NjMtMS4zNDcgMy4wMS0zLjAxIDMuMDFINC4wMUMyLjM0NyA0NyAxIDQ1LjY1MyAxIDQzLjk5VjQuMDFDMSAyLjM0NyAyLjM0NyAxIDQuMDEgMWgzOS45OHpNMjggMTJ2MWgxNXYtMUgyOHptMCA2djFoMTV2LTFIMjh6bS0yIDZ2MWgxN3YtMUgyNnpNNSAzOHYxaDM4di0xSDV6bTAtN3YxaDM4di0xSDV6bTAtMTQuNWMwIDUuMjQ3IDQuMjUzIDkuNSA5LjUgOS41czkuNS00LjI1MyA5LjUtOS41UzE5Ljc0NyA3IDE0LjUgNyA1IDExLjI1MyA1IDE2LjV6bTEgMGMwLTQuNTI2IDMuNTM4LTguMjI2IDgtOC40ODV2OC4xMzhsLTcuNjIzIDIuODZDNi4xMzIgMTguMjE3IDYgMTcuMzcyIDYgMTYuNXptOC41IDguNWMtMy40NjcgMC02LjQ1LTIuMDc1LTcuNzcyLTUuMDUybDcuNjQ2LTIuODY3IDUuNzczIDUuNzc0QzE4LjY0NSAyNC4xOSAxNi42NjcgMjUgMTQuNSAyNXptNi4zNTMtMi44NTNMMTUgMTYuMjkzVjguMDE1YzQuNDYyLjI2IDggMy45NiA4IDguNDg1IDAgMi4xNjctLjgxIDQuMTQ2LTIuMTQ3IDUuNjQ3eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-code-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDdfZmlsZS1jb2RlLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0yLjUgOS41bDUtNSAxIC41TDQgOS41IDguNSAxNGwtMSAuNS01LTV6bTE5IDVsLTUuMDUtNS0uOTUuNSA0LjUgNC41LTQuNSA0LjUuOTUuNSA1LjA1LTV6TTkgMjFoMWw1LTE4aC0xTDkgMjF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-code-icon.size-48,
.cp-code-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwMzlfZmlsZS1jb2RlLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTYgMTcuNWwxMC0xMCAuNSAxLTkgOSA5IDktLjUgMS0xMC0xMHptMzcgMTJsLTEwLTEwLS41IDEgOSA5LTkgOSAuNSAxIDEwLTEwek0xOCA0Mi45NjJoMUwzMCA2aC0xTDE4IDQyLjk2MnoiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-multimedia-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMDhfZmlsZS12aWRlby1sYXJnZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMCAyLjAwNXYxOS45OUMwIDIzLjEwMi44OTcgMjQgMi4wMDUgMjRoMTkuOTlDMjMuMTAyIDI0IDI0IDIzLjEwMyAyNCAyMS45OTVWMi4wMDVDMjQgLjg5OCAyMy4xMDMgMCAyMS45OTUgMEgyLjAwNUMuODk4IDAgMCAuODk3IDAgMi4wMDV6TTguMjkzIDFsLTMgM0gxLjcwN2wzLTNoMy41ODZ6TTYuNzA3IDRoMy41ODZsMy0zSDkuNzA3bC0zIDN6bTgtM2wtMyAzaDMuNTg2bDMtM2gtMy41ODZ6bTguMTMuNDZDMjIuNjYgMS4xOCAyMi4zNSAxIDIxLjk5NiAxaC0yLjI4OGwtMyAzaDMuNTg2bDIuNTQ1LTIuNTR6TTMuMjk0IDFIMi4wMDVDMS40NSAxIDEgMS40NSAxIDIuMDA1djEuMjg4TDMuMjkzIDF6TTIzIDRoLTEuMjkzTDIzIDIuNzA3VjR6bTAgMXYxNi45OTVDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVY1aDIyek05IDE5LjVsNy01LjUtNy01LjV2MTF6bTUuNS01LjQ3TDEwIDE3LjV2LTdsNC41IDMuNTN6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-multimedia-icon.size-48,
.cp-multimedia-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDZfZmlsZS12aWRlby14bDwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMHpNNDcgMTF2MzIuOTljMCAxLjY2My0xLjM0NyAzLjAxLTMuMDEgMy4wMUg0LjAxQzIuMzQ3IDQ3IDEgNDUuNjUzIDEgNDMuOTlMMS4wMyAxMUg0N3ptLTEyLjc5Mi0xaC01LjMzbDguODU1LTloNS4zM2wtOC44NTUgOXptLTcgMGgtNS4zM2w4Ljg1NS05aDUuMzNsLTguODU1IDl6bS03IDBoLTUuMzNsOC44NTUtOWg1LjMzbC04Ljg1NSA5em0tNyAwaC01LjMzbDguODU1LTloNS4zM2wtOC44NTUgOXptLTcgMEgxLjAzMnYtLjI1TDkuNzI2IDFoNS4zMzhsLTguODU2IDl6TTEgNC4wMUMxIDIuMzQ3IDIuMzQ3IDEgNC4wMSAxaDMuOTkzTDEgOC4wNDhWNC4wMXpNNDIuOTc0IDEwTDQ3IDUuOTMzVjEwaC00LjAyNnptLTcuMDk3IDBsOC43ODMtOC45MjVDNDYgMS4zOCA0NyAyLjU3NyA0NyA0LjEyNUw0MS4xODYgMTBoLTUuMzF6TTE5IDM5bDEyLTEwLjVMMTkgMTl2MjB6bTEtMThsOS41IDcuNUwyMCAzN1YyMXoiLz48L2c+PC9nPjwvc3ZnPg==");
}
.cp-archive-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMTBfZmlsZS1hcmNoaXZlLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0xMCAxOGg0di02aC0ydi0xaC0ydjd6bTItN2gydi0xaC0ydjF6bTAtMmgyVjhoLTJ2MXptLTIgMWgyVjloLTJ2MXptMC0yaDJWN2gtMnYxem0yLTFoMlY2aC0ydjF6bS0yLTFoMlY1aC0ydjF6bTItMWgyVjRoLTJ2MXptLTItMWgyVjNoLTJ2MXptMi0xaDJWMmgtMnYxem0tMi0xaDJWMWgtMnYxem0xMS45OTUtMUMyMi41NSAxIDIzIDEuNDUgMjMgMi4wMDV2MTkuOTlDMjMgMjIuNTUgMjIuNTUgMjMgMjEuOTk1IDIzSDIuMDA1QzEuNDUgMjMgMSAyMi41NSAxIDIxLjk5NVYyLjAwNUMxIDEuNDUgMS40NSAxIDIuMDA1IDFoMTkuOTl6TTAgMi4wMDV2MTkuOTlDMCAyMy4xMDMuODk3IDI0IDIuMDA1IDI0aDE5Ljk5QzIzLjEwMiAyNCAyNCAyMy4xMDMgMjQgMjEuOTk1VjIuMDA1QzI0IC44OTggMjMuMTAzIDAgMjEuOTk1IDBIMi4wMDVDLjg5NyAwIDAgLjg5OCAwIDIuMDA1ek0xMiAxNWMtLjU1MiAwLTEgLjQ0Ny0xIDEgMCAuNTUzLjQ0OCAxIDEgMXMxLS40NDcgMS0xYzAtLjU1My0uNDQ4LTEtMS0xeiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-archive-icon.size-48,
.cp-archive-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDhfZmlsZS1hcmNoaXZlLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5IDFDNDUuNjUzIDEgNDcgMi4zNDcgNDcgNC4wMXYzOS45OGMwIDEuNjYzLTEuMzQ3IDMuMDEtMy4wMSAzLjAxSDQuMDFDMi4zNDcgNDcgMSA0NS42NTMgMSA0My45OVY0LjAxQzEgMi4zNDcgMi4zNDcgMSA0LjAxIDFoMzkuOTh6TTIwIDMxLjAwMnY4Ljk5QzIwIDQxLjEgMjAuODg3IDQyIDIxLjk5OCA0Mmg0LjAwNEMyNy4xMDUgNDIgMjggNDEuMDk4IDI4IDM5Ljk5di04Ljk4OGMwLS41NTMtLjQ1My0xLjAwMi0uOTk3LTEuMDAyaC02LjAwNmMtLjU1IDAtLjk5Ny40NTYtLjk5NyAxLjAwMnpNMjcuMDAzIDMxYy0uMDA2IDAtLjAwMyA4Ljk5LS4wMDMgOC45OSAwIC41NTgtLjQ1IDEuMDEtLjk5OCAxLjAxaC00LjAwNGMtLjU1NSAwLS45OTgtLjQ0Ny0uOTk4LTEuMDF2LTguOTg4TDI3LjAwMyAzMXpNMjIgMzh2MWMwIC41NTMuNDQzIDEgMS4wMSAxaDEuOThjLjU1OCAwIDEuMDEtLjQ0MyAxLjAxLTF2LTFjMC0uNTUzLS40NDMtMS0xLjAxLTFoLTEuOThjLS41NTggMC0xLjAxLjQ0My0xLjAxIDF6bTIuOTkgMGMuMDEyIDAgLjAxLS4wMDIuMDEgMHYxaC0xLjk5Yy0uMDEyIDAtLjAxLjAwMi0uMDEgMHYtMWgxLjk5ek0yMCA2djFoNVY2aC01em0wLTR2MWg1VjJoLTV6bTMgNnYxaDVWOGgtNXptMC00djFoNVY0aC01em0tMyA2djFoNXYtMWgtNXptMyAydjFoNXYtMWgtNXptLTMgMnYxaDV2LTFoLTV6bTMgMnYxaDV2LTFoLTV6bS0zIDJ2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0zLTZ2MWg1di0xaC01em0wIDR2MWg1di0xaC01em0wIDR2MWg1di0xaC01eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-unknown-file-type-icon.size-24 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaDAwMTFfZmlsZS1nZW5lcmljLWxhcmdlPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDIuMDA1djE5Ljk5QzAgMjMuMTAyLjg5NyAyNCAyLjAwNSAyNGgxOS45OUMyMy4xMDIgMjQgMjQgMjMuMTAzIDI0IDIxLjk5NVYyLjAwNUMyNCAuODk4IDIzLjEwMyAwIDIxLjk5NSAwSDIuMDA1Qy44OTggMCAwIC44OTcgMCAyLjAwNXpNMjEuOTk1IDFDMjIuNTUgMSAyMyAxLjQ1IDIzIDIuMDA1djE5Ljk5QzIzIDIyLjU1IDIyLjU1IDIzIDIxLjk5NSAyM0gyLjAwNUMxLjQ1IDIzIDEgMjIuNTUgMSAyMS45OTVWMi4wMDVDMSAxLjQ1IDEuNDUgMSAyLjAwNSAxaDE5Ljk5ek0xMCAxNS40OGMwIDEuNTEgMSAyLjUyIDIuNSAyLjUyczIuNS0xLjAxIDIuNS0yLjUxNlY2Ljk5N0MxNSA0LjUgMTMgMyAxMSAzUzcgNC41IDcgNy4wMDJ2OC40OEM3IDE4LjUyIDkuNDY2IDIxIDEyLjUgMjFjMy4wMzcgMCA1LjUtMi40NjcgNS41LTUuNVY2aC0xdjkuNWMwIDIuNDgtMiA0LjUtNC41IDQuNVM4IDE3Ljk3IDggMTUuNDhWNy4wMDNDOCA1IDkuNSA0IDExIDRzMyAxIDMgMi45OTd2OC40ODdDMTQgMTYuNSAxMy41IDE3IDEyLjUgMTdzLTEuNS0uNS0xLjUtMS41MlYxMGgtMXY1LjQ4eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-unknown-file-type-icon.size-48,
.cp-unknown-file-type-icon.size-96 {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+NDgvaTAwNDlfZmlsZS1nZW5lcmljLXhsPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0wIDQuMDF2MzkuOThDMCA0Ni4yMDUgMS43OTQgNDggNC4wMSA0OGgzOS45OGMyLjIxNSAwIDQuMDEtMS43OTQgNC4wMS00LjAxVjQuMDFDNDggMS43OTUgNDYuMjA2IDAgNDMuOTkgMEg0LjAxQzEuNzk1IDAgMCAxLjc5NCAwIDQuMDF6TTQzLjk5Ljk4QzQ1LjY1My45OCA0NyAyLjMyNiA0NyAzLjk5djQwLjAxNmMwIDEuNjY1LTEuMzQ3IDMuMDEzLTMuMDEgMy4wMTNINC4wMUMyLjM0NyA0Ny4wMiAxIDQ1LjY3NCAxIDQ0LjAxVjMuOTkyQzEgMi4zMjcgMi4zNDcuOTggNC4wMS45OGgzOS45OHpNMTkgMzAuOTk1QzE5IDM0IDIxIDM2IDI0IDM2czUtMiA1LTVWMTMuOTg2QzI5IDEwIDI2IDYuOTkgMjEuOTk2IDYuOTlzLTcuMDA0IDMuMDEtNy4wMDQgN3YxOC4wMDVjMCA1LjAwNSA0IDkgOS4wMDQgOVMzMyAzNyAzMyAzMS45ODJWMTIuOTk0aC0xdjE4Ljk4OGMwIDQuNTE4LTMuNSA4LjAxMy04LjAwNCA4LjAxM3MtOC4wMDQtMy40OTUtOC4wMDQtOFYxMy45OWMwLTMuNDkgMi41LTYgNi4wMDQtNkMyNS41IDcuOTkgMjggMTAuNSAyOCAxMy45ODVWMzFjMCAyLjUtMiA0LTQgNHMtNC0xLjUtNC00LjAwNFYyMGgtMXYxMC45OTZ6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
</style><style type="text/css">.cp-control-panel-close {
  border-left: 1px solid #333;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvaTAwMjdfY2xvc2UtZGlhbG9nPC90aXRsZT48ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz48cGF0aCBkPSJNOS42NCA4bDQuMDE2IDQuMDE2Yy4yMy4yMy4zNDQuNTAyLjM0NC44MiAwIC4zMTgtLjExNS41OS0uMzQ0LjgyLS4yMy4yMy0uNTAyLjM0NC0uODIuMzQ0LS4zMTggMC0uNTktLjExNS0uODItLjM0NEw4IDkuNjRsLTQuMDE2IDQuMDE2Yy0uMjMuMjMtLjUwMi4zNDQtLjgyLjM0NC0uMzE4IDAtLjU5LS4xMTUtLjgyLS4zNDQtLjIzLS4yMy0uMzQ0LS41MDItLjM0NC0uODIgMC0uMzE4LjExNS0uNTkuMzQ0LS44Mkw2LjM2IDggMi4zNzQgNC4wMTZjLS4yMy0uMjMtLjM0NC0uNTAzLS4zNDQtLjgyIDAtLjMxOC4xMTYtLjU4Ny4zNDUtLjgwNS4yMy0uMjMuNTAzLS4zNC44Mi0uMzQuMzE4IDAgLjU5LjExMy44Mi4zNDNMOCA2LjM3Nmw0LjAzLTQuMDNjLjIzLS4yMy41MDQtLjM0NS44MjItLjM0NS4zMTcgMCAuNTkuMTE3LjgyLjM0Ni4yMi4yMy4zMjguNTAyLjMyOC44MiAwIC4zMTgtLjExLjU4Ni0uMzI4LjgwNUw5LjY0MiA4eiIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+");
}
.cp-control-panel-download,
.cp-waiting-message-download {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvZG93bmxvYWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0zIDE0di0xaDEwdjFIM3ptMTAtNy44NEw4LjIgMTFoLS4zOUwzIDYuMTZsMS4xMi0xLjEzTDcgNy45MlYzLjVjLjA5LTEgLjQ1LTEuNSAxLTEuNXMuODkuMzcgMSAxLjV2NC40MWwyLjkxLTIuODVMMTMgNi4xNnoiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==");
}
.cp-control-panel-more {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+MTYvaTAxMjdfbW9yZTwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PHBhdGggZD0iTTQgOGMwLS41NTItLjE5NS0xLjAyMy0uNTg2LTEuNDE0QzMuMDI0IDYuMTk2IDIuNTUyIDYgMiA2Yy0uNTUyIDAtMS4wMjMuMTk1LTEuNDE0LjU4NkMuMTk2IDYuOTc2IDAgNy40NDggMCA4YzAgLjU1Mi4xOTUgMS4wMjMuNTg2IDEuNDE0LjM5LjM5Ljg2Mi41ODYgMS40MTQuNTg2LjU1MiAwIDEuMDIzLS4xOTUgMS40MTQtLjU4NkMzLjgwNCA5LjAyNCA0IDguNTUyIDQgOHptNiAwYzAtLjU1Mi0uMTk1LTEuMDIzLS41ODYtMS40MTRDOS4wMjQgNi4xOTYgOC41NTIgNiA4IDZjLS41NTIgMC0xLjAyMy4xOTUtMS40MTQuNTg2QzYuMTk2IDYuOTc2IDYgNy40NDggNiA4YzAgLjU1Mi4xOTUgMS4wMjMuNTg2IDEuNDE0LjM5LjM5Ljg2Mi41ODYgMS40MTQuNTg2LjU1MiAwIDEuMDIzLS4xOTUgMS40MTQtLjU4Ni4zOS0uMzkuNTg2LS44NjIuNTg2LTEuNDE0em02IDBjMC0uNTUyLS4xOTUtMS4wMjMtLjU4Ni0xLjQxNEMxNS4wMjQgNi4xOTYgMTQuNTUyIDYgMTQgNmMtLjU1MiAwLTEuMDIzLjE5NS0xLjQxNC41ODYtLjM5LjM5LS41ODYuODYyLS41ODYgMS40MTQgMCAuNTUyLjE5NSAxLjAyMy41ODYgMS40MTQuMzkuMzkuODYyLjU4NiAxLjQxNC41ODYuNTUyIDAgMS4wMjMtLjE5NSAxLjQxNC0uNTg2LjM5LS4zOS41ODYtLjg2Mi41ODYtMS40MTR6IiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=");
  z-index: 201;
}
.cp-control-panel-more.cp-dropdown {
  position: relative;
  display: inline-block;
  text-indent: 0;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content {
  pointer-events: auto;
  display: block;
  margin-top: 0;
  padding: 0;
  white-space: nowrap;
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s ease, visibility 0s ease 0.5s;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li {
  font-size: 14px;
  list-style: none;
  padding: 0;
  background-color: #333;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li:hover {
  background-color: #707070;
}
.cp-control-panel-more.cp-dropdown .cp-dropdown-content li a {
  display: block;
  line-height: 2.25rem;
  padding: 0 1.5rem 0 1rem;
  color: #fff;
  text-decoration: none;
}
.cp-control-panel-more.cp-dropdown:focus {
  pointer-events: none;
}
.cp-control-panel-more.cp-dropdown:focus .cp-dropdown-content {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.25s ease, visibility 0s ease 0s;
}
.cp-control-panel-more.cp-dropdown:focus .tooltip {
  display: none !important;
}
</style><style type="text/css">.cp-toolbar {
  display: block;
  opacity: 0;
  position: absolute;
  left: 50%;
  bottom: 20px;
  background-color: #333333;
  border-radius: 5px;
  margin-left: -75px;
  /* to center the div */
}
.cp-toolbar > * {
  position: relative;
}
.cp-toolbar > *:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.cp-toolbar > *:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.cp-toolbar > *::before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  opacity: 1;
}
.cp-toolbar-prev-page::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+YXJyb3ctdXA8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGcgZmlsbD0iI2ZmZiI+PHBhdGggZD0iTTMgMTFsNS4wMS01TDEzIDExbC41LTEtNS40OS01LjVMMi41IDEwbC41IDF6Ii8+PC9nPjwvZz48L3N2Zz4=");
}
.cp-toolbar-next-page::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+YXJyb3ctZG93bjwvdGl0bGU+PGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48ZyBmaWxsPSIjZmZmIj48cGF0aCBkPSJNMyA1bDUuMDEgNUwxMyA1bC41IDEtNS40OSA1LjVMMi41IDYgMyA1eiIvPjwvZz48L2c+PC9zdmc+");
}
.cp-toolbar-fit::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1maXQ8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaCBCZXRhLjwvZGVzYz4KICAgIDxkZWZzPjwvZGVmcz4KICAgIDxnIGlkPSJXaGl0ZSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImNwLXRvb2xiYXItZml0IiBmaWxsPSIjRkZGRkZGIj4KICAgICAgICAgICAgPGcgaWQ9IlBhZ2UtMSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMi41MDAwMDAsIDIuNTAwMDAwKSI+CiAgICAgICAgICAgICAgICA8cG9seWdvbiBpZD0iRmlsbC0xIiBwb2ludHM9IjAuOTk5OSAxMi4wMDAyIDAuOTk5OSA4Ljk5OTIgLTAuMDAwMSA4Ljk5OTIgLTAuMDAwMSAxNC4wMDAyIDUuMDAwOSAxNC4wMDAyIDUuMDAwOSAxMy4wMDAyIDEuOTk5OSAxMy4wMDAyIDUuMDAwOSAxMC4wMDAyIDMuOTk5OSA4Ljk5OTIiPjwvcG9seWdvbj4KICAgICAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJGaWxsLTIiIHBvaW50cz0iMCAtMC4wMDA0IDAgNC45OTk2IDEgNC45OTk2IDEgMi4wMDA2IDQgNC45OTk2IDUgNC4wMDA2IDIgMS4wMDA2IDUgMS4wMDA2IDUgLTAuMDAwNCI+PC9wb2x5Z29uPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTksLTAuMDAwNCBMOSwxLjAwMDYgTDEyLDEuMDAwNiBMOSw0LjAwMDYgTDEwLDQuOTk5NiBMMTMsMi4wMDA2IEwxMyw0Ljk5OTYgTDE0LDQuOTk5NiBMMTQsLTAuMDAwNCBMOSwtMC4wMDA0IFogTTEzLDguOTk5NiBMMTMsMTIuMDAwNiBMMTAsOC45OTk2IEw5LDEwLjAwMDYgTDEyLDEzLjAwMDYgTDksMTMuMDAwNiBMOSwxMy45OTk2IEwxNCwxMy45OTk2IEwxNCw4Ljk5OTYgTDEzLDguOTk5NiBaIiBpZD0iRmlsbC0zIj48L3BhdGg+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar-minus::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1taW51czwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoIEJldGEuPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IldoaXRlIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iY3AtdG9vbGJhci1taW51cyIgZmlsbD0iI0ZGRkZGRiI+CiAgICAgICAgICAgIDxnIGlkPSJQYWdlLTEiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDEuMDAwMDAwLCAxLjAwMDAwMCkiPgogICAgICAgICAgICAgICAgPHBvbHlnb24gaWQ9IkZpbGwtMSIgcG9pbnRzPSI0LjEyNSA3LjgyNTUgMTAuMTI1IDcuODI1NSAxMC4xMjUgNi43OTU1IDQuMTI1IDYuNzk1NSI+PC9wb2x5Z29uPgogICAgICAgICAgICAgICAgPHBhdGggZD0iTTcuMTI1LDEzLjM3MiBDMy44ODYsMTMuMzcyIDEuMjUsMTAuNjUzIDEuMjUsNy4zMTEgQzEuMjUsMy45NjkgMy44ODYsMS4yNSA3LjEyNSwxLjI1IEMxMC4zNjQsMS4yNSAxMywzLjk2OSAxMyw3LjMxMSBDMTMsMTAuNjUzIDEwLjM2NCwxMy4zNzIgNy4xMjUsMTMuMzcyIE0xNi43MTYsMTUuMTQ3IEwxMy4xOCwxMS42MTIgQzEzLjExOSwxMS41NTIgMTMuMDQ1LDExLjUxOCAxMi45NzQsMTEuNDc2IEMxMy43NzYsMTAuMjkzIDE0LjI1LDguODU4IDE0LjI1LDcuMzExIEMxNC4yNSwzLjI4IDExLjA1NCwwIDcuMTI1LDAgQzMuMTk2LDAgMCwzLjI4IDAsNy4zMTEgQzAsMTEuMzQzIDMuMTk2LDE0LjYyMiA3LjEyNSwxNC42MjIgQzguODQyLDE0LjYyMiAxMC40MTksMTMuOTk1IDExLjY1LDEyLjk1MyBDMTEuNjc5LDEyLjk5IDExLjY5MiwxMy4wMzMgMTEuNzI2LDEzLjA2NiBMMTUuMjYyLDE2LjYwMSBDMTUuNjUyLDE2Ljk5MiAxNi4yODUsMTYuOTkyIDE2LjY3NiwxNi42MDEgTDE2LjcxNiwxNi41NjIgQzE3LjEwNiwxNi4xNzIgMTcuMTA2LDE1LjUzOCAxNi43MTYsMTUuMTQ3IiBpZD0iRmlsbC0yIj48L3BhdGg+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar-plus::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1wbHVzPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2ggQmV0YS48L2Rlc2M+CiAgICA8ZGVmcz48L2RlZnM+CiAgICA8ZyBpZD0iV2hpdGUiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJjcC10b29sYmFyLXBsdXMiIGZpbGw9IiNGRkZGRkYiPgogICAgICAgICAgICA8ZyBpZD0idG9vbGJhci1wbHVzIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxLjAwMDAwMCwgMS4wMDAwMDApIj4KICAgICAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJGaWxsLTEiIHBvaW50cz0iNi42MjUgNC4yMjUxIDcuNjI1IDQuMjI1MSA3LjYyNSA2Ljc5NTEgMTAuMTI1IDYuNzk1MSAxMC4xMjUgNy44MjUxIDcuNjI1IDcuODI1MSA3LjYyNSAxMC4zOTQxIDYuNjI1IDEwLjM5NDEgNi42MjUgNy44MjUxIDQuMTI1IDcuODI1MSA0LjEyNSA2Ljc5NTEgNi42MjUgNi43OTUxIj48L3BvbHlnb24+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNNy4xMjUsMTMuMzcyIEMzLjg4NiwxMy4zNzIgMS4yNSwxMC42NTMgMS4yNSw3LjMxMSBDMS4yNSwzLjk2OSAzLjg4NiwxLjI1IDcuMTI1LDEuMjUgQzEwLjM2NCwxLjI1IDEzLDMuOTY5IDEzLDcuMzExIEMxMywxMC42NTMgMTAuMzY0LDEzLjM3MiA3LjEyNSwxMy4zNzIgTTE2LjcxNiwxNS4xNDcgTDEzLjE4LDExLjYxMiBDMTMuMTE5LDExLjU1MiAxMy4wNDUsMTEuNTE4IDEyLjk3NCwxMS40NzYgQzEzLjc3NiwxMC4yOTMgMTQuMjUsOC44NTggMTQuMjUsNy4zMTEgQzE0LjI1LDMuMjggMTEuMDU0LDAgNy4xMjUsMCBDMy4xOTYsMCAwLDMuMjggMCw3LjMxMSBDMCwxMS4zNDMgMy4xOTYsMTQuNjIyIDcuMTI1LDE0LjYyMiBDOC44NDIsMTQuNjIyIDEwLjQxOSwxMy45OTUgMTEuNjUsMTIuOTUzIEMxMS42NzksMTIuOTkgMTEuNjkyLDEzLjAzMyAxMS43MjYsMTMuMDY2IEwxNS4yNjIsMTYuNjAxIEMxNS42NTIsMTYuOTkyIDE2LjI4NSwxNi45OTIgMTYuNjc2LDE2LjYwMSBMMTYuNzE2LDE2LjU2MiBDMTcuMTA2LDE2LjE3MiAxNy4xMDYsMTUuNTM4IDE2LjcxNiwxNS4xNDciIGlkPSJGaWxsLTIiPjwvcGF0aD4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+");
}
.cp-toolbar-presentation::before {
  background-image: url("data:image/svg+xml;base64,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");
}
.cp-toolbar-presentation-exit::before {
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+Cjxzdmcgd2lkdGg9IjE5cHgiIGhlaWdodD0iMTlweCIgdmlld0JveD0iMCAwIDE5IDE5IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCBCZXRhIDMuNy4yICgyODI3NikgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+CiAgICA8dGl0bGU+Y3AtdG9vbGJhci1wcmVzZW50YXRpb24tZXhpdDwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoIEJldGEuPC9kZXNjPgogICAgPGRlZnM+PC9kZWZzPgogICAgPGcgaWQ9IldoaXRlIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iY3AtdG9vbGJhci1wcmVzZW50YXRpb24tZXhpdCIgZmlsbD0iI0ZGRkZGRiI+CiAgICAgICAgICAgIDxwb2x5Z29uIGlkPSJQYWdlLTEiIHBvaW50cz0iMTQuNSAzLjUgOS41MDEgOC40OTkgNC41IDMuNSAzLjUgNC41IDguNTAxIDkuNDk5IDMuNSAxNC41IDQuNSAxNS41IDkuNTAxIDEwLjQ5OSAxNC41IDE1LjUgMTUuNSAxNC41IDEwLjUgOS40OTkgMTUuNSA0LjUiPjwvcG9seWdvbj4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==");
}
.cp-toolbar button,
.cp-toolbar a {
  height: 40px !important;
  width: 40px !important;
  background-color: transparent;
  cursor: pointer;
}
.cp-toolbar button.inactive,
.cp-toolbar a.inactive {
  pointer-events: none;
  cursor: default;
  opacity: 0.5;
}
.cp-waiting-message-spinner {
  position: relative;
}
.cp-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: 0;
}
.cp-spinner .cp-spinner-blades {
  position: absolute;
  top: -2px;
  opacity: 0.25;
}
.cp-spinner .cp-spinner-blades div {
  position: absolute;
  width: 12px;
  height: 4px;
  box-shadow: rgba(0, 0, 0, 0.0980392) 0px 0px 1px;
  border-radius: 2px;
  background: #ffffff;
  -webkit-transform-origin: left center 0px;
          transform-origin: left center 0px;
}
.cp-spinner .cp-spinner-blade-1 {
  -webkit-animation: cp-spinner-anim-1-12 0.666667s linear infinite;
          animation: cp-spinner-anim-1-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-1 div {
  -webkit-transform: rotate(0deg) translate(10px, 0px);
          transform: rotate(0deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-2 {
  -webkit-animation: cp-spinner-anim-2-12 0.666667s linear infinite;
          animation: cp-spinner-anim-2-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-2 div {
  -webkit-transform: rotate(30deg) translate(10px, 0px);
          transform: rotate(30deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-3 {
  -webkit-animation: cp-spinner-anim-3-12 0.666667s linear infinite;
          animation: cp-spinner-anim-3-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-3 div {
  -webkit-transform: rotate(60deg) translate(10px, 0px);
          transform: rotate(60deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-4 {
  -webkit-animation: cp-spinner-anim-4-12 0.666667s linear infinite;
          animation: cp-spinner-anim-4-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-4 div {
  -webkit-transform: rotate(90deg) translate(10px, 0px);
          transform: rotate(90deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-5 {
  -webkit-animation: cp-spinner-anim-5-12 0.666667s linear infinite;
          animation: cp-spinner-anim-5-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-5 div {
  -webkit-transform: rotate(120deg) translate(10px, 0px);
          transform: rotate(120deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-6 {
  -webkit-animation: cp-spinner-anim-6-12 0.666667s linear infinite;
          animation: cp-spinner-anim-6-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-6 div {
  -webkit-transform: rotate(150deg) translate(10px, 0px);
          transform: rotate(150deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-7 {
  -webkit-animation: cp-spinner-anim-7-12 0.666667s linear infinite;
          animation: cp-spinner-anim-7-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-7 div {
  -webkit-transform: rotate(180deg) translate(10px, 0px);
          transform: rotate(180deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-8 {
  -webkit-animation: cp-spinner-anim-8-12 0.666667s linear infinite;
          animation: cp-spinner-anim-8-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-8 div {
  -webkit-transform: rotate(210deg) translate(10px, 0px);
          transform: rotate(210deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-9 {
  -webkit-animation: cp-spinner-anim-9-12 0.666667s linear infinite;
          animation: cp-spinner-anim-9-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-9 div {
  -webkit-transform: rotate(240deg) translate(10px, 0px);
          transform: rotate(240deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-10 {
  -webkit-animation: cp-spinner-anim-10-12 0.666667s linear infinite;
          animation: cp-spinner-anim-10-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-10 div {
  -webkit-transform: rotate(270deg) translate(10px, 0px);
          transform: rotate(270deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-11 {
  -webkit-animation: cp-spinner-anim-11-12 0.666667s linear infinite;
          animation: cp-spinner-anim-11-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-11 div {
  -webkit-transform: rotate(300deg) translate(10px, 0px);
          transform: rotate(300deg) translate(10px, 0px);
}
.cp-spinner .cp-spinner-blade-12 {
  -webkit-animation: cp-spinner-anim-12-12 0.666667s linear infinite;
          animation: cp-spinner-anim-12-12 0.666667s linear infinite;
}
.cp-spinner .cp-spinner-blade-12 div {
  -webkit-transform: rotate(330deg) translate(10px, 0px);
          transform: rotate(330deg) translate(10px, 0px);
}
@-webkit-keyframes cp-spinner-anim-1-12 {
  0% {
    opacity: 0.25;
  }
  0.01% {
    opacity: 0.25;
  }
  0.02% {
    opacity: 1;
  }
  60.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-1-12 {
  0% {
    opacity: 0.25;
  }
  0.01% {
    opacity: 0.25;
  }
  0.02% {
    opacity: 1;
  }
  60.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-2-12 {
  0% {
    opacity: 0.25;
  }
  8.34333% {
    opacity: 0.25;
  }
  8.35333% {
    opacity: 1;
  }
  68.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-2-12 {
  0% {
    opacity: 0.25;
  }
  8.34333% {
    opacity: 0.25;
  }
  8.35333% {
    opacity: 1;
  }
  68.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-3-12 {
  0% {
    opacity: 0.25;
  }
  16.6767% {
    opacity: 0.25;
  }
  16.6867% {
    opacity: 1;
  }
  76.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-3-12 {
  0% {
    opacity: 0.25;
  }
  16.6767% {
    opacity: 0.25;
  }
  16.6867% {
    opacity: 1;
  }
  76.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-4-12 {
  0% {
    opacity: 0.25;
  }
  25.01% {
    opacity: 0.25;
  }
  25.02% {
    opacity: 1;
  }
  85.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-4-12 {
  0% {
    opacity: 0.25;
  }
  25.01% {
    opacity: 0.25;
  }
  25.02% {
    opacity: 1;
  }
  85.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-5-12 {
  0% {
    opacity: 0.25;
  }
  33.3433% {
    opacity: 0.25;
  }
  33.3533% {
    opacity: 1;
  }
  93.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@keyframes cp-spinner-anim-5-12 {
  0% {
    opacity: 0.25;
  }
  33.3433% {
    opacity: 0.25;
  }
  33.3533% {
    opacity: 1;
  }
  93.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.25;
  }
}
@-webkit-keyframes cp-spinner-anim-6-12 {
  0% {
    opacity: 0.270958;
  }
  41.6767% {
    opacity: 0.25;
  }
  41.6867% {
    opacity: 1;
  }
  1.67667% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.270958;
  }
}
@keyframes cp-spinner-anim-6-12 {
  0% {
    opacity: 0.270958;
  }
  41.6767% {
    opacity: 0.25;
  }
  41.6867% {
    opacity: 1;
  }
  1.67667% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.270958;
  }
}
@-webkit-keyframes cp-spinner-anim-7-12 {
  0% {
    opacity: 0.375125;
  }
  50.01% {
    opacity: 0.25;
  }
  50.02% {
    opacity: 1;
  }
  10.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.375125;
  }
}
@keyframes cp-spinner-anim-7-12 {
  0% {
    opacity: 0.375125;
  }
  50.01% {
    opacity: 0.25;
  }
  50.02% {
    opacity: 1;
  }
  10.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.375125;
  }
}
@-webkit-keyframes cp-spinner-anim-8-12 {
  0% {
    opacity: 0.479292;
  }
  58.3433% {
    opacity: 0.25;
  }
  58.3533% {
    opacity: 1;
  }
  18.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.479292;
  }
}
@keyframes cp-spinner-anim-8-12 {
  0% {
    opacity: 0.479292;
  }
  58.3433% {
    opacity: 0.25;
  }
  58.3533% {
    opacity: 1;
  }
  18.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.479292;
  }
}
@-webkit-keyframes cp-spinner-anim-9-12 {
  0% {
    opacity: 0.583458;
  }
  66.6767% {
    opacity: 0.25;
  }
  66.6867% {
    opacity: 1;
  }
  26.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.583458;
  }
}
@keyframes cp-spinner-anim-9-12 {
  0% {
    opacity: 0.583458;
  }
  66.6767% {
    opacity: 0.25;
  }
  66.6867% {
    opacity: 1;
  }
  26.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.583458;
  }
}
@-webkit-keyframes cp-spinner-anim-10-12 {
  0% {
    opacity: 0.687625;
  }
  75.01% {
    opacity: 0.25;
  }
  75.02% {
    opacity: 1;
  }
  35.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.687625;
  }
}
@keyframes cp-spinner-anim-10-12 {
  0% {
    opacity: 0.687625;
  }
  75.01% {
    opacity: 0.25;
  }
  75.02% {
    opacity: 1;
  }
  35.01% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.687625;
  }
}
@-webkit-keyframes cp-spinner-anim-11-12 {
  0% {
    opacity: 0.791792;
  }
  83.3433% {
    opacity: 0.25;
  }
  83.3533% {
    opacity: 1;
  }
  43.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.791792;
  }
}
@keyframes cp-spinner-anim-11-12 {
  0% {
    opacity: 0.791792;
  }
  83.3433% {
    opacity: 0.25;
  }
  83.3533% {
    opacity: 1;
  }
  43.3433% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.791792;
  }
}
@-webkit-keyframes cp-spinner-anim-12-12 {
  0% {
    opacity: 0.895958;
  }
  91.6767% {
    opacity: 0.25;
  }
  91.6867% {
    opacity: 1;
  }
  51.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.895958;
  }
}
@keyframes cp-spinner-anim-12-12 {
  0% {
    opacity: 0.895958;
  }
  91.6767% {
    opacity: 0.25;
  }
  91.6867% {
    opacity: 1;
  }
  51.6767% {
    opacity: 0.25;
  }
  100% {
    opacity: 0.895958;
  }
}
</style><style type="text/css">.cp-unknown-file-type-view-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.cp-unknown-file-type-view {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  min-width: 490px;
  padding: 35px 100px;
  line-height: 2em;
  border-radius: 5px;
}
.cp-unknown-file-type-view .file-icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}
.cp-unknown-file-type-view p {
  margin: 0;
  color: #fff;
}
.cp-unknown-file-type-view a.download-button {
  margin: 20px 0 0 0;
}
.cp-unknown-file-type-view a.download-button span.icon-download {
  position: relative;
  top: 3px;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48dGl0bGU+VW50aXRsZWQ8L3RpdGxlPjxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPjxwYXRoIGQ9Ik0xIDEydi0xaDEwdjFIMXptNS4yLTNoLS4zOUwxIDQuMTZsMS4xMi0xLjEzTDUgNS45MlYxLjVDNS4wOS41IDUuNDUgMCA2IDBzLjg5LjM3IDEgMS41djQuNDFsMi45MS0yLjg1TDExIDQuMTYgNi4yIDl6IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=");
  background-repeat: none;
  background-position: 0 0;
}
</style><style type="text/css">.cp-thumbnail-img-container.has-thumbnail {
  background-image: none !important;
}
.cp-sink.expanded .cp-thumbnails {
  display: block;
}
.cp-info {
  color: #fff;
  display: inline-block;
  width: 100%;
  font-size: 0.85em;
  height: 40px;
  line-height: 40px;
  text-decoration: none;
  vertical-align: middle;
}
.cp-info .cp-files-label {
  display: block;
  padding-left: 20px;
}
.cp-info .cp-files-label:focus {
  outline: none;
  background-color: #707070;
}
.cp-info .cp-files-label:active {
  background-color: #000;
}
.cp-files-collapser {
  margin-left: 5px;
}
.cp-files-collapser:after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  background-size: 12px;
  background-repeat: no-repeat;
  background-position: 0 0;
  position: relative;
  top: 2px;
  left: 5px;
  background-image: url("data:image/svg+xml;base64,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");
}
.cp-files-collapser.down:after {
  transform: rotate(180deg);
}
.cp-thumbnails {
  background-color: #333;
  display: none;
  height: 130px;
  margin-top: 0;
  padding-bottom: 20px;
  padding-left: 0;
  padding-top: 10px;
  overflow-y: auto;
  overflow-x: hidden;
}
.cp-thumbnail {
  color: #fff;
  display: inline-block;
  text-align: center;
  padding-left: 10px;
  max-width: 145px;
}
.cp-thumbnail.selected .cp-thumbnail-img {
  border: 2px solid #3572B0;
  border-radius: 3px;
}
.cp-thumbnail-group {
  margin: 0;
}
.cp-thumbnail-img-container {
  display: block;
  height: 60px;
  overflow: hidden;
  background-size: contain;
  background: no-repeat center center;
  background-size: 48px 48px;
}
.cp-thumbnail-img {
  background-color: #444;
  border: 2px solid #333;
  width: 100px;
  margin: 10px auto 0 auto;
  padding: 10px 20px;
}
.cp-thumbnail-img:hover {
  background-color: #555;
}
.cp-thumbnail-title {
  color: #f5f5f5;
  font-size: 12px;
  margin-top: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.cp-thumbnail-img img {
  height: 60px;
}
</style><script type="text/javascript" charset="utf-8" async="" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/mediaviewer-1.js.下载"></script><style type="text/css">.cp-image-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
  white-space: nowrap;
  box-sizing: border-box;
}
.cp-image-preview .cp-scale-info {
  position: absolute;
  width: 100px;
  height: 40px;
  top: 50%;
  left: 50%;
  margin-left: -50px;
  margin-top: -20px;
  border-radius: 5px;
  background: #333333;
  background: rgba(38, 38, 38, 0.5);
  line-height: 40px;
  color: #fff;
  z-index: 1;
  display: none;
}
.cp-image-preview .cp-image-container.cp-fit-width {
  width: 100%;
  height: auto;
}
.cp-image-preview .cp-image-container.cp-fit-height {
  width: auto;
  height: 100%;
}
.cp-image-preview img {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKAQMAAAC3/F3+AAAACXBIWXMAAAsTAAALEwEAmpwYAAAABlBMVEXf39////8zI3BgAAAAEUlEQVQIW2Nk38mIjH5wICMAez4Iyz2C/F8AAAAASUVORK5CYII=") repeat;
  /* vertical-align prevents extra space from being added between
           the top of the image and it's container */
  vertical-align: middle;
}
.cp-image-preview img.pixelate {
  /* Prevent images from being smoothed when scaled up */
  image-rendering: optimizeSpeed;
  /* Legal fallback */
  image-rendering: -moz-crisp-edges;
  /* Firefox        */
  image-rendering: -o-crisp-edges;
  /* Opera          */
  image-rendering: -webkit-optimize-contrast;
  /* Safari         */
  image-rendering: optimize-contrast;
  /* CSS3 Proposed  */
  image-rendering: crisp-edges;
  /* CSS4 Proposed  */
  image-rendering: pixelated;
  /* CSS4 Proposed  */
  -ms-interpolation-mode: nearest-neighbor;
  /* IE8+           */
}
.cp-img.pannable {
  cursor: pointer;
  cursor: -moz-grab;
  cursor: -webkit-grab;
  cursor: grab;
}
.cp-img.pannable.panning {
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing;
}
.cp-image-container {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
</style></head>

    
<body id="com-atlassian-confluence" class="theme-default  aui-layout aui-theme-default synchrony-active" data-aui-version="7.9.7">

        
            <div id="stp-licenseStatus-banner"></div>
    <ul id="assistive-skip-links" class="assistive">
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#title-heading">转至内容</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#breadcrumbs">转至导航栏</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#header-menu-bar">转至主菜单</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#navigation">转至动作菜单</a></li>
    <li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#quick-search-query">转至快速搜索</a></li>
</ul>
<!-- Render the dialog --><section role="dialog" id="page-view-tracker-dialog" class="aui-layer aui-dialog2" style="width:820px;" data-aui-modal="true" aria-hidden="true"><!-- Dialog header --><header class="aui-dialog2-header"><!-- The dialogs title --><h2 class="aui-dialog2-header-main">Page View Statistics</h2></header><!-- Main dialog content --><div class="aui-dialog2-content" id="page-view-tracker-content" style="height:400px; overflow-y: scroll;"></div><!-- Dialog footer --><footer class="aui-dialog2-footer"><!-- Actions to render on the right of the footer --><div class="aui-dialog2-footer-actions"><button id="page-view-tracker-help" class="aui-button aui-button-link">Help</button><button id="page-view-tracker-close" class="aui-button aui-button-link">Close</button></div></footer></section><div id="page">
<div id="full-height-container"><ul id="messageContainer"></ul>
    <div id="header-precursor">
        <div class="cell">
            
                            </div>
    </div>
        





<header id="header" role="banner" class="fixed-header">
    <nav class="aui-header aui-dropdown2-trigger-group" role="navigation" data-aui-responsive="true"><div class="aui-header-inner"><div class="aui-header-primary"><h1 id="logo" class="aui-header-logo aui-header-logo-custom"><a href="http://wiki.timevale.cn:8081/"><img src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/atl.site.logo" alt="天谷百科" data-aui-responsive-header-index="0"><span class="aui-header-logo-text">天谷百科</span></a></h1><ul class="aui-nav" resolved="" style="width: auto;">
                            <li>
            
        
        
<a id="space-menu-link" class=" aui-dropdown2-trigger aui-nav-link" aria-controls="space-menu-link-content" aria-haspopup="true" role="button" title="空间" tabindex="0" data-aui-trigger="" resolved="" aria-expanded="false" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#space-menu-link-content">空间<span class="icon aui-icon-dropdown"></span></a><div id="space-menu-link-content" class="aui-dropdown2 aui-style-default aui-dropdown2-in-header aui-layer" role="menu" aria-hidden="true" resolved=""><div role="application"></div></div>
        </li>
                    <li>
            
    
        
<a id="people-directory-link" href="http://wiki.timevale.cn:8081/browsepeople.action" class=" aui-nav-imagelink" title="人员">
            <span>人员</span>
    </a>
        </li>
                                        <li class="aui-buttons">
                                    
                                        <a href="http://wiki.timevale.cn:8081/pages/createpage.action?spaceKey=productreq&amp;fromPageId=*********&amp;src=quick-create" id="quick-create-page-button" class="aui-button aui-button-primary" title="创建空白页 (c)" tabindex="0">创建</a>
                                <a href="http://wiki.timevale.cn:8081/pages/createpage.action?spaceKey=productreq&amp;fromPageId=*********" id="create-page-button" class="aui-button aui-button-primary clc-create-dialog-btn" title="从模板创建" tabindex="0"><span class="aui-icon aui-icon-small aui-iconfont-more">创建 </span></a>
            </li>
    </ul>
</div><div class="aui-header-secondary"><ul class="aui-nav" resolved="">
                        <li>
        <form id="quick-search" class="aui-quicksearch dont-default-focus header-quicksearch" action="http://wiki.timevale.cn:8081/dosearchsite.action" method="get"><fieldset><label for="quick-search-query" class="assistive">快速搜索</label><input id="quick-search-query" class="text app-search search quick-search-query" type="text" accesskey="q" autocomplete="off" name="queryString" title="快速搜索 (g ，g或 /)" placeholder="搜索"><input id="quick-search-submit" class="quick-search-submit" type="submit" value="搜索"><div class="aui-dd-parent quick-nav-drop-down"></div></fieldset></form>
    </li>
        <li>
            
        <a id="help-menu-link" class="aui-nav-link aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-haspopup="true" title="帮助" resolved="" aria-controls="help-menu-link-content" aria-expanded="false">
        <span class="aui-icon aui-icon-small aui-iconfont-question-filled">帮助</span>
    </a>
    <nav id="help-menu-link-content" class="aui-dropdown2 aui-style-default aui-layer" aria-hidden="true" resolved="">
                    <div class="aui-dropdown2-section">
                                <ul id="help-menu-link-leading" class="aui-list-truncate section-leading first">
                                            <li>
        
            
<a id="confluence-help-link" href="https://docs.atlassian.com/confluence/docs-613/" class="    " title="访问Confluence文档首页" target="_blank">
        在线帮助
</a>
</li>
                                            <li>
    
                
<a id="keyboard-shortcuts-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="    " title="查看可用的键盘快捷方式 (?)">
        快捷键
</a>
</li>
                                            <li>
    
            
<a id="feed-builder-link" href="http://wiki.timevale.cn:8081/dashboard/configurerssfeed.action" class="    " title="创建个性化的 RSS源。">
        RSS源建立器
</a>
</li>
                                            <li>
    
            
<a id="whats-new-menu-link" href="https://confluence.atlassian.com/display/DOC/Confluence+6.13+Release+Notes?a=false" class="    " title="">
        新功能
</a>
</li>
                                            <li>
    
                
<a id="gadget-directory-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="   user-item administration-link " title="浏览小工具">
        可用的小工具
</a>
</li>
                                            <li>
    
            
<a id="confluence-about-link" href="http://wiki.timevale.cn:8081/aboutconfluencepage.action" class="    " title="获取关于Confluence的更多信息">
        关于Confluence
</a>
</li>
                                            <li>
    
            
<a id="index.section.link" href="https://dragonsoft.atlassian.net/wiki/spaces/DAS/pages/7078120" class="    " title="">
        水印插件 - 用户手册
</a>
</li>
                                    </ul>
            </div>
            </nav>
    
    </li>
        <li>
                
    
    </li>
        <li>
                    
        
            
<a id="notifications-anchor" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="mw-anchor read aui-nav-imagelink" title="打开通知 (g ，n)"><div class="badge-i aui-icon aui-icon-small aui-iconfont-notification"></div><span class="badge-w"><span class="badge">0</span></span></a>
    
    </li>
        <li>
                                            
        <a id="user-menu-link" class="aui-dropdown2-trigger aui-dropdown2-trigger-arrowless " aria-haspopup="true" data-username="tengqing" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" title="誊青" resolved="" aria-controls="user-menu-link-content" aria-expanded="false">
                    <div class="aui-avatar aui-avatar-small">
                <div class="aui-avatar-inner">
                                                                                                    <img src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/user-avatar">
                </div>
            </div>
            <span class="aui-icon-dropdown"></span>
        </a>
        <nav id="user-menu-link-content" class="aui-dropdown2 aui-style-default aui-layer" aria-hidden="true" resolved="">
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-preferences" class="aui-list-truncate section-user-preferences first">
                                                    <li>
        
            
<a id="view-personal-space-link" href="http://wiki.timevale.cn:8081/spaces/viewspace.action?key=~tengqing" class="   user-item personal-space " title="">
        个人空间
</a>
</li>
                                                    <li>
    
            
<a id="view-user-history-link" href="http://wiki.timevale.cn:8081/users/viewuserhistory.action" class="   user-item user-history popup-link " title=" (g ，r)">
        最近浏览
</a>
</li>
                                                    <li>
    
            
<a id="user-recently-worked-on" href="http://wiki.timevale.cn:8081/dashboard.action#recently-worked" class="   user-item " title="">
        最近的工作
</a>
</li>
                                            </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-content" class="aui-list-truncate section-user-content">
                                                    <li>
    
            
<a id="view-user-profile-link" href="http://wiki.timevale.cn:8081/users/viewmyprofile.action" class="   user-item user-profile " title="">
        用户信息
</a>
</li>
                                                    <li>
    
            
<a id="view-mytasks-link" href="http://wiki.timevale.cn:8081/plugins/inlinetasks/mytasks.action" class="   user-item list-user-status " title="">
        任务
</a>
</li>
                                                    <li>
    
            
<a id="user-favourites-link" href="http://wiki.timevale.cn:8081/users/viewmyfavourites.action" class="   user-item " title="">
        收藏夹
</a>
</li>
                                                    <li>
    
            
<a id="user-watches-link" href="http://wiki.timevale.cn:8081/users/viewnotifications.action" class="   user-item " title="">
        关注
</a>
</li>
                                                    <li>
    
            
<a id="user-drafts-link" href="http://wiki.timevale.cn:8081/users/viewmydrafts.action" class="   user-item " title="">
        草稿
</a>
</li>
                                                    <li>
    
            
<a id="user-network-link" href="http://wiki.timevale.cn:8081/users/viewfollow.action?username=tengqing" class="   follow-link " title="">
        网络
</a>
</li>
                                                    <li>
    
            
<a id="user-settings-link" href="http://wiki.timevale.cn:8081/users/viewmysettings.action" class="   user-item " title="">
        设置
</a>
</li>
                                                    <li>
    
            
<a id="upm-requests-link" href="http://wiki.timevale.cn:8081/plugins/servlet/upm/requests?source=header_user" class="    " title="">
        Atlassian Marketplace
</a>
</li>
                                            </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="user-menu-link-user-operations" class="aui-list-truncate section-user-operations">
                                                    <li>
    
            
<a id="logout-link" href="http://wiki.timevale.cn:8081/logout.action" class="   user-item logout-link " title="">
        注销
</a>
</li>
                                            </ul>
                </div>
                    </nav>
                    
    </li>
    </ul>
</div></div><!-- .aui-header-inner--></nav><!-- .aui-header -->
    <br class="clear">
</header>
    

    
    	<div class="ia-splitter">
    		<div class="ia-splitter-left">
    			<div class="ia-fixed-sidebar collapsed" style="width: 55px; visibility: visible; top: 40px; left: 0px;">
                                            
                            <div class="acs-side-bar ia-scrollable-section"><div class="acs-side-bar-space-info tipsy-enabled" data-configure-tooltip="编辑空间详情"><div class="avatar"><div class="space-logo" data-key="productreq" data-name="1.产品中心" data-entity-type="confluence.space"><div class="avatar-img-container"><div class="avatar-img-wrapper"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=42926508&amp;src=sidebar" title="1.产品中心"><img class="avatar-img" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/global.logo" alt="1.产品中心"></a></div></div></div></div><div class="space-information-container"><div class="name"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=42926508&amp;src=sidebar" title="1.产品中心">1.产品中心</a></div><div class="flyout-handle icon aui-icon aui-icon-small aui-iconfont-edit"></div><div class="favourite-space-icon "><button title="收藏空间" id="space-favourite-add" class="space-favourite aui-icon aui-icon-small aui-iconfont-unstar"></button><button class=" space-favourite-hidden  space-favourite aui-icon aui-icon-small aui-iconfont-star" id="space-favourite-remove" title="取消收藏"></button></div></div></div><div class="acs-side-bar-content"><div class="acs-nav-wrapper"><div class="acs-nav" data-has-create-permission="true" data-quick-links-state="null" data-page-tree-state="null" data-nav-type="page-tree"><div class="acs-nav-sections"><div class="main-links-section "><ul class="acs-nav-list"><li class="acs-nav-item wiki current-item" data-collector-key="spacebar-pages"><a class="acs-nav-item-link tipsy-enabled" href="http://wiki.timevale.cn:8081/collector/pages.action?key=productreq&amp;src=sidebar" data-collapsed-tooltip="页面"><span class="icon"></span><span class="acs-nav-item-label">页面</span></a></li><li class="acs-nav-item blog" data-collector-key="spacebar-blogs"><a class="acs-nav-item-link tipsy-enabled" href="http://wiki.timevale.cn:8081/pages/viewrecentblogposts.action?key=productreq&amp;src=sidebar" data-collapsed-tooltip="博文"><span class="icon"></span><span class="acs-nav-item-label">博文</span></a></li></ul></div><div class="quick-links-wrapper"><h5 class="ia-quick-links-header-title">空间快捷链接</h5><div class="quick-links-section tipsy-enabled " data-collapsed-tooltip="空间快捷链接"><ul class="acs-nav-list"><li class="acs-nav-item pinned_page blueprint file-list"><a class="acs-nav-item-link tipsy-enabled" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113880319&amp;src=spaceshortcut" data-collapsed-tooltip="null"><span class="icon"></span><span class="acs-nav-item-label">文件列表</span></a></li><li class="acs-nav-item pinned_page blueprint requirements"><a class="acs-nav-item-link tipsy-enabled" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126334774&amp;src=spaceshortcut" data-collapsed-tooltip="null"><span class="icon"></span><span class="acs-nav-item-label">产品需求</span></a></li></ul></div></div></div></div></div><div class="ia-secondary-container tipsy-enabled" data-tree-type="page-tree"><div class="ia-secondary-header"><h5 class="ia-secondary-header-title page-tree"><span class="icon"></span><span class="label">页面树结构</span></h5></div><div class="ia-secondary-content">


<div class="plugin_pagetree conf-macro output-inline" data-hasbody="false" data-macro-name="pagetree">

        
        
    <ul class="plugin_pagetree_children_list plugin_pagetree_children_list_noleftspace">
        <div class="plugin_pagetree_children" id="children42926508-0">
<ul class="plugin_pagetree_children_list" id="child_ul42926508-0">
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157749882-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157749882-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157749882&amp;src=contextnavpagetreemode">1、产品中心介绍</a>
        </span>
            </div>

        <div id="children157749882-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157749898-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157749898-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157749898&amp;src=contextnavpagetreemode">2、产品中心流程与制度</a>
        </span>
            </div>

        <div id="children157749898-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157749904-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157749904&amp;src=contextnavpagetreemode">3、产品中心新手入门</a>
        </span>
            </div>

        <div id="children157749904-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157749907-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157749907-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157749907&amp;src=contextnavpagetreemode">4、客户拜访记录</a>
        </span>
            </div>

        <div id="children157749907-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157749910-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157749910-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157749910&amp;src=contextnavpagetreemode">5、竞对分析</a>
        </span>
            </div>

        <div id="children157749910-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus118578306-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan118578306-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=118578306&amp;src=contextnavpagetreemode">【公有云】-产品管理制度</a>
        </span>
            </div>

        <div id="children118578306-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus140145673-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan140145673-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=140145673&amp;src=contextnavpagetreemode">【公有云】-SaaS</a>
        </span>
            </div>

        <div id="children140145673-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul140145673-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus159946573-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159946573-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946573&amp;src=contextnavpagetreemode">01、产品规划</a>
        </span>
            </div>

        <div id="children159946573-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus159946577-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159946577-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946577&amp;src=contextnavpagetreemode">02、运营专题</a>
        </span>
            </div>

        <div id="children159946577-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus159946580-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159946580-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946580&amp;src=contextnavpagetreemode">03、迭代需求</a>
        </span>
            </div>

        <div id="children159946580-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul159946580-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus210144582-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210144582-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210144582&amp;src=contextnavpagetreemode">2025年迭代</a>
        </span>
            </div>

        <div id="children210144582-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul210144582-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210152872-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210152872&amp;src=contextnavpagetreemode">25年迭代模板(含技改)</a>
        </span>
            </div>

        <div id="children210152872-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210144586-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210144586&amp;src=contextnavpagetreemode">0102迭代</a>
        </span>
            </div>

        <div id="children210144586-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210159109-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210159109&amp;src=contextnavpagetreemode">0123技改内容</a>
        </span>
            </div>

        <div id="children210159109-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210155811-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210155811&amp;src=contextnavpagetreemode">0123迭代</a>
        </span>
            </div>

        <div id="children210155811-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210165729-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210165729&amp;src=contextnavpagetreemode">0227技改内容</a>
        </span>
            </div>

        <div id="children210165729-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210165945-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210165945&amp;src=contextnavpagetreemode">0227迭代</a>
        </span>
            </div>

        <div id="children210165945-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214534550-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214534550&amp;src=contextnavpagetreemode">0313技改内容</a>
        </span>
            </div>

        <div id="children214534550-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan210169686-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210169686&amp;src=contextnavpagetreemode">0313迭代</a>
        </span>
            </div>

        <div id="children210169686-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214544200-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214544200&amp;src=contextnavpagetreemode">0327技改内容</a>
        </span>
            </div>

        <div id="children214544200-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214544579-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214544579&amp;src=contextnavpagetreemode">0327迭代</a>
        </span>
            </div>

        <div id="children214544579-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214552498-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214552498&amp;src=contextnavpagetreemode">0410技改内容</a>
        </span>
            </div>

        <div id="children214552498-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus214549031-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214549031-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214549031&amp;src=contextnavpagetreemode">0410迭代</a>
        </span>
            </div>

        <div id="children214549031-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214558674-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214558674&amp;src=contextnavpagetreemode">0424迭代</a>
        </span>
            </div>

        <div id="children214558674-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus214562046-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan214562046-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=214562046&amp;src=contextnavpagetreemode">0515迭代</a>
        </span>
            </div>

        <div id="children214562046-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217651898-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217651898&amp;src=contextnavpagetreemode">0529迭代</a>
        </span>
            </div>

        <div id="children217651898-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus217658203-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217658203-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217658203&amp;src=contextnavpagetreemode">0612迭代-</a>
        </span>
            </div>

        <div id="children217658203-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217663765-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217663765&amp;src=contextnavpagetreemode">0626迭代-</a>
        </span>
            </div>

        <div id="children217663765-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217669827-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217669827&amp;src=contextnavpagetreemode">0710迭代</a>
        </span>
            </div>

        <div id="children217669827-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus217674377-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-down" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217674377-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217674377&amp;src=contextnavpagetreemode">0724迭代</a>
        </span>
            </div>

        <div id="children217674377-0" class="plugin_pagetree_children_container">
                                                        <ul class="plugin_pagetree_children_list" id="child_ul217674377-0">
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span plugin_pagetree_current" id="childrenspan*********-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********&amp;src=contextnavpagetreemode">SaaS及API支持FDA电子签名</a>
        </span>
            </div>

        <div id="children*********-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221515371-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221515371&amp;src=contextnavpagetreemode">0807迭代</a>
        </span>
            </div>

        <div id="children221515371-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221525034-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221525034&amp;src=contextnavpagetreemode">0821迭代</a>
        </span>
            </div>

        <div id="children221525034-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus190247475-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan190247475-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=190247475&amp;src=contextnavpagetreemode">2024年迭代</a>
        </span>
            </div>

        <div id="children190247475-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus169182538-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan169182538-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=169182538&amp;src=contextnavpagetreemode">2023年迭代</a>
        </span>
            </div>

        <div id="children169182538-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus175643957-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan175643957-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=175643957&amp;src=contextnavpagetreemode">2022年迭代（归档）</a>
        </span>
            </div>

        <div id="children175643957-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus184087382-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan184087382-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=184087382&amp;src=contextnavpagetreemode">2023钉钉迭代</a>
        </span>
            </div>

        <div id="children184087382-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus192558782-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan192558782-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=192558782&amp;src=contextnavpagetreemode">2024钉钉迭代</a>
        </span>
            </div>

        <div id="children192558782-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus159946584-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159946584-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946584&amp;src=contextnavpagetreemode">04、重点项目</a>
        </span>
            </div>

        <div id="children159946584-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus159946589-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159946589-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946589&amp;src=contextnavpagetreemode">05、数据分析</a>
        </span>
            </div>

        <div id="children159946589-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus159946592-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159946592-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946592&amp;src=contextnavpagetreemode">06、客户调研专题</a>
        </span>
            </div>

        <div id="children159946592-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus159946659-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159946659-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946659&amp;src=contextnavpagetreemode">【各类日常梳理资料】</a>
        </span>
            </div>

        <div id="children159946659-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus142710615-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan142710615-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=142710615&amp;src=contextnavpagetreemode">移动端</a>
        </span>
            </div>

        <div id="children142710615-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus145142307-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan145142307-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145142307&amp;src=contextnavpagetreemode">【官网】</a>
        </span>
            </div>

        <div id="children145142307-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus190225381-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan190225381-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=190225381&amp;src=contextnavpagetreemode">【电子借条】</a>
        </span>
            </div>

        <div id="children190225381-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan217677556-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217677556&amp;src=contextnavpagetreemode">合同解约统一方案</a>
        </span>
            </div>

        <div id="children217677556-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan221513291-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=221513291&amp;src=contextnavpagetreemode">公有云支持OFD签署</a>
        </span>
            </div>

        <div id="children221513291-0" class="plugin_pagetree_children_container">
            </div>
    </li>
                                        </ul>
                                        </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus192545362-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan192545362-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=192545362&amp;src=contextnavpagetreemode">【公有云】-海外签</a>
        </span>
            </div>

        <div id="children192545362-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus192558786-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan192558786-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=192558786&amp;src=contextnavpagetreemode">【公有云】-专属云</a>
        </span>
            </div>

        <div id="children192558786-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157751480-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157751480-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157751480&amp;src=contextnavpagetreemode">【公有云】-开放平台</a>
        </span>
            </div>

        <div id="children157751480-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157750906-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157750906-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157750906&amp;src=contextnavpagetreemode">【公有云】-基础能力</a>
        </span>
            </div>

        <div id="children157750906-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus199107361-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan199107361-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=199107361&amp;src=contextnavpagetreemode">【国际站】eSignGlobal</a>
        </span>
            </div>

        <div id="children199107361-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus113880764-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan113880764-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=113880764&amp;src=contextnavpagetreemode">【混合云】产品运营</a>
        </span>
            </div>

        <div id="children113880764-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus126333999-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan126333999-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=126333999&amp;src=contextnavpagetreemode">【混合云】天印产品</a>
        </span>
            </div>

        <div id="children126333999-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus180989758-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan180989758-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=180989758&amp;src=contextnavpagetreemode">【混合云】数政产品</a>
        </span>
            </div>

        <div id="children180989758-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus122522682-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan122522682-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=122522682&amp;src=contextnavpagetreemode">【混合云】医签宝</a>
        </span>
            </div>

        <div id="children122522682-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus184068243-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan184068243-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=184068243&amp;src=contextnavpagetreemode">【ePaaS】基础能力</a>
        </span>
            </div>

        <div id="children184068243-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus145142294-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan145142294-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145142294&amp;src=contextnavpagetreemode">【ePaaS】低代码平台</a>
        </span>
            </div>

        <div id="children145142294-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus177842860-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan177842860-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=177842860&amp;src=contextnavpagetreemode">【ePaaS】全链路交付</a>
        </span>
            </div>

        <div id="children177842860-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus142707815-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan142707815-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=142707815&amp;src=contextnavpagetreemode">【超阅】产品</a>
        </span>
            </div>

        <div id="children142707815-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus145142274-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan145142274-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145142274&amp;src=contextnavpagetreemode">【平台产品部】</a>
        </span>
            </div>

        <div id="children145142274-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157750984-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157750984-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157750984&amp;src=contextnavpagetreemode">【UED】</a>
        </span>
            </div>

        <div id="children157750984-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus157751033-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan157751033-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=157751033&amp;src=contextnavpagetreemode">历史资料</a>
        </span>
            </div>

        <div id="children157751033-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                    <span class="no-children icon"></span>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan159969778-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159969778&amp;src=contextnavpagetreemode">官网落地页优化项目数据校准排查</a>
        </span>
            </div>

        <div id="children159969778-0" class="plugin_pagetree_children_container">
            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus124674046-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan124674046-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=124674046&amp;src=contextnavpagetreemode">【公有云】-招投标产品</a>
        </span>
            </div>

        <div id="children124674046-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
            <li>
    <div class="plugin_pagetree_childtoggle_container">
                                            <a id="plusminus175652654-0" class="plugin_pagetree_childtoggle aui-icon aui-icon-small aui-iconfont-chevron-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">
            </a>
            </div>
    <div class="plugin_pagetree_children_content">
          
        
                    <span class="plugin_pagetree_children_span" id="childrenspan175652654-0">                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=175652654&amp;src=contextnavpagetreemode">【流版签】-统一客户端</a>
        </span>
            </div>

        <div id="children175652654-0" class="plugin_pagetree_children_container">
                                                            </div>
    </li>
    </ul>
</div>
    </ul>

    <fieldset class="hidden">
        <input type="hidden" name="treeId" value="">
        <input type="hidden" name="treeRequestId" value="/plugins/pagetree/naturalchildren.action?decorator=none&amp;excerpt=false&amp;sort=position&amp;reverse=false&amp;disableLinks=false&amp;expandCurrent=true">
        <input type="hidden" name="treePageId" value="*********">

        <input type="hidden" name="noRoot" value="false">
        <input type="hidden" name="rootPageId" value="42926508">

        <input type="hidden" name="rootPage" value="">
        <input type="hidden" name="startDepth" value="0">
        <input type="hidden" name="spaceKey" value="productreq">

        <input type="hidden" name="i18n-pagetree.loading" value="载入中...">
        <input type="hidden" name="i18n-pagetree.error.permission" value="无法载入页面树。看来你没有权限查看根页面。">
        <input type="hidden" name="i18n-pagetree.eeror.general" value="检索页面树时发生问题。有关详细信息，请检查服务器的日志文件。">
        <input type="hidden" name="loginUrl" value="/login.action?os_destination=%2Fpages%2Fviewpage.action%3FpageId%3D*********&amp;permissionViolation=true">
        <input type="hidden" name="mobile" value="false">

                <fieldset class="hidden">
                                                <input type="hidden" name="ancestorId" value="217674377">
                                    <input type="hidden" name="ancestorId" value="210144582">
                                    <input type="hidden" name="ancestorId" value="159946580">
                                    <input type="hidden" name="ancestorId" value="140145673">
                                    <input type="hidden" name="ancestorId" value="42926508">
                                    </fieldset>
    </fieldset>
</div>
</div></div></div><div class="hidden"><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=productreq&amp;src=sidebar" id="space-pages-link" title=" (g ，s)"></a><script type="text/x-template" title="logo-config-content"><h2>空间详情</h2><div class="personal-space-logo-hint">您的个人头像被作为您的个人空间的logo使用。<a href="/users/profile/editmyprofilepicture.action" target="_blank">更改您的个人头像</a>.</div></script></div></div><div class="space-tools-section"><div id="space-tools-menu-additional-items" class="hidden"><div data-label="重排页面" data-class="" data-href="/pages/reorderpages.action?key=productreq">重排页面</div></div><button id="space-tools-menu-trigger" class=" aui-dropdown2-trigger aui-button aui-button-subtle tipsy-enabled aui-dropdown2-trigger-arrowless " aria-controls="space-tools-menu" aria-haspopup="true" role="button" data-aui-trigger="" resolved="" aria-expanded="false" data-collapsed-tooltip="空间管理"><span class="aui-icon aui-icon-small aui-iconfont-configure">设置</span><span class="aui-button-label">空间管理</span><span class="icon aui-icon-dropdown"></span></button><div id="space-tools-menu" class="aui-dropdown2 aui-style-default space-tools-dropdown aui-layer" role="menu" aria-hidden="true" data-aui-alignment="top left" resolved=""><div role="presentation" class="aui-dropdown2-section space-tools-navigation"><div role="group"><ul class="aui-list-truncate" role="presentation"><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/spaces/viewspacesummary.action?key=productreq&amp;src=spacetools">概览</a></li><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=productreq&amp;src=spacetools">内容工具</a></li></ul></div></div><div role="presentation" class="aui-dropdown2-section space-operations"><div role="group"><ul class="aui-list-truncate" role="presentation"><li role="presentation"><a role="menuitem" tabindex="-1" class="" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=productreq&amp;src=spacetools">重排页面</a></li></ul></div></div></div><a class="expand-collapse-trigger aui-icon aui-icon-small aui-iconfont-chevron-double-right" data-tooltip="展开侧边栏 ( [ )" original-title=""></a></div>
                    
                        			<div class="ia-splitter-handle tipsy-enabled" data-tooltip="展开侧边栏 ( [ )" original-title=" ([)"><div class="ia-splitter-handle-highlight confluence-icon-grab-handle"></div></div></div>
    		</div>
        <!-- \#header -->

            
    
        <div id="main" class=" aui-page-panel" style="margin-left: 55px; margin-top: 40px;">
                        <div id="main-header-placeholder" style="margin: -10px 0px 20px; padding: 0px; height: 70px;"><h1 class="with-breadcrumbs" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-size: 28px; font-style: normal; font-weight: 500; text-decoration: none solid rgb(23, 43, 77); letter-spacing: -0.28px; text-align: start; padding: 35px 0px 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px;">
                                                <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" style="font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-size: 28px; font-style: normal; font-weight: 500; margin: 0px; padding: 0px; color: rgb(23, 43, 77); text-decoration: none solid rgb(23, 43, 77); letter-spacing: -0.28px; text-align: start;">SaaS及API支持FDA电子签名</a>
                                    </h1></div><div id="main-header" style="top: 40px; width: 1770px; position: fixed; right: 0px; margin-top: 0px; padding: 10px 40px; z-index: 100;" class="overlay-header">
                        
    <div id="navigation" class="content-navigation view">
                    <ul class="ajs-menu-bar">
                                                                    <li class="ajs-button normal">

        
        
                                            
    
    
    <a id="editPageLink" href="http://wiki.timevale.cn:8081/pages/editpage.action?pageId=*********" rel="nofollow" class="aui-button aui-button-subtle edit" accesskey="e" title="编辑 (e)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-edit"></span>
                        <u>E</u>编辑
        </span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="page-favourite" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle action-page-favourite" accesskey="f" title="收藏 (f)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-unstar"></span>
                        <u>F</u>收藏
        </span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="watch-content-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle watch-menu watch-state-initialised" title="关注(w)">
                <span><span class="aui-icon aui-icon-small aui-iconfont-unwatch"></span> <u> </u> 观看</span>    </a>
</li>
                                            <li class="ajs-button normal">

        
            
                                            
    
    
    <a id="shareContentLink" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="aui-button aui-button-subtle share" title="Share this page with others (s或 k)">
                <span>
                            <span class="aui-icon aui-icon-small aui-iconfont-share"></span>
                        <u>S</u>分享
        </span>    </a>
</li>
                    
        <li class="normal ajs-menu-item">
        <a id="action-menu-link" class="action aui-dropdown2-trigger-arrowless aui-button aui-button-subtle ajs-menu-title aui-dropdown2-trigger aui-alignment-target aui-alignment-element-attached-top aui-alignment-element-attached-right aui-alignment-target-attached-bottom aui-alignment-target-attached-right" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-haspopup="true" data-container="#navigation" resolved="" aria-controls="action-menu" aria-expanded="false">
            <span>
                                    <span class="aui-icon aui-icon-small aui-iconfont-more"></span>
                                
            </span>
        </a>         
    <div id="action-menu" class="aui-dropdown2 aui-style-default aui-layer most-right-menu-item aui-alignment-element aui-alignment-side-bottom aui-alignment-snap-right aui-alignment-element-attached-top aui-alignment-element-attached-right aui-alignment-target-attached-bottom aui-alignment-target-attached-right" aria-hidden="true" resolved="" style="left: 0px; z-index: 1000; top: 0px; position: absolute; transform: translateX(1715px) translateY(4360px) translateZ(0px);" data-aui-alignment="bottom auto" data-aui-alignment-static="true">
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-primary" class="section-primary first">
                                                    <li>

    
        
                                            
    
    
    <a id="view-attachments-link" href="http://wiki.timevale.cn:8081/pages/viewpageattachments.action?pageId=*********" rel="nofollow" class="action-view-attachments" accesskey="t" title="查看附件 (t)" tabindex="-1">
                <span>
                        <u>t</u>附件(25)
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-view-history-link" href="http://wiki.timevale.cn:8081/pages/viewpreviousversions.action?pageId=*********" rel="nofollow" class="action-view-history" title="" tabindex="-1">
                <span>
                        页面历史
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-page-permissions-link" href="http://wiki.timevale.cn:8081/pages/viewinfo.action?pageId=*********" rel="nofollow" class="action-page-permissions" title="编辑限制" tabindex="-1">
                <span>
                        限制
        </span>    </a>
</li>
                                        </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-secondary" class="section-secondary">
                                                    <li>

    
        
                                            
    
    
    <a id="view-page-info-link" href="http://wiki.timevale.cn:8081/pages/viewinfo.action?pageId=*********" rel="nofollow" class="action-view-info" title="" tabindex="-1">
                <span>
                        页面信息
        </span>    </a>
</li>
                                                <li>

    
            
                                            
    
    
    <a id="view-resolved-comments" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" rel="nofollow" class="" title="" tabindex="-1">
                <span>已解决评论 (0)</span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="view-in-hierarchy-link" href="http://wiki.timevale.cn:8081/pages/reorderpages.action?key=productreq&amp;openId=*********#selectedPageInHierarchy" rel="nofollow" class="" title="" tabindex="-1">
                <span>
                        以层级方式查看
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-view-source-link" href="http://wiki.timevale.cn:8081/plugins/viewsource/viewpagesrc.action?pageId=*********" rel="nofollow" class="action-view-source popup-link" title="" tabindex="-1">
                <span>
                        查看页面源代码
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-export-pdf-link" href="http://wiki.timevale.cn:8081/spaces/flyingpdf/pdfpageexport.action?pageId=*********" rel="nofollow" class="" title="" tabindex="-1">
                <span>
                        导出为PDF
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="com-k15t-confluence-scroll-html-launcher" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" rel="nofollow" class="" title="" tabindex="-1">
                <span>
                        Export to HTML
        </span>    <section role="dialog" id="k15t-exp-html-no-export-dialog" class="aui-layer aui-dialog2 aui-dialog2-small aui-dialog2-warning" aria-hidden="true">    <header class="aui-dialog2-header">        <h2 class="aui-dialog2-header-main">Page Not Available</h2>        <a class="aui-dialog2-header-close">            <span class="aui-icon aui-icon-small aui-iconfont-close-dialog">Close</span>        </a>    </header>    <div class="aui-dialog2-content">        <p>You cannot export this page, because it is not available in the current version, variant, or language.</p>    </div>    <footer class="aui-dialog2-footer">        <div class="aui-dialog2-footer-actions">            <button id="k15t-exp-html-no-export-dialog-close-button" class="aui-button aui-button-link">Close</button>        </div>        <div class="aui-dialog2-footer-hint">           <span style="  background: linear-gradient(to right, #1062fb 0, #1062fb 33.3%, #2eb785 33.3%, #2eb785 66.6%, #ffc420 66.6%);  height: .5em;  width: 2.625em;  display: inline-block;  position: relative;           "></span>         </div>    </footer></section></a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-export-word-link" href="http://wiki.timevale.cn:8081/exportword?pageId=*********" rel="nofollow" class="action-export-word" title="" tabindex="-1">
                <span>
                        导出为Word
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="import-word-doc" href="http://wiki.timevale.cn:8081/pages/worddav/uploadimport.action?pageId=*********" rel="nofollow" class="" title="" tabindex="-1">
                <span>
                        Doc文件导入
        </span>    </a>
</li>
                                        </ul>
                </div>
                            <div class="aui-dropdown2-section">
                    <ul id="action-menu-modify" class="section-modify">
                                                    <li>

    
        
                                            
    
    
    <a id="action-copy-page-link" href="http://wiki.timevale.cn:8081/pages/copypage.action?idOfPageToCopy=*********&amp;spaceKey=productreq" rel="nofollow" class="action-copy" title="" tabindex="-1">
                <span>
                        复制
        </span>    </a>
</li>
                                                <li>

    
        
                                            
    
    
    <a id="action-move-page-dialog-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" rel="nofollow" class="action-move" title="" tabindex="-1">
                <span>
                        移动
        </span>    </a>
</li>
                                        </ul>
                </div>
                    </div></li>
            </ul>
    </div>

            
            <div id="title-heading" class="pagetitle with-breadcrumbs">
                
                                    <div id="breadcrumb-section">
                        
    
    
    <ol id="breadcrumbs">
                                        
                        
        <li class="first">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=productreq&amp;src=breadcrumbs-collector">页面</a></span>
                                                                                                            </li><li id="ellipsis" title="显示全部导航项"><span><strong>…</strong></span></li>
                                                
                        
        <li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=42926508&amp;src=breadcrumbs-expanded">产品中心</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=140145673&amp;src=breadcrumbs-expanded">【公有云】-SaaS</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946580&amp;src=breadcrumbs-expanded">03、迭代需求</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210144582&amp;src=breadcrumbs-expanded">2025年迭代</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217674377&amp;src=breadcrumbs-parent">0724迭代</a></span>
                                                                    </li></ol>


                    </div>
                
                
        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-banner-end" class="assistive">跳到banner的尾部</a>
<div id="page-banner-start" class="assistive"></div>

                    
            <div id="page-metadata-banner" style="visibility: visible;"><ul class="banner"><li id="system-content-items" class="noprint"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" title="未限制" id="content-metadata-page-restrictions" class="aui-icon aui-icon-small aui-iconfont-unlocked system-metadata-restrictions" resolved=""></a><a href="http://wiki.timevale.cn:8081/pages/viewpageattachments.action?pageId=*********&amp;metadataLink=true" title="25 个附件" id="content-metadata-attachments" class="aui-icon aui-icon-small aui-iconfont-attachment"></a></li><li class="page-metadata-item noprinthas-button" id="content-metadata-jira-wrapper"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" title="" id="content-metadata-jira" class="aui-button aui-button-subtle content-metadata-jira tipsy-disabled hidden"><span>JIRA 链接</span></a></li></ul></div>
            

<a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-banner-start" class="assistive">回到标题开始</a>
<div id="page-banner-end" class="assistive"></div>
    

                <h1 id="title-text" class="with-breadcrumbs" style="display: none;">
                                                <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********">SaaS及API支持FDA电子签名</a>
                                    </h1>
            </div>
        </div><!-- \#main-header -->
        
        

        <div id="sidebar-container">
                                                </div><!-- \#sidebar-container -->

        
    

        




            
    

                                
    

    
    
        
    
    
                    
    

    

    
            
        

    
    

    
            
        



    
<div id="content" class="page view">
    


<div id="action-messages">
                        </div>



            <script type="text/x-template" title="searchResultsGrid">
    <table class="aui">
        <thead>
            <tr class="header">
                <th class="search-result-title">页面标题</th>
                <th class="search-result-space">空间</th>
                <th class="search-result-date">更新于</th>
            </tr>
        </thead>
    </table>
</script>
<script type="text/x-template" title="searchResultsGridCount">
    <p class="search-result-count">{0}</p>
</script>
<script type="text/x-template" title="searchResultsGridRow">
    <tr class="search-result">
        <td class="search-result-title"><a href="{1}" class="content-type-{2}"><span>{0}</span></a></td>
        <td class="search-result-space"><a class="space" href="/display/{4}/" title="{3}">{3}</a></td>
        <td class="search-result-date"><span class="date" title="{6}">{5}</span></td>
    </tr>
</script>
        
    
            

        
                            
    

                    

        
        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-metadata-end" class="assistive">转至元数据结尾</a>
<div id="page-metadata-start" class="assistive"></div>

    <div class="page-metadata">
        <ul>
            <li class="page-metadata-modification-info">
                
        
    
        
    
        
            
            由<span class="author">     <a href="http://wiki.timevale.cn:8081/display/~feituo" class="url fn confluence-userlink userlink-0" data-username="feituo" title="" data-user-hover-bound="true">吠陀</a></span>创建, 最后修改于<a class="last-modified" title="查看变更" href="http://wiki.timevale.cn:8081/pages/diffpagesbyversion.action?pageId=*********&amp;selectedPageVersions=33&amp;selectedPageVersions=34">七月 21, 2025</a>
                </li>
        </ul>
    </div>


<a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#page-metadata-start" class="assistive">转至元数据起始</a>
<div id="page-metadata-end" class="assistive"></div>

        
                                            
        <div id="main-content" class="wiki-content">
                           
        <p></p><div class="toc-macro client-side-toc-macro  conf-macro output-block hidden-outline" data-cssliststyle="none" data-headerelements="H1,H2,H3,H4,H5,H6,H7" data-hasbody="false" data-macro-name="toc"><ul style=" list-style: none;"><li><span class="toc-item-body" data-outline="1"><span class="toc-outline">1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E4%B8%80%E3%80%81%E6%96%87%E6%A1%A3%E8%AE%B0%E5%BD%95" class="toc-link">一、文档记录</a></span></li><li><span class="toc-item-body" data-outline="2"><span class="toc-outline">2</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E4%BA%8C%E3%80%81%E9%9C%80%E6%B1%82%E6%A6%82%E5%86%B5" class="toc-link">二、需求概况</a></span><ul style=" list-style: none;"><li><span class="toc-item-body" data-outline="2.1"><span class="toc-outline">2.1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E9%9C%80%E6%B1%82%E6%A6%82%E8%BF%B0" class="toc-link">需求概述</a></span></li><li><span class="toc-item-body" data-outline="2.2"><span class="toc-outline">2.2</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E5%9F%BA%E6%9C%AC%E5%AE%9A%E4%B9%89" class="toc-link">基本定义</a></span></li><li><span class="toc-item-body" data-outline="2.3"><span class="toc-outline">2.3</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-21CFRpart11%E7%9A%84%E5%8A%9F%E8%83%BD%E9%A1%B9%E5%8F%82%E8%80%83" class="toc-link">21 CFR part 11 的功能项参考</a></span></li><li><span class="toc-item-body" data-outline="2.4"><span class="toc-outline">2.4</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E7%9B%AE%E6%A0%87%E5%AE%A2%E6%88%B7%E7%94%BB%E5%83%8F" class="toc-link">目标客户画像</a></span></li><li><span class="toc-item-body" data-outline="2.5"><span class="toc-outline">2.5</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E5%90%88%E8%A7%84%E6%80%A7%E8%AF%84%E4%BC%B0%E6%96%B9%E6%A1%88" class="toc-link">合规性评估方案</a></span></li></ul></li><li><span class="toc-item-body" data-outline="3"><span class="toc-outline">3</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E4%B8%89%E3%80%81%E7%AB%9E%E5%93%81%E5%88%86%E6%9E%90" class="toc-link">三、竞品分析</a></span></li><li><span class="toc-item-body" data-outline="4"><span class="toc-outline">4</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E5%9B%9B%E3%80%81%E7%AD%BE%E5%90%8D%E6%A0%B7%E5%BC%8F%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83" class="toc-link">四、签名样式设计规范</a></span></li><li><span class="toc-item-body" data-outline="5"><span class="toc-outline">5</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E4%BA%94%E3%80%81%E9%A1%B9%E7%9B%AE%E8%AE%A1%E5%88%92" class="toc-link">五、项目计划</a></span></li><li><span class="toc-item-body" data-outline="6"><span class="toc-outline">6</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E5%85%AD%E3%80%81%E4%BA%A7%E5%93%81%E8%AE%BE%E8%AE%A1" class="toc-link">六、产品设计</a></span><ul style=" list-style: none;"><li><span class="toc-item-body" data-outline="6.1"><span class="toc-outline">6.1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-6.1%E6%B5%81%E7%A8%8B%E5%9B%BE" class="toc-link">6.1 流程图</a></span></li></ul></li><li><span class="toc-item-body" data-outline="7"><span class="toc-outline">7</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E4%B8%83%E3%80%81%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95" class="toc-link">七、功能清单</a></span></li><li><span class="toc-item-body" data-outline="8"><span class="toc-outline">8</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E5%85%AB%E3%80%81%E5%8A%9F%E8%83%BD%E9%9C%80%E6%B1%82%E6%B8%85%E5%8D%95" class="toc-link">八、功能需求清单</a></span><ul style=" list-style: none;"><li><span class="toc-item-body" data-outline="8.1"><span class="toc-outline">8.1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.1%E5%90%88%E5%90%8C%E5%81%8F%E5%A5%BD%E8%AE%BE%E7%BD%AE-FDA%E7%AD%BE%E5%90%8D%E9%85%8D%E7%BD%AE" class="toc-link">8.1 合同偏好设置-FDA签名配置</a></span></li><li><span class="toc-item-body" data-outline="8.2"><span class="toc-outline">8.2</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.2%E7%9B%B4%E6%8E%A5%E5%8F%91%E8%B5%B7%E3%80%81%E6%A8%A1%E6%9D%BF%E7%BC%96%E8%BE%91%E3%80%81%E6%A8%A1%E6%9D%BF%E5%8F%91%E8%B5%B7%E6%97%B6FDA%E5%90%AF%E7%94%A8" class="toc-link">8.2 直接发起、模板编辑、模板发起时FDA启用</a></span></li><li><span class="toc-item-body" data-outline="8.3"><span class="toc-outline">8.3</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.3%E6%8C%87%E5%AE%9A%E7%AD%BE%E7%BD%B2%E4%BD%8D%E7%BD%AE%E9%A1%B5%E9%9D%A2%E9%85%8D%E7%BD%AE%E5%85%BC%E5%AE%B9" class="toc-link">8.3 指定签署位置页面配置兼容</a></span></li><li><span class="toc-item-body" data-outline="8.4"><span class="toc-outline">8.4</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.4%E7%AD%BE%E7%BD%B2%E9%A1%B5%E9%9D%A2%E9%80%89%E6%8B%A9%E7%AD%BE%E7%BD%B2%E7%90%86%E7%94%B1%E4%BA%A4%E4%BA%92" class="toc-link">8.4 签署页面选择签署理由交互</a></span></li><li><span class="toc-item-body" data-outline="8.5"><span class="toc-outline">8.5</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.5%E7%AD%BE%E7%BD%B2%E5%90%8E%E7%BB%AD%E6%B5%81%E7%A8%8B%E9%80%BB%E8%BE%91" class="toc-link">8.5 签署后续流程逻辑</a></span></li><li><span class="toc-item-body" data-outline="8.6"><span class="toc-outline">8.6</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.6%E6%A8%A1%E6%9D%BF%E6%97%B6%E9%97%B4%E6%94%AF%E6%8C%81%E5%88%B0%E7%A7%92%E7%BA%A7" class="toc-link">8.6 模板时间支持到秒级</a></span></li><li><span class="toc-item-body" data-outline="8.7"><span class="toc-outline">8.7</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.7%E3%80%90V3API%E3%80%91%E5%9F%BA%E4%BA%8E%E6%96%87%E4%BB%B6%E5%8F%91%E8%B5%B7%E7%AD%BE%E7%BD%B2%E6%8E%A5%E5%8F%A3%E5%85%BC%E5%AE%B9" class="toc-link">8.7 【V3API】基于文件发起签署接口兼容</a></span></li><li><span class="toc-item-body" data-outline="8.8"><span class="toc-outline">8.8</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.8%E3%80%90V3API%E3%80%91%E9%80%9A%E8%BF%87%E9%A1%B5%E9%9D%A2%E5%8F%91%E8%B5%B7%E7%AD%BE%E7%BD%B2%E6%8E%A5%E5%8F%A3%E5%85%BC%E5%AE%B9" class="toc-link">8.8 【V3API】通过页面发起签署接口兼容</a></span></li><li><span class="toc-item-body" data-outline="8.9"><span class="toc-outline">8.9</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.9%E3%80%90V3API%E3%80%91%E8%BF%BD%E5%8A%A0&amp;%E5%88%A0%E9%99%A4%E7%AD%BE%E7%BD%B2%E5%8C%BA%E6%8E%A5%E5%8F%A3%E5%85%BC%E5%AE%B9" class="toc-link">8.9 【V3API】追加&amp;删除签署区接口兼容</a></span></li><li><span class="toc-item-body" data-outline="8.10"><span class="toc-outline">8.10</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.10%E3%80%90V3API%E3%80%91%E8%8E%B7%E5%8F%96%E6%89%B9%E9%87%8F%E7%AD%BE%E9%A1%B5%E9%9D%A2%E9%93%BE%E6%8E%A5%EF%BC%88%E5%A4%9A%E6%B5%81%E7%A8%8B%EF%BC%89%E6%8E%A5%E5%8F%A3%E5%85%BC%E5%AE%B9" class="toc-link">8.10 【V3API】获取批量签页面链接（多流程）接口兼容</a></span></li><li><span class="toc-item-body" data-outline="8.11"><span class="toc-outline">8.11</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.11%E3%80%90V3API%E3%80%91%E5%8F%91%E8%B5%B7%E5%90%88%E5%90%8C%E8%A7%A3%E7%BA%A6&amp;%E9%80%9A%E8%BF%87%E9%A1%B5%E9%9D%A2%E5%8F%91%E8%B5%B7%E5%90%88%E5%90%8C%E8%A7%A3%E7%BA%A6%E6%8E%A5%E5%8F%A3%E5%85%BC%E5%AE%B9" class="toc-link">8.11 【V3API】发起合同解约&amp;通过页面发起合同解约接口兼容</a></span></li><li><span class="toc-item-body" data-outline="8.12"><span class="toc-outline">8.12</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.12%E3%80%90V3API%E3%80%91%E8%8E%B7%E5%8F%96%E3%80%8A%E5%88%9B%E5%BB%BA%E6%B5%81%E7%A8%8B%E6%A8%A1%E6%9D%BF%E3%80%8B%E9%A1%B5%E9%9D%A2%E9%93%BE%E6%8E%A5&amp;%E8%8E%B7%E5%8F%96%E3%80%8A%E6%B5%81%E7%A8%8B%E7%BC%96%E8%BE%91%E6%A8%A1%E6%9D%BF%E3%80%8B%E9%A1%B5%E9%9D%A2%E9%93%BE%E6%8E%A5" class="toc-link">8.12 【V3API】获取《创建流程模板》页面链接&amp;获取《流程编辑模板》页面链接</a></span></li><li><span class="toc-item-body" data-outline="8.13"><span class="toc-outline">8.13</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-8.13%E8%BF%90%E8%90%A5%E6%94%AF%E6%92%91%E5%B9%B3%E5%8F%B0%E6%94%AF%E6%8C%81%E5%9C%A8appid%E7%BB%B4%E5%BA%A6%E6%8E%A7%E5%88%B6%E6%98%AF%E5%90%A6%E5%8F%AF%E7%94%A8FDA%E7%AD%BE%E5%90%8D%EF%BC%88%E5%BE%85%E5%AE%9A%EF%BC%89" class="toc-link">8.13 运营支撑平台支持在appid维度控制是否可用FDA签名（待定）</a></span></li></ul></li><li><span class="toc-item-body" data-outline="9"><span class="toc-outline">9</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-%E4%B9%9D%E3%80%81%E9%9D%9E%E5%8A%9F%E8%83%BD%E6%80%A7%E9%9C%80%E6%B1%82" class="toc-link">九、非功能性需求</a></span><ul style=" list-style: none;"><li><span class="toc-item-body" data-outline="9.1"><span class="toc-outline">9.1</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-9.1%E6%95%B0%E6%8D%AE%E7%BB%9F%E8%AE%A1%E9%9C%80%E6%B1%82" class="toc-link">9.1 数据统计需求</a></span></li><li class="toc-empty-item"></li><li><span class="toc-item-body" data-outline="9.3"><span class="toc-outline">9.3</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-9.2%E5%85%B6%E4%BB%96%E9%9D%9E%E5%8A%9F%E8%83%BD%E9%9C%80%E6%B1%82" class="toc-link">9.2 其他非功能需求</a></span></li><li><span class="toc-item-body" data-outline="9.4"><span class="toc-outline">9.4</span><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#SaaS%E5%8F%8AAPI%E6%94%AF%E6%8C%81FDA%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D-9.3%E9%9A%90%E7%A7%81%E9%A3%8E%E9%99%A9%E8%AF%84%E4%BC%B0" class="toc-link">9.3 隐私风险评估</a></span></li></ul></li></ul></div><p></p><h2 id="SaaS及API支持FDA电子签名-一、文档记录">一、文档记录</h2><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 65.7083%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 7.37046%;"><col style="width: 10.4688%;"><col style="width: 72.321%;"><col style="width: 9.83173%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 95px; z-index: 3; width: 1149px; top: 75px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="版本: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">版本</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="修订日期: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">修订日期</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="修订内容: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">修订内容</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="修订人: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">修订人</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="版本: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">版本</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="修订日期: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">修订日期</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="修订内容: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">修订内容</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="修订人: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">修订人</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">V1.0.0</td><td class="confluenceTd">2025.06.20</td><td class="confluenceTd">创建</td><td class="confluenceTd">吠陀</td></tr><tr role="row"><td class="confluenceTd">V1.0.1</td><td class="confluenceTd"><p>2025.07.03</p></td><td class="confluenceTd">补充获取批量签接口校验逻辑；修改发起签署FDA签名相关入参；</td><td class="confluenceTd">吠陀</td></tr><tr role="row"><td class="confluenceTd">V1.0.2</td><td class="confluenceTd">2025.07.18</td><td class="confluenceTd">修改英文样式下签署理由的限制字数</td><td class="confluenceTd">吠陀</td></tr><tr role="row"><td colspan="1" class="confluenceTd">V1.0.3</td><td colspan="1" class="confluenceTd">2025.07.21</td><td colspan="1" class="confluenceTd">补充非epass企业使用FDA签名时的提示</td><td colspan="1" class="confluenceTd">吠陀</td></tr></tbody></table></div><h2 id="SaaS及API支持FDA电子签名-二、需求概况">二、需求概况</h2><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 70.9924%;" resolved=""><colgroup><col style="width: 13.3072%;"><col style="width: 86.6977%;"></colgroup><tbody><tr><th class="confluenceTh"><h3 id="SaaS及API支持FDA电子签名-需求概述">需求概述</h3></th><td class="confluenceTd">在医药行业，若企业生产食品、药品、器材等产品需销往美国等地，在此类产品生产交易活动中所签署的电子合同，需符合美国联邦法规对电子签名的管理要求。因此针对此类企业，e签宝需支持FDA配置化。当配置开启时，满足电子合同签署全流程符合该法规的一系列要求。</td></tr><tr><th class="confluenceTh"><h3 id="SaaS及API支持FDA电子签名-基本定义">基本定义</h3></th><td class="confluenceTd"><p><span>FDA（Food and Drug Administration），食品药品监督管理局（美国），通过FDA认证的食品、药品、化妆品和医疗器具对人体是确保安全而有效的。在美国等近百个国家，只有通过了FDA认可的材料、器械和技术才能进行商业化临床应用。</span></p><p><span>CFR（Code of Federal Regulations），联邦法规（美国），21 CFR Part 11是指《联邦法规21章》第11款，主要内容涉及电子记录和电子签名。实际应用常以符合FDA 21 CFR Part 11 方式表达，此法规确保了电子数据的有效性和可靠性。食品、医药制造行业多遵照此标准。</span></p><p><span>法规原文：<a href="http://wiki.timevale.cn:8081/download/attachments/*********/21%20CFR%20Part%2011%20%28up%20to%20date%20as%20of%205-15-2025%29.pdf?version=1&amp;modificationDate=1750384514000&amp;api=v2" data-linked-resource-id="217672436" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="21 CFR Part 11 (up to date as of 5-15-2025).pdf" data-nice-type="PDF Document" data-linked-resource-content-type="application/pdf" data-linked-resource-container-id="*********" data-linked-resource-container-version="34">21 CFR Part 11 (up to date as of 5-15-2025).pdf</a></span></p><p>法规重点内容中英互译：<a href="http://wiki.timevale.cn:8081/download/attachments/*********/US%20FDA%2021CFR%20part11%EF%BC%9Aelectronic%20record%20and%20electronic%20signature.pdf?version=1&amp;modificationDate=1750384544000&amp;api=v2" data-linked-resource-id="217672437" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="US FDA 21CFR part11：electronic record and electronic signature.pdf" data-nice-type="PDF Document" data-linked-resource-content-type="application/pdf" data-linked-resource-container-id="*********" data-linked-resource-container-version="34">US FDA 21CFR part11：electronic record and electronic signature.pdf</a></p></td></tr><tr><th class="confluenceTh"><h3 id="SaaS及API支持FDA电子签名-21CFRpart11的功能项参考"><span>21 CFR part 11 的功能项参考</span></h3></th><td class="confluenceTd"><p><span style="color: rgb(64,64,64);">一、身份验证与访问控制</span></p><ol><li><p><span style="color: rgb(64,64,64);">多因素身份认证（MFA）</span></p><ul><li><span style="color: rgb(64,64,64);">支持密码+动态验证码（短信/邮箱）、生物识别（指纹/人脸）、硬件令牌（如YubiKey）等组合认证。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.100(a)（唯一身份绑定）、§11.300（签名控制）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">唯一用户标识</span></p><ul><li><span style="color: rgb(64,64,64);">禁止账户共享，每个用户需通过唯一ID和身份验证绑定。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.100(a)。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">基于角色的访问控制（RBAC）</span></p><ul><li><span style="color: rgb(64,64,64);">权限分级（如管理员、审核员、签署人），按最小权限原则分配操作权限。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.10(d)（访问限制）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">会话超时与重新认证</span></p><ul><li><span style="color: rgb(64,64,64);">默认15分钟无操作自动登出，重新登录需二次认证。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.300(c)。</span></li></ul></li></ol><hr><p><span style="color: rgb(64,64,64);">二、电子签名流程管理</span></p><ol><li><p><span style="color: rgb(64,64,64);">签名意图确认</span></p><ul><li><span style="color: rgb(64,64,64);">签署前需明确展示文档内容，并要求用户确认“签名意图”（如弹窗提示）。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.50（签名含义明确）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">签名元数据绑定</span></p><ul><li><p><span style="color: rgb(64,64,64);">签名时自动嵌入以下不可篡改的元数据：</span></p><ul><li><span style="color: rgb(64,64,64);">签名者姓名、职位/角色</span></li><li><span style="color: rgb(64,64,64);">签名时间（UTC时区，精确到秒）</span></li><li><span style="color: rgb(64,64,64);">签名含义（如“批准”“拒绝”）</span></li><li><span style="color: rgb(64,64,64);">签名时系统状态（如IP地址、设备指纹）</span></li></ul></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.50、§11.70（签名与记录绑定）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">批量签名与审批链</span></p><ul><li><span style="color: rgb(64,64,64);">支持多级签名流程（如发起人→审核人→批准人），可配置并行或串行签署顺序。</span></li><li><span style="color: rgb(64,64,64);">自动触发下一环节通知，并记录流程状态。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">签名撤销与重签</span></p><ul><li><span style="color: rgb(64,64,64);">仅允许授权用户在审计追踪中注明原因后撤销签名，需重新完成完整签署流程。</span></li></ul></li></ol><hr><p><span style="color: rgb(64,64,64);">三、数据完整性与安全</span></p><ol><li><p><span style="color: rgb(64,64,64);">防篡改技术</span></p><ul><li><span style="color: rgb(64,64,64);">采用哈希值（如SHA-256）对文档和签名生成唯一指纹，任何修改均导致哈希值失效。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.10(c)（数据完整性）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">端到端加密</span></p><ul><li><span style="color: rgb(64,64,64);">数据传输：TLS 1.3加密。</span></li><li><span style="color: rgb(64,64,64);">数据存储：AES-256加密敏感信息（如签名私钥）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">时间戳服务（TSA）</span></p><ul><li><span style="color: rgb(64,64,64);">集成权威时间戳机构（如DigiCert TSA），确保签名时间不可篡改。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.50（签名时间要求）。</span></li></ul></li></ol><hr><p><span style="color: rgb(64,64,64);">四、审计追踪与日志管理</span></p><ol><li><p><span style="color: rgb(64,64,64);">全生命周期操作记录</span></p><ul><li><span style="color: rgb(64,64,64);">记录所有关键操作：登录、文档查看、编辑、签名、撤销、权限变更等。</span></li><li><span style="color: rgb(64,64,64);">记录内容包括：操作者、时间戳、操作类型、修改前后的数据差异。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.10(e)（审计追踪）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">防篡改审计日志</span></p><ul><li><span style="color: rgb(64,64,64);">日志使用区块链或哈希链技术保护，防止删除或修改。</span></li><li><span style="color: rgb(64,64,64);">仅允许只读权限导出日志（如PDF/CSV格式）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">日志检索与报告</span></p><ul><li><span style="color: rgb(64,64,64);">支持按时间、用户、操作类型等条件快速检索。</span></li><li><span style="color: rgb(64,64,64);">生成合规性报告（如签名记录汇总、异常操作警报）。</span></li></ul></li></ol><hr><p><span style="color: rgb(64,64,64);">五、系统验证与合规支持</span></p><ol><li><p><span style="color: rgb(64,64,64);">系统验证文档（IQ/OQ/PQ）</span></p><ul><li><span style="color: rgb(64,64,64);">提供安装确认（IQ）、操作确认（OQ）、性能确认（PQ）报告模板。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.10(a)（系统验证）。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">合规预配置模板</span></p><ul><li><span style="color: rgb(64,64,64);">内置符合FDA Part 11的签名流程模板、审计日志格式、保留策略等。</span></li></ul></li><li><p><span style="color: rgb(64,64,64);">电子记录归档与保留</span></p><ul><li><span style="color: rgb(64,64,64);">支持长期归档（默认记录有效期+3年），格式符合FDA可读性要求（如PDF/A）。</span></li><li><span style="color: rgb(64,64,64);">合规依据</span><span style="color: rgb(64,64,64);">：§11.10(b)（记录副本）。</span></li></ul></li></ol></td></tr><tr><th colspan="1" class="confluenceTh"><h3 id="SaaS及API支持FDA电子签名-目标客户画像">目标客户画像</h3></th><td colspan="1" class="confluenceTd">以医药行业为主，具备食品药品器材等的生产能力，并进行欧美等地跨境贸易的企业。</td></tr><tr><th colspan="1" class="confluenceTh"><h3 id="SaaS及API支持FDA电子签名-合规性评估方案">合规性评估方案</h3></th><td colspan="1" class="confluenceTd"><p>通过咨询公司德恩做产品合规性评估，确保e签宝满足FDA合规性要求。</p><p>符合性建议报告：<a href="http://wiki.timevale.cn:8081/download/attachments/*********/e%E7%AD%BE%E5%AE%9D%2021CFR%20Part%2011%E7%AC%A6%E5%90%88%E6%80%A7%E5%BB%BA%E8%AE%AE.docx?version=1&amp;modificationDate=1750387034000&amp;api=v2" data-linked-resource-id="217672463" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="e签宝 21CFR Part 11符合性建议.docx" data-nice-type="Word Document" data-linked-resource-content-type="application/vnd.openxmlformats-officedocument.wordprocessingml.document" data-linked-resource-container-id="*********" data-linked-resource-container-version="34">e签宝 21CFR Part 11符合性建议.docx</a></p><p>当前e签宝已基本满足法规要求，在电子签名样式上仍需做部分改造。</p></td></tr></tbody></table></div><h2 id="SaaS及API支持FDA电子签名-三、竞品分析">三、竞品分析</h2><p>横向对比厂商现有功能，取其精华去其糟粕。</p><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 71.108%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 10.5615%;"><col style="width: 16.287%;"><col style="width: 46.3919%;"><col style="width: 26.751%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: fixed; margin-top: 0px; left: 95px; z-index: 3; width: 1243px; top: 91px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="厂商: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 105.312px; max-width: 105.312px;"><div class="tablesorter-header-inner">厂商</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="是否支持FDA: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 176.516px; max-width: 176.516px;"><div class="tablesorter-header-inner">是否支持FDA</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能截图: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 550.875px; max-width: 550.875px;"><div class="tablesorter-header-inner">功能截图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能描述: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 306.672px; max-width: 306.672px;"><div class="tablesorter-header-inner">功能描述</div></th></tr></thead><thead class="tableFloatingHeader" style=""><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="厂商: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">厂商</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="是否支持FDA: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">是否支持FDA</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能截图: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能截图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能描述: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能描述</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">契约锁</td><td class="confluenceTd">是</td><td class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="150" src="http://wiki.timevale.cn:8081/download/attachments/*********/image%20%282%29.png?version=1&amp;modificationDate=1750398118000&amp;api=v2" data-image-src="/download/attachments/*********/image%20%282%29.png?version=1&amp;modificationDate=1750398118000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217672514" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image (2).png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="150" src="http://wiki.timevale.cn:8081/download/attachments/*********/image%20%283%29.png?version=1&amp;modificationDate=1750398123000&amp;api=v2" data-image-src="/download/attachments/*********/image%20%283%29.png?version=1&amp;modificationDate=1750398123000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217672515" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image (3).png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p><p><br></p></div></td><td colspan="1" class="confluenceTd"><ol><li>支持配置FDA电子签名样式；</li><li>支持签署含义配置和中英文切换；</li><li>功能以定制开发为主；</li></ol></td></tr><tr role="row"><td class="confluenceTd">Docusign</td><td class="confluenceTd">是</td><td class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="http://wiki.timevale.cn:8081/download/attachments/*********/image2025-6-20_11-36-44.png?version=1&amp;modificationDate=1750390604000&amp;api=v2" data-image-src="/download/attachments/*********/image2025-6-20_11-36-44.png?version=1&amp;modificationDate=1750390604000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217672503" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-6-20_11-36-44.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="http://wiki.timevale.cn:8081/download/attachments/*********/1.png?version=1&amp;modificationDate=1750401917000&amp;api=v2" data-image-src="/download/attachments/*********/1.png?version=1&amp;modificationDate=1750401917000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217672551" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="1.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="http://wiki.timevale.cn:8081/download/attachments/*********/2.png?version=1&amp;modificationDate=1750401917000&amp;api=v2" data-image-src="/download/attachments/*********/2.png?version=1&amp;modificationDate=1750401917000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217672552" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="2.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p><p><br></p></div></td><td colspan="1" class="confluenceTd"><ol><li>能够体现手绘签名，签名标准文本，时区、签署含义、时区、签署时间戳明细；</li></ol></td></tr><tr role="row"><td class="confluenceTd">法大大</td><td class="confluenceTd">是</td><td class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="150" src="http://wiki.timevale.cn:8081/download/attachments/*********/%E6%B3%95%E5%A4%A7%E5%A4%A7FDA1.png?version=1&amp;modificationDate=1750675680000&amp;api=v2" data-image-src="/download/attachments/*********/%E6%B3%95%E5%A4%A7%E5%A4%A7FDA1.png?version=1&amp;modificationDate=1750675680000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217673567" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="法大大FDA1.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="150" src="http://wiki.timevale.cn:8081/download/thumbnails/*********/%E6%B3%95%E5%A4%A7%E5%A4%A7FDA2.png?version=1&amp;modificationDate=1750675740000&amp;api=v2" data-image-src="/download/attachments/*********/%E6%B3%95%E5%A4%A7%E5%A4%A7FDA2.png?version=1&amp;modificationDate=1750675740000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217673570" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="法大大FDA2.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p><p><br></p></div></td><td colspan="1" class="confluenceTd"><ol><li>在发起签署任务时，选择是否启用FDA签名；</li><li>相较于契约锁，增加了校验值的展示；</li></ol></td></tr><tr role="row"><td colspan="1" class="confluenceTd">上上签</td><td colspan="1" class="confluenceTd">否</td><td colspan="1" class="confluenceTd">/</td><td colspan="1" class="confluenceTd">/</td></tr></tbody></table></div><h2 id="SaaS及API支持FDA电子签名-四、签名样式设计规范">四、签名样式设计规范</h2><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 71.0379%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 55.0469%;"><col style="width: 44.9444%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 981px; top: 75px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="样式设计建议: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">样式设计建议</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能拆解: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能拆解</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="样式设计建议: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">样式设计建议</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能拆解: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能拆解</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="http://wiki.timevale.cn:8081/download/attachments/*********/image2025-6-20_13-40-34.png?version=3&amp;modificationDate=1752045879000&amp;api=v2" data-image-src="/download/attachments/*********/image2025-6-20_13-40-34.png?version=3&amp;modificationDate=1752045879000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217672513" data-linked-resource-version="3" data-linked-resource-type="attachment" data-linked-resource-default-alias="image2025-6-20_13-40-34.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p></div></td><td class="confluenceTd"><ol><li>签章或个人签名；</li><li>标准字体的签名，以保证个人签名的可识别和准确；</li><li>签名含义：解释签名人签名含义，对合同表示同意或者批准；</li><li>时区：添加时区标识，保证全球范围内阅读时对时间的确定性；</li><li>校验值：展示签名对应哈希值等；</li></ol></td></tr></tbody></table></div><h2 id="SaaS及API支持FDA电子签名-五、项目计划">五、项目计划</h2><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 63.6746%;" resolved=""><colgroup><col style="width: 14.0008%;"><col style="width: 20.7249%;"><col style="width: 13.1188%;"><col style="width: 52.1486%;"></colgroup><tbody><tr><th style="text-align: center;" colspan="2" class="confluenceTh">分类</th><th style="text-align: center;" class="confluenceTh">时间</th><th style="text-align: center;" colspan="1" class="confluenceTh">明细</th></tr><tr><td style="text-align: center;" rowspan="4" class="confluenceTd">项目排期</td><td style="text-align: center;" class="confluenceTd"><span>项目评审</span></td><td class="confluenceTd">2025.07.02</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td style="text-align: center;" colspan="1" class="confluenceTd">合规认证</td><td colspan="1" class="confluenceTd">待定</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td style="text-align: center;" class="confluenceTd"><span>项目上线</span></td><td class="confluenceTd"><span>2025.08</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td style="text-align: center;" colspan="1" class="confluenceTd">功能培训</td><td colspan="1" class="confluenceTd"><span>待定</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td style="text-align: center;" colspan="2" class="confluenceTd">收费模式</td><td colspan="2" class="confluenceTd">暂定为高级版功能，API功能暂不透出，后续开放到appid维度控制</td></tr><tr><td style="text-align: center;" rowspan="2" class="confluenceTd">成本分析</td><td style="text-align: center;" class="confluenceTd">产研成本</td><td colspan="2" class="confluenceTd"><br></td></tr><tr><td style="text-align: center;" colspan="1" class="confluenceTd">合规认证成本</td><td colspan="2" class="confluenceTd"><br></td></tr></tbody></table></div><h2 id="SaaS及API支持FDA电子签名-六、产品设计">六、产品设计</h2><h3 id="SaaS及API支持FDA电子签名-6.1流程图">6.1 流程图</h3><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="400" src="http://wiki.timevale.cn:8081/download/attachments/*********/%E6%B5%81%E7%A8%8B%E5%9B%BE.png?version=1&amp;modificationDate=1750846293000&amp;api=v2" data-image-src="/download/attachments/*********/%E6%B5%81%E7%A8%8B%E5%9B%BE.png?version=1&amp;modificationDate=1750846293000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217675371" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="流程图.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p><h2 id="SaaS及API支持FDA电子签名-七、功能清单">七、功能清单</h2><div class="table-wrap"><table class="relative-table confluenceTable" style="width: 99.9299%;" resolved=""><colgroup><col style="width: 4.84463%;"><col style="width: 6.1789%;"><col style="width: 10.8129%;"><col style="width: 13.5507%;"><col style="width: 8.14431%;"><col style="width: 38.4761%;"><col style="width: 6.60025%;"><col style="width: 5.19575%;"><col style="width: 6.18241%;"></colgroup><tbody><tr><th colspan="1" class="confluenceTh"><strong>序号</strong></th><th colspan="1" class="confluenceTh"><strong>一级模块</strong></th><th colspan="1" rowspan="1" class="confluenceTh"><p style="text-align: center;" title=""><strong><span style="color: rgb(0,0,0);">二级模块</span></strong></p></th><th colspan="1" rowspan="1" class="confluenceTh"><p style="text-align: center;" title=""><strong><span style="color: rgb(0,0,0);">功能点</span></strong></p></th><th class="highlight-grey confluenceTh" colspan="1" data-highlight-colour="grey"><strong>类型</strong></th><th class="highlight-grey confluenceTh" colspan="1" rowspan="1" data-highlight-colour="grey"><p style="text-align: center;" title=""><strong><span style="color: rgb(0,0,0);">功能描述</span></strong></p></th><th class="highlight-grey confluenceTh" colspan="1" data-highlight-colour="grey"><strong title="">备注</strong></th><th class="highlight-grey confluenceTh" colspan="1" rowspan="1" data-highlight-colour="grey"><p style="text-align: center;" title=""><strong><span style="color: rgb(0,0,0);">优先级</span></strong></p></th><th class="highlight-grey confluenceTh" colspan="1" data-highlight-colour="grey"><strong>需求分期</strong></th></tr><tr><td colspan="1" class="confluenceTd">8.1</td><td class="confluenceTd"><p style="text-align: center;">设置</p></td><td class="confluenceTd"><p style="text-align: center;">合同偏好设置</p></td><td colspan="1" rowspan="1" class="confluenceTd"><p><span>新增FDA签名配置</span></p></td><td colspan="1" class="confluenceTd"><span>功能新增</span></td><td colspan="1" rowspan="1" class="confluenceTd">用户可在合同偏好设置中开启FDA签名配置，并为FDA签名配置默认样式和默认签署原因，提升每次发起签署时的配置效率</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><br></p></div></td><td colspan="1" rowspan="1" class="confluenceTd"><p>P0</p></td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><br></p></div></td></tr><tr><td rowspan="3" class="confluenceTd">8.2</td><td rowspan="2" class="confluenceTd">发起</td><td class="confluenceTd">直接发起</td><td colspan="1" class="confluenceTd">配置FDA签名样式</td><td colspan="1" class="confluenceTd"><span>功能新增</span></td><td colspan="1" class="confluenceTd">用户可在直接发起合同时，编辑FDA签名样式和签署原因，发起后该流程的所有签署方均按此样式执行</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd">P0</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><br></p></div></td></tr><tr><td class="confluenceTd">模板发起</td><td class="confluenceTd"><span>查看FDA签名样式</span></td><td colspan="1" class="confluenceTd"><span>功能新增</span></td><td colspan="1" class="confluenceTd">用户可查看当前模板配置的FDA签名样式</td><td colspan="1" class="confluenceTd"><p><br></p></td><td colspan="1" class="confluenceTd">P0</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">模板</td><td colspan="1" class="confluenceTd">模板编辑</td><td colspan="1" class="confluenceTd"><span>配置FDA签名样式</span></td><td colspan="1" class="confluenceTd"><span>功能新增</span></td><td colspan="1" class="confluenceTd"><span>用户可在编辑合同模板时，编辑FDA签名样式和签署原因，使用该模板发起的流程的所有签署方均按此样式执行</span></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd">P0</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td rowspan="2" class="confluenceTd">8.3</td><td colspan="1" class="confluenceTd">发起</td><td colspan="1" class="confluenceTd">直接发起</td><td rowspan="2" class="confluenceTd">签署区设置</td><td rowspan="2" class="confluenceTd"><span>功能新增</span></td><td rowspan="2" class="confluenceTd">启用了FDA签名后，企业经办人章和个人章签名样式仅限手绘签署且签署区样式发生变更</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd">P0</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">模板</td><td colspan="1" class="confluenceTd">模板编辑</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd">P0</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">8.4</td><td colspan="1" class="confluenceTd">签署</td><td colspan="1" class="confluenceTd">拖章签署</td><td colspan="1" class="confluenceTd">签署理由选择</td><td colspan="1" class="confluenceTd"><span>功能新增</span></td><td colspan="1" class="confluenceTd">签署流程启用了FDA签名，则需要用户在拖章后对签署理由进行确认</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span>P0</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">8.5</td><td colspan="1" class="confluenceTd">合同</td><td colspan="1" class="confluenceTd">重新发起/续签/解约</td><td colspan="1" class="confluenceTd">FDA配置继承逻辑</td><td colspan="1" class="confluenceTd"><span>功能新增</span></td><td colspan="1" class="confluenceTd">对于原合同的后续流程，需要沿用原合同的FDA签名设置</td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">解约P1</span></td><td colspan="1" class="confluenceTd"><span>P0</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">8.6</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">模板</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">模板列表</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">列表时间到秒级</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">功能优化</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">为符合CFR管理需求，需将模板的变更记录展示到秒级</span></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">P2</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">8.7</td><td colspan="2" class="confluenceTd"><span>基于文件发起签署</span></td><td colspan="1" class="confluenceTd"><span>新增FDA签名配置</span></td><td colspan="1" class="confluenceTd"><span>功能新增</span></td><td colspan="1" class="confluenceTd">接口支持发起流程配置FDA签名</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd">P0</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">8.8</span></td><td colspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">通过页面发起签署</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">新增FDA签名配置</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">功能新增</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">接口支持发起流程配置FDA签名</span></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">P1</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">8.9</td><td colspan="2" class="confluenceTd"><span>追加&amp;删除签署区</span></td><td colspan="1" class="confluenceTd">FDA签名配置校验</td><td colspan="1" class="confluenceTd"><span>功能新增</span></td><td colspan="1" class="confluenceTd">接口支持对原流程FDA签名配置的沿用和校验</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd">P0</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">8.10</td><td colspan="2" class="confluenceTd">获取批量签链接</td><td colspan="1" class="confluenceTd"><span>FDA签名配置校验</span></td><td colspan="1" class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd">接口支持判断批量签入参流程是否含启用FDA签名流程</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd">P0</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td rowspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">8.11</span></td><td colspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">发起合同解约</span></td><td rowspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">FDA签名配置校验</span></td><td rowspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">功能新增</span></td><td rowspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">接口支持对原流程FDA签名配置的沿用和校验</span></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">P1</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">通过页面发起合同解约</span></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">P1</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td rowspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">8.12</span></td><td colspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">获取《创建流程模板》页面链接</span></td><td rowspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">配置FDA签名样式</span></td><td rowspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">功能新增</span></td><td rowspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">接口返回页面，支持对模板进行FDA签名配置</span></td><td colspan="1" class="confluenceTd"><br></td><td rowspan="2" class="confluenceTd"><span style="color: rgb(193,199,208);">P1</span></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="2" class="confluenceTd"><span style="color: rgb(165,173,186);">获取《流程编辑模板》</span></td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">8.13</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">开放平台</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">服务配置</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">appid控制FDA是否可用</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">功能新增</span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(165,173,186);">通过appid维度控制客户是否可通过接口使用FDA签名配置</span></td><td colspan="1" class="confluenceTd">待定，先不做</td><td colspan="1" class="confluenceTd"><span style="color: rgb(193,199,208);">P2</span></td><td colspan="1" class="confluenceTd"><br></td></tr></tbody></table></div><h2 id="SaaS及API支持FDA电子签名-八、功能需求清单">八、功能需求清单</h2><h3 id="SaaS及API支持FDA电子签名-8.1合同偏好设置-FDA签名配置">8.1 合同偏好设置-FDA签名配置</h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 100%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 5.19382%;"><col style="width: 8.58166%;"><col style="width: 7.71575%;"><col style="width: 6.28225%;"><col style="width: 27.4994%;"><col style="width: 44.7272%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 1382px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">设置-合同偏好设置</td><td class="confluenceTd">新增FDA签名配置</td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="400" src="http://wiki.timevale.cn:8081/download/attachments/*********/fda%E7%AD%BE%E5%90%8D%E5%90%AF%E7%94%A8.png?version=5&amp;modificationDate=1752116362000&amp;api=v2" data-image-src="/download/attachments/*********/fda%E7%AD%BE%E5%90%8D%E5%90%AF%E7%94%A8.png?version=5&amp;modificationDate=1752116362000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217673713" data-linked-resource-version="5" data-linked-resource-type="attachment" data-linked-resource-default-alias="fda签名启用.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p></div></td><td colspan="1" class="confluenceTd"><ol><li>在【企业经办人章】后插入配置项【FDA签名配置】<ol><li>灰色文本提示：开启后可按规则使用符合FDA规范的签名，同时可设置签名中英文样式及签署理由的默认值。</li><li>开关按钮默认关闭，点击开启若用户非高级版及以上套餐<ol><li>确认是否开放7天试用等形式；</li><li>开启后弹窗介绍功能并引导客户联系销售（交互和UI确认，做统一设计）；<ol><li>弹窗介绍内容：通过开通FDA签名配置，可以实现合同流程维度自定义配置FDA签名样式，启用FDA签名样式并按规范发起的合同将能够符合FDA（Food and Drug Administration，美国食品药品监督管理局）及21 CFR （Code of Federal Regulations，美国联邦法规）Part 11对电子签名的基本要求。</li></ol></li><li>开关关闭时，不展示右侧【配置】按钮；</li></ol></li><li>若客户为高级版及以上客户则开关打开自动进入样式配置弹窗，弹窗分为中英文两种样式，每种样式均展示以下内容和功能；<ol><li>弹窗可tab切换中英文样式；</li><li>签署人：[签署人姓名]，固定展示不可修改；</li><li>签署理由，用户可进行添加和删除，此处配置的值为默认值，用户可在发起时在默认值的基础上继续修改；<ol><li>同意和已审核为系统预设值，用户可修改；</li><li>中文 样式每个签署理由选项最多可支持15个字，英文样式支持最多35个字；</li><li>签署理由数量范围：【1,5】，若删除最后一个原因则toast提示：签署理由最少保留一个，最多添加五个；当原因添加至5个时，添加按钮置灰；</li><li>签署理由选项支持上下拖曳排序；</li></ol></li><li>签署时间：[时区信息]，固定展示不可修改；</li><li>设置中文/英文样式为默认使用样式。<ol><li>默认勾选中文样式，中英文样式必须勾选一个；</li><li>选择后对应的样式的tab标题标记默认字样；</li><li>此设置为发起时的预设值，发起时仍然可以修改；</li></ol></li><li>预览，点击预览可根据当前用户配置的内容生成中英文样式进行查看；<ol><li>样式中的中英文姓名及其字体均默认“签小侠”；</li><li>签署理由，取中英文样式的第一个选项；</li><li>签署时间，获取系统当前时间和时区展示（使用UTC），若时区获取失败则默认使用北京时区，时间无需动态展示，仅获取点击预览时的时间即可；时间格式为YYYY-MM-DDThh:mm:ss+(UTC时间差），其中T为分隔字符；</li><li>校验值，固定值展示（待补充）；</li></ol></li></ol></li><li>初始化默认值<ol><li>默认中文样式</li><li>签署理由中文样式两个默认值；<ol><li>同意；</li><li>已审核；</li></ol></li><li>签署理由英文样式两个默认值；<ol><li>I approve this document&nbsp;；</li><li>I have reviewed this document；</li></ol></li></ol></li></ol></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.2直接发起、模板编辑、模板发起时FDA启用">8.2 直接发起、模板编辑、模板发起时FDA启用</h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 100%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 5.65822%;"><col style="width: 8.62842%;"><col style="width: 7.60449%;"><col style="width: 5.9662%;"><col style="width: 27.4687%;"><col style="width: 44.674%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 1382px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">直接发起&amp;新增模板/编辑模板</td><td class="confluenceTd">选填项设置新增FDA签名</td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="http://wiki.timevale.cn:8081/download/attachments/*********/%E5%8F%91%E8%B5%B7%E9%A1%B5%E9%9D%A2fda%E5%BC%80%E5%90%AF%E6%95%88%E6%9E%9C.png?version=2&amp;modificationDate=1750746743000&amp;api=v2" data-image-src="/download/attachments/*********/%E5%8F%91%E8%B5%B7%E9%A1%B5%E9%9D%A2fda%E5%BC%80%E5%90%AF%E6%95%88%E6%9E%9C.png?version=2&amp;modificationDate=1750746743000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217673920" data-linked-resource-version="2" data-linked-resource-type="attachment" data-linked-resource-default-alias="发起页面fda开启效果.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="250" src="http://wiki.timevale.cn:8081/download/attachments/*********/%E4%BD%BF%E7%94%A8%E6%A8%A1%E6%9D%BF%E5%8F%91%E8%B5%B7%E9%A1%B5%E6%95%88%E6%9E%9C.png?version=2&amp;modificationDate=1750746744000&amp;api=v2" data-image-src="/download/attachments/*********/%E4%BD%BF%E7%94%A8%E6%A8%A1%E6%9D%BF%E5%8F%91%E8%B5%B7%E9%A1%B5%E6%95%88%E6%9E%9C.png?version=2&amp;modificationDate=1750746744000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217673921" data-linked-resource-version="2" data-linked-resource-type="attachment" data-linked-resource-default-alias="使用模板发起页效果.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p><p><br></p></div></td><td colspan="1" class="confluenceTd"><ol><li>直接发起或模板新增&amp;编辑时，在选填项设置-合同抄送后新增【FDA签名】配置项；<ol><li>当合同偏好设置中【FDA签名配置】为启用状态时，选填项中【FDA签名】才可见，默认为关闭状态，右侧配置按钮可见可使用；</li><li>【FDA签名】后做问号提示，鼠标悬浮展示气泡内容：<ol><li>可切换中英文样式，支持配置最多5种签署理由。注意：中文样式不支持模板章，英文样式仅支持手绘签名。</li></ol></li><li>打开【FDA签名】开关或点击【配置】按钮，则进入弹窗；<ol><li>弹窗按照【FDA签名配置】首选默认展示中文或英文样式；</li><li>用户可点击【切换为中文（英文）样式】切换当前需要使用的样式；</li><li>可在弹窗内对签署理由进行编辑，其他均不可编辑；<ol><li>签署理由的配置和交互逻辑和合同偏好设置中一致；</li><li>排序第一的签署原因即为签署方签署时的默认值；</li></ol></li><li>点击确认则按照当前保存的中文或英文样式做FDA签名展示；</li></ol></li></ol></li><li>使用模板发起时，【FDA签名】展示效果如下：<ol><li>根据编辑模板时的调整，FDA签名开关按钮做置灰展示，不可编辑；</li><li>模板编辑时的【配置】按钮改为【详情】，用户可点击查看当前使用样式的明细内容；<ol><li>点击【详情】进入弹窗可查看签署人、签署理由、签署时间等内容；</li><li>切换样式按钮，原因添加按钮等均不展示，样式内容均不可编辑只能查看；</li></ol></li></ol></li><li>FDA签名启用和签章方式的关联逻辑；<ol><li>FDA签名仅针对企业经办人章、个人章生效，企业章和法人章样式不会变更；</li><li>当直接发起、模板编辑和模板发起开启了FDA签名中文样式时，若需要经办人章或个人章，则签章方式中的模板章默认无法选中；</li><li><span>当直接发起、模板编辑和模板发起开启了FDA签名英文样式时，若需要经办人章或个人章，则签章方式中只能且必定选中手绘签名；</span></li><li><s><span>海外签时，图片签始终支持；</span></s></li></ol></li><li><span>各端发起兼容</span><ol><li><span>app、小程序等端发起时，直接发起不支持开启FDA签名；模板发起则允许使用启用了FDA签名配置的模板，交互上允许查看签名配置；</span><span><br></span></li></ol></li><li><span style="color: rgb(255,102,0);">非epass企业流程中使用FDA签名</span><ol><li><span style="color: rgb(255,102,0);">非epass企业流程中不可开启FDA，提示：“当前企业账户不可使用FDA签名，请联系客服”；</span></li></ol></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.3指定签署位置页面配置兼容">8.3 指定签署位置页面配置兼容</h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 100%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 4.02477%;"><col style="width: 8.25593%;"><col style="width: 6.60475%;"><col style="width: 5.46956%;"><col style="width: 41.1765%;"><col style="width: 34.4685%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 1382px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">直接发起设置签署位置/模板编辑-设置签署与填写位置</td><td class="confluenceTd">签署区属性面板兼容FDA签名</td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="150" src="http://wiki.timevale.cn:8081/download/attachments/*********/%E6%8C%87%E5%AE%9A%E4%BD%8D%E7%BD%AE%E7%AD%BE%E7%BD%B2%E9%A1%B5%E9%9D%A2%E6%95%88%E6%9E%9C.png?version=3&amp;modificationDate=1753360734000&amp;api=v2" data-image-src="/download/attachments/*********/%E6%8C%87%E5%AE%9A%E4%BD%8D%E7%BD%AE%E7%AD%BE%E7%BD%B2%E9%A1%B5%E9%9D%A2%E6%95%88%E6%9E%9C.png?version=3&amp;modificationDate=1753360734000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217674058" data-linked-resource-version="3" data-linked-resource-type="attachment" data-linked-resource-default-alias="指定位置签署页面效果.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p></div></td><td colspan="1" class="confluenceTd"><ol><li>直接发起或模板编辑配置签署位置时，若启用了【FDA签名】则对于企业经办人章、个人章都有如下变更：<ol><li>签署区需变化，具体样式由epass输出；</li><li><span style="color: rgb(255,0,0);">需要限制每个流程中的每一份文件，所有签署方都至少有一个必签的签署区，报错文案“每个签署方均需在每份文件中设置至少一个必签的签署区”；</span></li><li><span style="color: rgb(255,0,0);">需要限制每个流程中的所有签署方，都分别至少包含一个经办人章或个人章，报错文案“每个签署方都至少包含一个经办人章或个人章”；</span></li><li>注意签署区和底稿边界距离处理的问题；</li><li>在发起和合同模板编辑时开启了FDA签名则除企业章和法人章外不允许使用骑缝签，保存时报错；</li></ol></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.4签署页面选择签署理由交互">8.4 签署页面选择签署理由交互</h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 100%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 5.65822%;"><col style="width: 8.62842%;"><col style="width: 7.60449%;"><col style="width: 5.9662%;"><col style="width: 27.4687%;"><col style="width: 44.674%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 1382px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">签署合同页面</td><td class="confluenceTd">拖章支持选择签署理由</td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image confluence-thumbnail" height="250" src="http://wiki.timevale.cn:8081/download/thumbnails/*********/%E7%AD%BE%E7%BD%B2%E9%A1%B5%E9%9D%A2%E6%95%88%E6%9E%9C.png?version=1&amp;modificationDate=1750768157000&amp;api=v2" data-image-src="/download/attachments/*********/%E7%AD%BE%E7%BD%B2%E9%A1%B5%E9%9D%A2%E6%95%88%E6%9E%9C.png?version=1&amp;modificationDate=1750768157000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217674546" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="签署页面效果.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p></div></td><td colspan="1" class="confluenceTd"><ol><li>FDA签名不支持批量签，若发起批量签时包含开启了FDA的合同流程，则在点击【提交批量签】时：<ol><li>弹窗内不支持批量签条件增加：4. 启用了FDA签名的合同流程；</li><li>进入批量签页面后自动剔除包含FDA签名的合同流程；</li></ol></li><li>单次拖曳一个印章签署<ol><li>签署人自动展示当前用户实名信息，若用户未实名则显示“实名后更新”，在提交签署实名后自动刷新姓名；</li><li>签署理由默认展示第一个选项；<ol><li>用户点击原因，弹出下拉框，可滑动选择；</li></ol></li><li>签署时间，自动获取拖曳印章时时间，无需动态更新，提交签署后将当前签署方所有签署区中的签署时间统一更新；</li><li>校验值：直接展示；</li></ol></li><li>一键落章签署<ol><li>若用户使用一键落章，则在用印后自动弹出签署原因选择下拉框，让用户进行确认；</li><li>如果用户对多个签署区的原因需要修改，必须要独立选中一个签署区进行修改；</li></ol></li><li>以上逻辑和交互各端均需支持；</li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.5签署后续流程逻辑">8.5 签署后续流程逻辑</h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 970px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 54.8281px;"><col style="width: 83.6094px;"><col style="width: 73.6875px;"><col style="width: 57.7969px;"><col style="width: 266.156px;"><col style="width: 432.922px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 969px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">重新发起/续签/解约</td><td class="confluenceTd"><br></td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><ol><li>重新发起需要把原合同中FDA签名的设置默认带入，前置需判断当前账户是否开启FDA配置；</li><li><s>续签由用户选择直接发起续签或模板续签，默认带入原合同中FDA签名设置（不包含签署区设置）；</s></li><li><s>解约需默认带入原合同中FDA签名设置（不包含签署区设置）；</s></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.6模板时间支持到秒级"><span style="color: rgb(122,134,154);">8.6 模板时间支持到秒级</span></h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 970px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 54.8281px;"><col style="width: 83.6094px;"><col style="width: 73.6875px;"><col style="width: 57.7969px;"><col style="width: 266.156px;"><col style="width: 432.922px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 969px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="原型图: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">原型图</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="5" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">模板</td><td class="confluenceTd">时间展示到秒级</td><td class="confluenceTd">功能优化</td><td colspan="1" class="confluenceTd"><div class="content-wrapper"><p><span class="confluence-embedded-file-wrapper confluence-embedded-manual-size"><img class="confluence-embedded-image" height="150" src="http://wiki.timevale.cn:8081/download/attachments/*********/%E6%A8%A1%E6%9D%BF%E6%97%B6%E9%97%B4.png?version=1&amp;modificationDate=1750835209000&amp;api=v2" data-image-src="/download/attachments/*********/%E6%A8%A1%E6%9D%BF%E6%97%B6%E9%97%B4.png?version=1&amp;modificationDate=1750835209000&amp;api=v2" data-unresolved-comment-count="0" data-linked-resource-id="217675161" data-linked-resource-version="1" data-linked-resource-type="attachment" data-linked-resource-default-alias="模板时间.png" data-base-url="http://wiki.timevale.cn:8081" data-linked-resource-content-type="image/png" data-linked-resource-container-id="*********" data-linked-resource-container-version="34"></span></p></div></td><td colspan="1" class="confluenceTd"><ol><li>创建和更新时间展示改为到秒级；</li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.7【V3API】基于文件发起签署接口兼容">8.7 【V3API】基于文件发起签署接口兼容</h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 100%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 5.65822%;"><col style="width: 8.62842%;"><col style="width: 7.60449%;"><col style="width: 5.9662%;"><col style="width: 44.674%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 1382px; top: 91px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">基于文件发起签署</td><td class="confluenceTd">接口支持FDA签名样式配置</td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><ol><li>signFlowConfig-signConfig，签署配置项下新增参数；<ol><li>signType，string，否，签名样式启用配置，默认为空；<ol><li>空值代表不启用任何样式；</li><li>FDAType-FDA签名样式，代表启用FDA签名样式<ol><li>【注】FDA签名需购买对应版本才可以使用；</li></ol></li></ol></li><li>fdaLanguageMode，string，非必填，FDA签名样式，默认CHINESE；<ol><li><span>CHINESE-中文样式；</span></li><li><span>ENGLISH-英文样式；</span></li></ol></li><li>fdaSigningReason，list，非必填，FDA签署理由；<br><ol><li>【注】若<span>signType为FDAType时</span><span>，</span><span>fdaSigningReason不传，则使用默认值，中文样式时签署原因为【同意】和【已审核】，英文样式时签署原因为【<span>I approve this document</span>】和【<span>I have reviewed this document</span>】。<span><span><span><span>signingReason按入参顺序，为首的作为签署时的默认值；</span></span></span></span></span></li></ol></li><li>若FDA签名启用且使用中文样式，则psnSealStyles不可传1-姓名印章；若启用且使用英文样式，则psnSealStyles只能传0，且默认值自动变更为0；<span><span><span><span><span><br></span></span></span></span></span></li><li>若FDA签名启用，则除企业章和法人章外不可使用骑缝签，signFieldStyle传2则报错；</li><li>若FDA签名启用，则globalWillingness-是否意愿认证必须传true；</li></ol></li><li><span><span><span><span><span>增加错误码</span></span></span></span></span><ol><li>返回报错：“FDA签名样式仅支持手写签名”；</li><li>返回报错：“FDA签名样式不支持骑缝签”；</li><li>返回报错：“FDA签名要求各签署方在每份文件中均需要盖章”；</li></ol></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.8【V3API】通过页面发起签署接口兼容"><span style="color: rgb(122,134,154);">8.8 【V3API】通过页面发起签署接口兼容</span></h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 970px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 54.8281px;"><col style="width: 83.6094px;"><col style="width: 73.6875px;"><col style="width: 57.7969px;"><col style="width: 432.922px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 969px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">通过页面发起签署</td><td class="confluenceTd"><span>接口支持FDA签名样式配置</span></td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><ol><li><span>signFlowConfig-</span><span>signConfig，签署配置项下新增参数（逻辑基本同上）；</span><ol><li><p>signType，string，否，签名样式启用配置，默认为空；</p><ol><li><p>空值代表不启用任何样式；</p></li><li><p>FDAType-FDA签名样式，代表启用FDA签名样式；</p><ol><li><p>【注】FDA签名需购买对应版本才可以使用；</p></li></ol></li></ol></li><li>fdaLanguageMode，string，非必填，FDA签名样式，默认CHINESE；<ol><li>CHINESE-中文样式；</li><li>ENGLISH-英文样式；</li></ol></li><li>fdaSigningReason，list，非必填，FDA签署理由；<br><ol><li>【注】<span>若</span><span>signType为FDAType时</span>，fdaSigningReason不传，则使用默认值，中文样式时签署原因为【同意】和【已审核】，英文样式时签署原因为【I approve this document】和【I have reviewed this document】。signingReason按入参顺序，为首的作为签署时的默认值；</li></ol></li><li>若FDA签名启用且使用中文样式，则psnSealStyles不可传1-姓名印章；若启用且使用英文样式，则psnSealStyles只能传0，且默认值自动变更为0；</li><li>若FDA签名启用，则除企业章和法人章外不可使用骑缝签，signFieldStyle传2则报错；</li></ol></li><li>增加错误码<ol><li>返回报错：“FDA签名样式仅支持手写签名”；</li><li>返回报错：“FDA签名样式不支持骑缝签”；</li><li>返回报错：“FDA签名要求各签署方在每份文件中均需要盖章”；</li></ol></li><li>页面功能修改<ol><li>获取页面发起签署链接后，选填项中新增FDA签名配置功能，交互同功能清单8.2&amp;8.3中所示，FDA签名已配置的内容按接口中入参展示；</li></ol></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.9【V3API】追加&amp;删除签署区接口兼容">8.9 【V3API】追加&amp;删除签署区接口兼容</h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 970px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 54.8281px;"><col style="width: 83.6094px;"><col style="width: 73.6875px;"><col style="width: 57.7969px;"><col style="width: 432.922px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 969px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">追加签署区</td><td class="confluenceTd">接口兼容FDA签名样式配置</td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><ol><li>若需要追加签署区的签署流程启用了FDA签名，需要校验：<ol><li>若FDA签名启用且使用中文样式，则psnSealStyles不可传1-姓名印章；若启用且使用英文样式，则psnSealStyles只能传0，且默认值自动变更为0；</li><li>若FDA签名启用，则除企业章和法人章外不可使用骑缝签，signFieldStyle传2则报错；</li></ol></li><li>增加错误码<ol><li>返回报错：“FDA签名样式仅支持手写签名”；</li><li>返回报错：“FDA签名样式不支持骑缝签”；</li><li>返回报错：“FDA签名要求各签署方在每份文件中均需要盖章”；</li></ol></li></ol></td></tr><tr role="row"><td colspan="1" class="confluenceTd">2</td><td colspan="1" class="confluenceTd">删除签署区</td><td colspan="1" class="confluenceTd">接口兼容FDA签名校验</td><td colspan="1" class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><ol><li>增加错误码<ol><li>返回报错：“FDA签名要求各签署方在每份文件中均需要盖章”；</li></ol></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.10【V3API】获取批量签页面链接（多流程）接口兼容">8.10 【V3API】获取批量签页面链接（多流程）接口兼容</h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 100%; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 7.93505%;"><col style="width: 11.2277%;"><col style="width: 10.7166%;"><col style="width: 8.65583%;"><col style="width: 61.4648%;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 1382px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">获取批量签</td><td class="confluenceTd">接口兼容FDA签名校验</td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><ol><li>增加错误码<ol><li>当入参flowid中包含启用了FDA签名的流程时，返回报错：“批量签不支持启用FDA签名流程”</li></ol></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.11【V3API】发起合同解约&amp;通过页面发起合同解约接口兼容"><span style="color: rgb(122,134,154);">8.11 【V3API】发起合同解约&amp;通过页面发起合同解约接口兼容</span></h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 970px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 54.8281px;"><col style="width: 83.6094px;"><col style="width: 73.6875px;"><col style="width: 57.7969px;"><col style="width: 432.922px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 969px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">发起合同解约</td><td class="confluenceTd"><span>接口兼容FDA签名样式配置</span></td><td class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><ol><li>原签署流程启用了FDA签名，则合同解约自动按照原合同FDA签名设置在签署页面中生效；</li><li>若FDA签名启用且使用中文样式，则psnSealStyles不可传1-姓名印章；若启用且使用英文样式，则psnSealStyles只能传0，且默认值自动变更为0；</li><li><span>增加错误码</span><ol><li>返回报错：“FDA签名样式仅支持手写签名”；</li></ol></li></ol></td></tr><tr role="row"><td colspan="1" class="confluenceTd">2</td><td colspan="1" class="confluenceTd">通过页面发起合同解约</td><td colspan="1" class="confluenceTd">接口兼容FDA签名样式配置</td><td colspan="1" class="confluenceTd">功能新增</td><td colspan="1" class="confluenceTd"><ol><li>若原签署流程启用了FDA签名，则获取合同解约页面中选填项设置需支持对FDA签名进行设置，且自动带入原流程中的FDA签名样式设置（不包含签署区设置），交互同功能清单8.2&amp;8.3；</li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.12【V3API】获取《创建流程模板》页面链接&amp;获取《流程编辑模板》页面链接"><span style="color: rgb(122,134,154);">8.12 【V3API】获取《创建流程模板》页面链接&amp;获取《流程编辑模板》页面链接</span></h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 970px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 54.8281px;"><col style="width: 83.6094px;"><col style="width: 73.6875px;"><col style="width: 57.7969px;"><col style="width: 432.922px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 969px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">获取《创建流程模板》页面链接&amp;获取《流程编辑模板》</td><td class="confluenceTd"><span>接口兼容FDA签名样式配置</span></td><td class="confluenceTd"><span>功能新增</span></td><td colspan="1" class="confluenceTd"><ol><li>流程模板需要判断账号在官网中是否在合同偏好设置里开启【FDA签名配置】；<ol><li>若不开启则同原逻辑；</li><li>若开启则页面支持对FDA签名样式进行配置，<span>交互同功能清单8.2&amp;8.3；</span></li></ol></li></ol></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-8.13运营支撑平台支持在appid维度控制是否可用FDA签名（待定）"><span style="color: rgb(122,134,154);">8.13 运营支撑平台支持在appid维度控制是否可用FDA签名（待定）</span></h3><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 970px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 54.8281px;"><col style="width: 83.6094px;"><col style="width: 73.6875px;"><col style="width: 57.7969px;"><col style="width: 432.922px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 969px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="序号: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">序号</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能模块</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="功能点: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">功能点</div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="类型: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">类型</div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="4" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="需求说明: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner">需求说明</div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">1</td><td class="confluenceTd">开放平台-应用管理-服务配置-签署服务</td><td class="confluenceTd">签署服务新增参数控制是否可用FDA签名样式</td><td class="confluenceTd"><br></td><td colspan="1" class="confluenceTd"><br></td></tr></tbody></table></div><h2 id="SaaS及API支持FDA电子签名-九、非功能性需求">九、非功能性需求</h2><h3 id="SaaS及API支持FDA电子签名-9.1数据统计需求">9.1 数据统计需求</h3><p>（陆续补充）</p><div class="table-wrap"><table class="relative-table confluenceTable tablesorter tablesorter-default stickyTableHeaders" style="width: 617.578px; padding: 0px;" role="grid" resolved=""><colgroup><col style="width: 55.9375px;"><col style="width: 90.9688px;"><col style="width: 126.406px;"><col style="width: 343.266px;"></colgroup><thead class="tableFloatingHeaderOriginal" style="position: static; margin-top: 0px; left: 462px; z-index: 3; width: 617px; top: 40px;"><tr role="row" class="tablesorter-headerRow"><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="模块: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner"><p>模块</p></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="事件: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner"><p>事件</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="事件名: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner"><p>事件名</p></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="自定义属性: No sort applied, activate to apply an ascending sort" style="user-select: none; min-width: 8px; max-width: none;"><div class="tablesorter-header-inner"><p>自定义属性</p></div></th></tr></thead><thead class="tableFloatingHeader" style="display: none;"><tr role="row" class="tablesorter-headerRow"><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="0" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="模块: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>模块</p></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="1" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="事件: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>事件</p></div></th><th colspan="1" class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="2" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="事件名: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>事件名</p></div></th><th class="confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted" data-column="3" tabindex="0" scope="col" role="columnheader" aria-disabled="false" unselectable="on" aria-sort="none" aria-label="自定义属性: No sort applied, activate to apply an ascending sort" style="user-select: none;"><div class="tablesorter-header-inner"><p>自定义属性</p></div></th></tr></thead><tbody aria-live="polite" aria-relevant="all"><tr role="row"><td class="confluenceTd">签署</td><td colspan="1" class="confluenceTd">sign_flow_initiate</td><td colspan="1" class="confluenceTd"><span style="color: rgb(71,86,105);">签署流程开启[后端]</span></td><td colspan="1" class="confluenceTd"><p class="auto-cursor-target">增加属性：needFda，是否开启FDA签名，BOOL；</p></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-"><span style="color: rgb(0,0,0);"><br></span></h3><h3 id="SaaS及API支持FDA电子签名-9.2其他非功能需求"><span style="color: rgb(0,0,0);">9.2 其他非功能需求</span></h3><div class="table-wrap"><table class="fixed-table confluenceTable" resolved=""><colgroup><col style="width: 156.0px;"><col style="width: 355.0px;"><col style="width: 239.0px;"></colgroup><tbody><tr><td class="highlight-grey confluenceTd" data-highlight-colour="grey"><p style="text-align: center;" title="" align="center"><span style="color: rgb(0,0,0);"><strong>类型</strong></span></p></td><td class="highlight-grey confluenceTd" data-highlight-colour="grey"><p title="" align="center"><span style="color: rgb(0,0,0);"><strong>需求说明</strong></span></p></td><td class="highlight-grey confluenceTd" data-highlight-colour="grey"><p title="" align="center"><span style="color: rgb(0,0,0);"><strong>是否作为验收条件</strong></span></p></td></tr><tr><td colspan="1" class="confluenceTd">性能需求</td><td colspan="1" class="confluenceTd"><p>/</p></td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">可用性需求</td><td colspan="1" class="confluenceTd">/</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">兼容性需求</td><td colspan="1" class="confluenceTd">/</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">存出证需求</td><td colspan="1" class="confluenceTd">/</td><td colspan="1" class="confluenceTd"><br></td></tr><tr><td colspan="1" class="confluenceTd">其他</td><td colspan="1" class="confluenceTd">/</td><td colspan="1" class="confluenceTd"><br></td></tr></tbody></table></div><h3 id="SaaS及API支持FDA电子签名-9.3隐私风险评估"><span style="color: rgb(0,0,0);">9.3 隐私风险评估</span></h3><p><span style="color: rgb(0,0,0);">若产品涉及用户敏感信息获取/存储/展示/外发(如通过API调用)，需要在该部分说明。目前主要侧重于账号、个人信息的安全。同时根据BSI审核要求，需前置隐私风险评审，主要含概隐私数据的内容，隐私风险的评估。</span></p><div class="table-wrap"><table class="fixed-table confluenceTable" resolved=""><colgroup><col style="width: 147.0px;"><col style="width: 172.0px;"><col style="width: 177.0px;"><col style="width: 177.0px;"><col style="width: 170.0px;"></colgroup><tbody><tr><th style="text-align: center;" colspan="5" class="confluenceTh"><p><span style="color: rgb(0,0,0);"><strong>隐私风险评估表</strong></span></p></th></tr><tr><td class="highlight-grey confluenceTd" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>产品域</strong></span></td><td class="confluenceTd"><span style="color: rgb(0,0,0);">SaaS&amp;API</span></td><td class="highlight-grey confluenceTd" data-highlight-colour="grey"><span style="color: rgb(0,0,0);">产品经理</span></td><td colspan="2" class="confluenceTd">吠陀</td></tr><tr><td class="highlight-grey confluenceTd" colspan="1" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>产品功能说明</strong></span></td><td colspan="4" class="confluenceTd"><p><br></p></td></tr><tr><td class="highlight-grey confluenceTd" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>涉及用户信息</strong></span></td><td class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="69"><span style="color: rgb(0,0,0);">个人敏感信息</span></li><li class="checked" data-inline-task-id="70"><span style="color: rgb(0,0,0);">个人姓名</span></li><li data-inline-task-id="71"><span style="color: rgb(0,0,0);">个人证件号</span></li><li data-inline-task-id="72"><span style="color: rgb(0,0,0);">个人证件照片</span></li><li data-inline-task-id="73"><span style="color: rgb(0,0,0);">个人手机号</span></li><li data-inline-task-id="74"><span style="color: rgb(0,0,0);">个人邮箱</span></li><li data-inline-task-id="75"><span style="color: rgb(0,0,0);">个人银行</span></li><li data-inline-task-id="76"><span style="color: rgb(0,0,0);">人脸照片</span></li><li data-inline-task-id="77"><span style="color: rgb(0,0,0);">人脸视频</span></li><li data-inline-task-id="78"><span style="color: rgb(0,0,0);">数字证书</span></li></ul></td><td class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="79"><span style="color: rgb(0,0,0);">终端信息</span></li><li data-inline-task-id="80"><span style="color: rgb(0,0,0);">设备信息（终端型号、识别码）</span></li><li data-inline-task-id="81"><span style="color: rgb(0,0,0);">IP地址</span></li><li data-inline-task-id="82"><span style="color: rgb(0,0,0);">MAC信息</span></li><li data-inline-task-id="83"><span style="color: rgb(0,0,0);">位置数据</span></li></ul></td><td class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li data-inline-task-id="84"><span style="color: rgb(0,0,0);">企业基本信息</span></li><li data-inline-task-id="85"><span style="color: rgb(0,0,0);">管理员姓名、证件号、联系方式</span></li><li data-inline-task-id="86"><span style="color: rgb(0,0,0);">法定代表人姓名、证件号、联系方式</span></li><li data-inline-task-id="87"><span style="color: rgb(0,0,0);">企业对公账户信息</span></li></ul></td><td colspan="1" class="confluenceTd"><ul class="inline-task-list" data-inline-tasks-content-id="*********"><li class="checked" data-inline-task-id="88"><span style="color: rgb(0,0,0);">电子签名相关</span></li><li data-inline-task-id="89"><span style="color: rgb(0,0,0);">企业印章</span></li><li data-inline-task-id="90"><span style="color: rgb(0,0,0);">个人印章、签名</span></li><li data-inline-task-id="91"><span style="color: rgb(0,0,0);">签署后文件<br class="_mce_tagged_br"></span></li><li data-inline-task-id="92"><span style="color: rgb(0,0,0);">证据报告</span></li></ul></td></tr><tr><td class="highlight-grey confluenceTd" colspan="1" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>信息采集过程</strong></span></td><td colspan="4" class="confluenceTd"><p><br></p></td></tr><tr><td class="highlight-grey confluenceTd" colspan="1" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>信息存储过程</strong></span></td><td colspan="4" class="confluenceTd"><br></td></tr><tr><td class="highlight-grey confluenceTd" colspan="1" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>信息使用过程</strong></span></td><td colspan="4" class="confluenceTd"><br></td></tr><tr><td class="highlight-grey confluenceTd" colspan="1" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>信息销毁过程</strong></span></td><td colspan="4" class="confluenceTd"><br></td></tr><tr><td class="highlight-grey confluenceTd" colspan="1" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>法务合规评估</strong></span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,0,0);">评估人：</span></td><td colspan="3" class="confluenceTd"><span style="color: rgb(0,0,0);">意见：</span><p><br></p></td></tr><tr><td class="highlight-grey confluenceTd" colspan="1" data-highlight-colour="grey"><span style="color: rgb(0,0,0);"><strong>安全合规评估</strong></span></td><td colspan="1" class="confluenceTd"><span style="color: rgb(0,0,0);">评估人：</span></td><td colspan="3" class="confluenceTd"><p><span style="color: rgb(0,0,0);">意见：</span></p><p><br></p><p><br></p></td></tr></tbody></table></div><p><br></p><p><br></p><p><br></p><p><br></p><p><br></p>

                
        
    
        </div>

        <!--
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:dc="http://purl.org/dc/elements/1.1/"
         xmlns:trackback="http://madskills.com/public/xml/rss/module/trackback/">
         <rdf:Description
    rdf:about="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********"
    dc:identifier="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********"
    dc:title="SaaS及API支持FDA电子签名"
    trackback:ping="http://wiki.timevale.cn:8081/rpc/trackback/*********"/>
</rdf:RDF>
-->

                        
    



<div id="likes-and-labels-container"><div id="likes-section" class="no-print"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" class="like-button"><span class="aui-icon aui-icon-small aui-iconfont-like"></span><span class="like-button-text">赞</span></a><span class="like-summary like-summary-margin-left">成为第一个赞同者</span></div><div id="labels-section" class="pageSection group">
    <div class="labels-section-content content-column" entityid="*********" entitytype="page">
	<div class="labels-content">
		
    <ul class="label-list label-list-right  has-pen">
            <li class="no-labels-message">
            无标签
        </li>
                <li class="labels-edit-container">
            <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="show-labels-editor" title="编辑标签 (l)">
                <span class="aui-icon aui-icon-small aui-iconfont-devtools-tag-small">编辑标签</span>
            </a>
        </li>
        </ul>

    </div>
</div>
</div></div>
        
		
            




            
        








                        
    
<div id="comments-section" class="pageSection group">
        
    


    <div class="bottom-comment-panels comment-panels">
                
                    
    


        
        
    
    <div class="quick-comment-container comment add"><p class="comment-user-logo"><a class="userLogoLink userlink-1" data-username="tengqing" href="http://wiki.timevale.cn:8081/display/~tengqing" title="" data-user-hover-bound="true"><img class="userLogo logo" src="./SaaS及API支持FDA电子签名 - 1.产品中心 - 天谷百科_files/user-avatar" alt="用户图标: tengqing" title=""></a></p><div class="quick-comment-body"><div class="quick-comment-loading-container" style="display:none;"></div><div id="editor-messages"></div><div id="all-messages"></div><form style="display:block;" class="quick-comment-form aui" method="post" name="inlinecommentform" action="http://wiki.timevale.cn:8081/pages/doaddcomment.action?pageId=*********"><div title="添加评论 (m)" class="quick-comment-prompt"><span>编写评论...</span></div></form></div></div>

            </div>

            <div id="comments-actions" class="aui-toolbar noprint" style="display: none;">
            <p class="toolbar-group">
                <span class="toolbar-item"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********&amp;showComments=true&amp;showCommentArea=true#addcomment" id="add-comment-rte" accesskey="m" class="toolbar-trigger">添加评论</a></span>
            </p>
        </div>
    </div>
        


                
    
            
    <div id="watermark" style="pointer-events: none; width: 100%; height: 100%; top: 1px; left: 1px; position: absolute; background: url(&quot;data:image/png;base64,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&quot;) right top;">
     <canvas width="280" height="280" style="display: none;"></canvas></div>

        <script>
   function addWaterMarker(str){
	  var can = document.createElement('canvas');
	  var body = document.getElementById('watermark');

	  body.appendChild(can);

	 can.width=280;
	 can.height=280;
	 can.style.display='none';
	 var cans = can.getContext('2d');
	 cans.rotate(-20*Math.PI/180);
	 cans.font = "16px Microsoft JhengHei"; 
	 cans.fillStyle = "rgba(17, 17, 17, 0.20)";
	 cans.textAlign = 'left'; 
	 cans.textBaseline = 'Middle';
	 cans.fillText(str, can.width/3-40, can.height/2,200);
	 cans.fillText("e签宝版权所有请勿外传", can.width/3-40, can.height/2+40,200);
	 body.style = "pointer-events: none; width: 100%; height: 100%; top: 1px; left: 1px; position: absolute; background:url("+can.toDataURL("image/png")+") right top;"
   
   }
   </script>
  
  　    <script>
            var myDate = new Date();
　　     addWaterMarker("誊青"+"-"+myDate.toLocaleDateString())
　    </script>
  </div>

</div>

    

    




    
    

    
    
    


    
<div id="space-tools-web-items" class="hidden">
                <div data-label="概览" data-href="/spaces/viewspacesummary.action?key=productreq">概览</div>
            <div data-label="内容工具" data-href="/pages/reorderpages.action?key=productreq">内容工具</div>
    </div>
        



            </div><!-- \#main -->
            
    
    
        
            
            

<div id="footer" role="contentinfo" style="margin-left: 55px;">
    <section class="footer-body">

                                                    
        

        <ul id="poweredby">
            <li class="noprint">基于 <a href="http://www.atlassian.com/software/confluence" class="hover-footer-link" rel="nofollow">Atlassian Confluence</a> <span id="footer-build-information">6.13.4</span> 技术构建</li>
            <li class="print-only">由 Atlassian 合流6.13.4 打印</li>
            <li class="noprint"><a href="https://support.atlassian.com/help/confluence" class="hover-footer-link" rel="nofollow">报告缺陷</a></li>
            <li class="noprint"><a href="http://www.atlassian.com/about/connected.jsp?s_kwcid=Confluence-stayintouch" class="hover-footer-link" rel="nofollow">Atlassian 新闻</a></li>
        </ul>

        

        <div id="footer-logo"><a href="http://www.atlassian.com/" rel="nofollow">Atlassian</a></div>

                    
        
    </section>
</div>

    
</div>

</div><!-- \#full-height-container -->
<!-- \#page -->

    <span style="display:none;" id="confluence-server-performance">{"serverDuration": 604, "requestCorrelationId": "5c5264d186808e05"}</span>


<script type="text/javascript">
    AJS.BigPipe = AJS.BigPipe || {};
    AJS.BigPipe.metrics = AJS.BigPipe.metrics || {};
    AJS.BigPipe.metrics.pageEnd = typeof window.performance !== "undefined" && typeof window.performance.now === "function"
                                    ? Math.ceil(window.performance.now()) : 0;
    AJS.BigPipe.metrics.isBigPipeEnabled = 'false' === 'true';
</script>


    
<div id="editor-preload-container" style="display: none;">
 

<div class="hidden">
        


<content tag="breadcrumbs">
    
    
    <ol id="quickedit-breadcrumbs">
                                        
                        
        <li class="first">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/display/productreq" target="_blank">1.产品中心</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/collector/pages.action?key=productreq" target="_blank">页面</a></span>
                                                                                                            </li><li id="ellipsis" title="显示全部导航项"><span><strong>…</strong></span></li>
                                                
                        
        <li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=42926508" target="_blank">产品中心</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=140145673" target="_blank">【公有云】-SaaS</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=159946580" target="_blank">03、迭代需求</a></span>
                                                                                                    
                        
        </li><li class="hidden-crumb">
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=210144582" target="_blank">2025年迭代</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class=""><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=217674377" target="_blank">0724迭代</a></span>
                                                                                    
                        
        </li><li>
                        
                            <span class="edited-page-title"><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********" target="_blank">SaaS及API支持FDA电子签名</a></span>
                                                                    </li></ol>

</content>
</div>


        
    

                                                                                        

<script type="text/x-template" title="editor-css" id="editor-css-resources">
    <link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.1.0/_/download/batch/com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources/com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.************************-editor-plugin:************************-editor-plugin-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secenc-font/net.customware.confluence.plugin.vault:secenc-font.css" data-wrm-key="net.customware.confluence.plugin.vault:secenc-font" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secenc-editor/net.customware.confluence.plugin.vault:secenc-editor.css" data-wrm-key="net.customware.confluence.plugin.vault:secenc-editor" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.5.14/_/download/batch/net.customware.confluence.plugin.vault:secure-macro-browser-override/net.customware.confluence.plugin.vault:secure-macro-browser-override.css" data-wrm-key="net.customware.confluence.plugin.vault:secure-macro-browser-override" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-page-typography/com.atlassian.auiplugin:aui-page-typography.css" data-wrm-key="com.atlassian.auiplugin:aui-page-typography" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-link/com.atlassian.auiplugin:aui-link.css" data-wrm-key="com.atlassian.auiplugin:aui-link" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-avatars/com.atlassian.auiplugin:aui-avatars.css" data-wrm-key="com.atlassian.auiplugin:aui-avatars" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/47a8861ddeabe407d6a865c6dde5d487-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-page-layout/com.atlassian.auiplugin:aui-page-layout.css" data-wrm-key="com.atlassian.auiplugin:aui-page-layout" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:editor-content-styles/com.atlassian.confluence.editor:editor-content-styles.css" data-wrm-key="com.atlassian.confluence.editor:editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:table-resizable-editor-content-styles/com.atlassian.confluence.editor:table-resizable-editor-content-styles.css?confluence.table.resizable=true" data-wrm-key="com.atlassian.confluence.editor:table-resizable-editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/ffac64c8ac1a578f182b6f0d74f755ad-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.editor:table-resizable-styles/com.atlassian.confluence.editor:table-resizable-styles.css?confluence.table.resizable=true" data-wrm-key="com.atlassian.confluence.editor:table-resizable-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles/com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-templates:variable-editor-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/025db4566562116adc5cdff41cef8e9a-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/7.9.7/_/download/batch/com.atlassian.auiplugin:aui-lozenge/com.atlassian.auiplugin:aui-lozenge.css" data-wrm-key="com.atlassian.auiplugin:aui-lozenge" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/85aded984d32e6df43893124b5aff573-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.0.4/_/download/batch/com.atlassian.confluence.plugins.status-macro:view_content_status/com.atlassian.confluence.plugins.status-macro:view_content_status.css" data-wrm-key="com.atlassian.confluence.plugins.status-macro:view_content_status" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/85aded984d32e6df43893124b5aff573-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.0.4/_/download/batch/com.atlassian.confluence.plugins.status-macro:editor_content_status/com.atlassian.confluence.plugins.status-macro:editor_content_status.css" data-wrm-key="com.atlassian.confluence.plugins.status-macro:editor_content_status" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/9daf7afbce8f12b08a5c226d5aed29c7-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/3.1.14/_/download/batch/com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources/com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-mentions-plugin:smart-mentions-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/1bf6e69c18341244d990250bf5aa3ce0-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.4.0/_/download/batch/com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources/com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-fixed-headers:confluence-fixed-headers-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/a0e894d2f295b40fda5171460781b200-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.0.3/_/download/batch/confluence.extra.attachments:attachments-css/confluence.extra.attachments:attachments-css.css" data-wrm-key="confluence.extra.attachments:attachments-css" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/14.2.1/_/download/batch/com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css/com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-roadmap-plugin:roadmap-placeholder-css" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.0/_/download/batch/confluence.web.resources:panel-styles/confluence.web.resources:panel-styles.css" data-wrm-key="confluence.web.resources:panel-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/17017df768625b4c50edd34ec564513c-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/1.0/_/download/batch/confluence.web.resources:content-styles/confluence.web.resources:content-styles.css" data-wrm-key="confluence.web.resources:content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_/download/batch/com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles/com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-page-layout:editor-pagelayout-content-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles/com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:inline-tasks-styles" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css" data-wrm-batch-type="resource" media="all">
<!--[if lte IE 9]>
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/11.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css/com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css.css?conditionalComment=lte+IE+9" data-wrm-key="com.atlassian.confluence.plugins.confluence-inline-tasks:editor-autocomplete-date-css" data-wrm-batch-type="resource" media="all">
<![endif]-->
<link type="text/css" rel="stylesheet" href="/s/5baeb58a5608e28c5f241eee74f064b2-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/4.0.2/_/download/batch/com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources/com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-view-file-macro:view-file-macro-editor-content-resources" data-wrm-batch-type="resource" media="all">
<link type="text/css" rel="stylesheet" href="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/5.1.4/_/download/batch/com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources/com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources.css" data-wrm-key="com.atlassian.confluence.plugins.confluence-software-blueprints:common-resources" data-wrm-batch-type="resource" media="all">

</script>













        

<div class="editor-container">

        
            

<div id="link-browser-tab-items" class="hidden">
                <div title="搜索" data-weight="10">search</div>
            <div title="最近浏览过" data-weight="20">recentlyviewed</div>
            <div title="文件" data-weight="30">attachments</div>
            <div title="Web链接" data-weight="40">weblink</div>
            <div title="高级" data-weight="50">advanced</div>
    </div>
            <div id="image-properties-tab-items" class="hidden">
                <div title="效果" data-weight="10">image-effects</div>
            <div title="标题" data-weight="20">image-attributes</div>
    </div>
            

 










<div id="toolbar">
    <div id="rte-toolbar" class="aui-toolbar aui-toolbar2">

        <div class="aui-toolbar2-primary toolbar-primary">
            <ul class="aui-buttons rte-toolbar-group-formatting">
                            <li id="format-dropdown" class="toolbar-item toolbar-dropdown">
                    <div class="aui-dd-parent">
                        <a id="format-dropdown-display" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button" data-control-id="formatselect">
                            <span class="dropdown-text">正文</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>
                        <ul id="format-dropdown-display-menu" class="aui-dropdown hidden">
                            <li class="dropdown-item format-p" data-format="p" data-tooltip="正文 (Ctrl+0)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">正文</a>
</li>
                                <li class="dropdown-item format-h1" data-format="h1" data-tooltip="标题 1 (Ctrl+1)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 1</a>
</li>
                                <li class="dropdown-item format-h2" data-format="h2" data-tooltip="标题 2 (Ctrl+2)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 2</a>
</li>
                                <li class="dropdown-item format-h3" data-format="h3" data-tooltip="标题 3 (Ctrl+3)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 3</a>
</li>
                                <li class="dropdown-item format-h4" data-format="h4" data-tooltip="标题 4 (Ctrl+4)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 4</a>
</li>
                                <li class="dropdown-item format-h5" data-format="h5" data-tooltip="标题 5 (Ctrl+5)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 5</a>
</li>
                                <li class="dropdown-item format-h6" data-format="h6" data-tooltip="标题 6 (Ctrl+6)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">标题 6</a>
</li>
                                <li class="dropdown-item format-pre" data-format="pre" data-tooltip="预格式化 (Ctrl+7)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">预格式化</a>
</li>
                                <li class="dropdown-item format-blockquote" data-format="blockquote" data-tooltip="引用 (Ctrl+8)">
    <a class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">引用</a>
</li>
                        </ul>
                    </div>
                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-style">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-bold" data-tooltip="粗体 (Ctrl+B)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="bold">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-bold ">粗体</span>
    </a>
</li>
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-italic" data-tooltip="斜体 (Ctrl+I)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="italic">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-italic ">斜体</span>
    </a>
</li>
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-underline" data-tooltip="下划线 (Ctrl+U)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="underline">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-underline ">下划线</span>
    </a>
</li>
                            <li id="color-picker-control" class="toolbar-item toolbar-splitbutton">
                    <a class="toolbar-trigger aui-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-color" data-color="003366" data-tooltip="颜色"><span class="icon aui-icon aui-icon-small aui-iconfont-editor-color ">颜色选取器</span><span class="selected-color"></span></a><div class="aui-dd-parent"><a class="toolbar-trigger aui-dd-trigger aui-button" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-color-selector" data-control-id="colorSelector" data-tooltip="更多颜色"><span class="icon aui-icon aui-icon-small aui-iconfont-dropdown ">更多颜色</span></a><div class="color-picker-container"><div class="color-picker aui-dropdown hidden"><ul><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黑色" data-tooltip="黑色" style="background-color: #000000" data-color="000000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深橙黄色" data-tooltip="深橙黄色" style="background-color: #993300" data-color="993300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深橄榄绿色" data-tooltip="深橄榄绿色" style="background-color: #333300" data-color="333300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深绿色" data-tooltip="深绿色" style="background-color: #003300" data-color="003300">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="藏青色" data-tooltip="藏青色" style="background-color: #003366" data-color="003366">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="海蓝色" data-tooltip="海蓝色" style="background-color: #000080" data-color="000080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="靛蓝色" data-tooltip="靛蓝色" style="background-color: #333399" data-color="333399">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="深灰色" data-tooltip="深灰色" style="background-color: #333333" data-color="333333">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="褐红色" data-tooltip="褐红色" style="background-color: #800000" data-color="800000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="橙色" data-tooltip="橙色" style="background-color: #FF6600" data-color="FF6600">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="橄榄绿色" data-tooltip="橄榄绿色" style="background-color: #808000" data-color="808000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绿色" data-tooltip="绿色" style="background-color: #008000" data-color="008000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="蓝绿色" data-tooltip="蓝绿色" style="background-color: #008080" data-color="008080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="蓝色" data-tooltip="蓝色" style="background-color: #0000FF" data-color="0000FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="灰蓝色" data-tooltip="灰蓝色" style="background-color: #666699" data-color="666699">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="灰色" data-tooltip="灰色" style="background-color: #7A869A" data-color="7A869A">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="红色" data-tooltip="红色" style="background-color: #FF0000" data-color="FF0000">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="琥珀色" data-tooltip="琥珀色" style="background-color: #FF9900" data-color="FF9900">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黄绿色" data-tooltip="黄绿色" style="background-color: #99CC00" data-color="99CC00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="海绿色" data-tooltip="海绿色" style="background-color: #339966" data-color="339966">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="青绿色" data-tooltip="青绿色" style="background-color: #33CCCC" data-color="33CCCC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="宝蓝色" data-tooltip="宝蓝色" style="background-color: #3366FF" data-color="3366FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="紫色" data-tooltip="紫色" style="background-color: #800080" data-color="800080">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="中灰色" data-tooltip="中灰色" style="background-color: #A5ADBA" data-color="A5ADBA">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="洋红色" data-tooltip="洋红色" style="background-color: #FF00FF" data-color="FF00FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="金色" data-tooltip="金色" style="background-color: #FFCC00" data-color="FFCC00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="黄色" data-tooltip="黄色" style="background-color: #FFFF00" data-color="FFFF00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绿黄色" data-tooltip="绿黄色" style="background-color: #00FF00" data-color="00FF00">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="湖绿色" data-tooltip="湖绿色" style="background-color: #00FFFF" data-color="00FFFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="天蓝色" data-tooltip="天蓝色" style="background-color: #00CCFF" data-color="00CCFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="红紫色" data-tooltip="红紫色" style="background-color: #993366" data-color="993366">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅灰色" data-tooltip="浅灰色" style="background-color: #C1C7D0" data-color="C1C7D0">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="粉色" data-tooltip="粉色" style="background-color: #FF99CC" data-color="FF99CC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="桃红色" data-tooltip="桃红色" style="background-color: #FFCC99" data-color="FFCC99">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅黄色" data-tooltip="浅黄色" style="background-color: #FFFF99" data-color="FFFF99">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅绿色" data-tooltip="浅绿色" style="background-color: #CCFFCC" data-color="CCFFCC">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅蓝绿色" data-tooltip="浅蓝绿色" style="background-color: #CCFFFF" data-color="CCFFFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="浅天蓝色" data-tooltip="浅天蓝色" style="background-color: #99CCFF" data-color="99CCFF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="绛紫色" data-tooltip="绛紫色" style="background-color: #CC99FF" data-color="CC99FF">&nbsp;</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" aria-label="白色" data-tooltip="白色" style="background-color: #FFFFFF" data-color="FFFFFF">&nbsp;</a></li></ul></div></div></div>
                </li>
                <li id="more-menu" class="toolbar-item toolbar-dropdown">
                    <div class="aui-dd-parent">
                        <a id="rte-button-more" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button" data-tooltip="更多">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-styles ">格式</span>
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>
                        <div id="rte-button-more-menu" class="aui-dropdown grouped hidden">
                            <div class="grouped-dropdown-item">
                                <ul>
                                                        <li class="dropdown-item more-menu-trigger" data-control-id="strikethrough" data-tooltip="删除线 (Ctrl+Shift+S)">
    <a id="rte-strikethrough" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
删除线
    </a>
</li>
                                                             <li class="dropdown-item more-menu-trigger" data-control-id="sub" data-tooltip="">
    <a id="rte-sub" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
下标
    </a>
</li>
                                                             <li class="dropdown-item more-menu-trigger" data-control-id="sup" data-tooltip="">
    <a id="rte-sup" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
上标
    </a>
</li>
                                                    <li class="dropdown-item more-menu-trigger" data-control-id="monospace" data-tooltip="用等宽字体格式化文本">
    <a id="rte-monospace" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
等宽
    </a>
</li>

                                                                                                                </ul>
                            </div>
                            <div class="grouped-dropdown-item">
                                <ul>
                                    <li class="dropdown-item more-menu-trigger no-icon" data-format="removeformat" data-tooltip="当前选中文本清除格式">
<a id="rte-removeformat" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
    清除格式
</a>
</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-lists">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-bullist" data-tooltip="无序列表 (Ctrl+Shift+B)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="bullist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-list-bullet ">无序列表</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-numlist" data-tooltip="有序列表 (Ctrl+Shift+N)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="numlist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-list-number ">有序列表</span>
    </a>
</li>
            </ul>
                            <ul class="aui-buttons rte-toolbar-group-task-lists">
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-tasklist" data-tooltip="任务列表 (tinymce.confluence.layout.three_col=三栏)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="tasklist">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-task ">任务列表</span>
    </a>
</li>
                </ul>
            
            <ul class="aui-buttons rte-toolbar-group-indentation">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-outdent" data-tooltip="减小缩进">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="outdent">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-indent-left ">减小缩进</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-indent" data-tooltip="增大缩进">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="indent">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-indent-right ">增大缩进</span>
    </a>
</li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-justification">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifyleft" data-tooltip="左对齐">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifyleft">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-left ">左对齐</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifycenter" data-tooltip="居中">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifycenter">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-center ">居中</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-justifyright" data-tooltip="右对齐">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="justifyright">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-align-right ">右对齐</span>
    </a>
</li>
            </ul>

                            <ul class="aui-buttons hidden" id="page-layout-2-group">
                    <li id="page-layout-2" class="toolbar-item" data-tooltip="页面布局">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="aui-button aui-button-subtle toolbar-trigger" id="rte-button-pagelayout-2">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-layout ">页面布局</span>
                        </a>
                    </li>
                </ul>
            

            <ul class="aui-buttons rte-toolbar-group-files hidden"></ul>

            <ul class="aui-buttons rte-toolbar-group-link no-separator">
                <li class="toolbar-item" data-tooltip="插入链接 (Ctrl+K)">
                    <a id="rte-button-link" class="toolbar-trigger aui-button aui-button-subtle" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="linkbrowserButton">
                                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-link "></span>
                        <span class="trigger-text">链接</span>
                    </a>
                </li>
            </ul>

            <ul class="aui-buttons rte-toolbar-group-table no-separator">
                <li class="toolbar-item toolbar-dropdown" id="insert-table-dropdown">
                    <div class="aui-dd-parent">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-insert-table" data-tooltip="插入表格">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-table "></span>
                            <span class="dropdown-text">表格</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>

                        <div id="table-picker-container" class="hidden aui-box-shadow">
                            <div class="table-picker-box" data-tooltip="按住 SHIFT键，创建无表头表格 。">
                                <div class="table-picker-background">
                                    <div class="picker picker-cell"></div>
                                    <div class="picker picker-heading heading"></div>
                                    <div class="picker picker-selected-cell"></div>
                                    <div class="picker picker-selected-heading heading"></div>
                                </div>
                                <p class="desc"></p>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

                                    
            
            <ul class="aui-buttons rte-toolbar-group-insert no-separator">
                <li class="toolbar-item toolbar-dropdown" id="insert-menu">
                    <div class="aui-dd-parent">
                        <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-insert" data-tooltip="插入更多内容">
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-add "></span>
                            <span class="dropdown-text">插入</span>
                                        
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                        </a>

                        <div id="insert-menu-options" class="aui-dropdown grouped hidden">
                            <div class="grouped-dropdown-item">
                                <span class="assistive">插入内容</span>
                                <ul id="content-insert-list">
                                    
        
                <li class="dropdown-item content-image" data-command="mceConfimage" data-tooltip="插入文件和图片 (Ctrl+M)">
<a id="rte-insert-image" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-image "></span>
 文件和图片
</a>
</li>
                                            
        
                <li class="dropdown-item content-link" data-control-id="linkbrowserButton" data-tooltip="插入链接 (Ctrl+K)">
<a id="rte-insert-link" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-link "></span>
 链接
</a>
</li>
                                            
        
                <li class="dropdown-item content-wikimarkup" data-command="InsertWikiMarkup" data-tooltip="插入Wiki标记 (Ctrl+Shift+D)">
<a id="rte-insert-wikimarkup" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-code "></span>
 Wiki标记
</a>
</li>
                                            
    
                <li class="dropdown-item content-hr" data-command="InsertHorizontalRule" data-tooltip="插入水平线(----)">
<a id="rte-insert-hr" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-horizontal-rule "></span>
 水平线
</a>
</li>
                                                                                
        
                <li class="dropdown-item content-tasklist" data-command="InsertInlineTaskListNoToggle" data-tooltip="插入任务列表 (tinymce.propertypanel.images.link.remove.tooltip=删除图片链接)">
<a id="rte-insert-tasklist" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-task "></span>
 任务列表
</a>
</li>
                                                                            
        
                <li class="dropdown-item content-date" data-command="confMenuInsertDate" data-tooltip="插入日期 (/ then /)">
<a id="rte-insert-date" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-calendar "></span>
 日期
</a>
</li>
                                            
    
                <li class="dropdown-item content-emoticon" data-command="mceEmotion" data-tooltip="插入表情">
<a id="rte-insert-emoticon" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-emoji "></span>
 表情符号
</a>
</li>
                                            
    
                <li class="dropdown-item content-symbol" data-command="confCharmap" data-tooltip="插入符号">
<a id="rte-insert-symbol" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-symbol "></span>
 符号
</a>
</li>
                                    </ul>
                                <span class="assistive">插入宏</span>
                                <ul id="macro-insert-list">
                                                                                                                                                                                                <li class="dropdown-item macro-insertmention-button" data-macro-name="insertmention-button" data-tooltip="插入&#39;用户提及&#39;宏">
<a id="insertmention-button" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-mention "></span>
 用户提及
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-jiralink" data-macro-name="jiralink" data-tooltip="插入&#39;Jira问题/过滤器&#39;宏">
<a id="jiralink" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-jira "></span>
 Jira问题/过滤器
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-info" data-macro-name="info" data-tooltip="插入&#39;信息&#39;宏">
<a id="info" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-info-filled "></span>
 信息
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-status" data-macro-name="status" data-tooltip="插入&#39;状态&#39;宏">
<a id="status" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                        
    
    <span class="icon confluence-icon-status-macro"></span>
 状态
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-gallery" data-macro-name="gallery" data-tooltip="插入&#39;画廊&#39;宏">
<a id="gallery" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-gallery "></span>
 画廊
</a>
</li>
                                                                                                                                                            <li class="dropdown-item macro-toc" data-macro-name="toc" data-tooltip="插入&#39;目录&#39;宏">
<a id="toc" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
                
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-overview "></span>
 目录
</a>
</li>
                                                                    </ul>
                            </div>
                            <div class="grouped-dropdown-item">
                                <ul id="more-macros-list">
                                    
        
                <li class="dropdown-item content-macro" data-command="mceConfMacroBrowser" data-tooltip="打开宏浏览器 (Ctrl+Shift+A)">
<a id="rte-insert-macro" class="item-link" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#">
    其它宏
</a>
</li>
                                    </ul>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

                            <ul class="aui-buttons rte-toolbar-group-page-layouts-section-types">
                    <li id="pagelayout-menu" class="toolbar-item toolbar-dropdown">
                        <div class="aui-dd-parent">
                            <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="toolbar-trigger aui-dd-trigger aui-button aui-button-subtle" id="rte-button-pagelayout" data-tooltip="页面布局">
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-default">页面布局</span>
                                            
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-dropdown "></span>
                            </a>

                            <ul id="pagelayout-menu-options" class="aui-dropdown hidden">
                                <li class="dropdown-item" data-tooltip="无布局">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-none&quot;, &quot;columns&quot;: 0   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-none">无布局</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple">两栏 (简单)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单，左侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple-left&quot;, &quot;columns&quot;: [&quot;aside&quot;, &quot;large&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple-left">两栏 (简单，左侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (简单，右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-simple-right&quot;, &quot;columns&quot;: [&quot;large&quot;, &quot;aside&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-simple-right">两栏 (简单，右侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三栏 (简单)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three-simple&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;, &quot;&quot;]   }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three-simple">三栏 (简单)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two">两栏</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (左侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-left&quot;, &quot;columns&quot;: [&quot;aside&quot;, &quot;large&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-left">两栏 (左侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="两栏 (右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-two-right&quot;, &quot;columns&quot;: [&quot;large&quot;, &quot;aside&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-two-right">两栏 (右侧栏)</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三列">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three&quot;, &quot;columns&quot;: [&quot;&quot;, &quot;&quot;, &quot;&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three">三列</span>
    </a>
</li>
                                <li class="dropdown-item" data-tooltip="三栏 (左边和右侧栏)">
    <a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" class="item-link" data-atlassian-layout="{&quot;name&quot;: &quot;pagelayout-three-sidebars&quot;, &quot;columns&quot;: [&quot;sidebars&quot;, &quot;large&quot;, &quot;sidebars&quot;] , &quot;header&quot;: true  , &quot;footer&quot;:true  }">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-check hidden"></span>
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-layout pagelayout-three-sidebars">三栏 (左边和右侧栏)</span>
    </a>
</li>
                            </ul>
                        </div>
                    </li>
                </ul>
            
                        
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-undo">
                
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-undo" data-tooltip="回退 (Ctrl+Z)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="undo">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-undo ">回退</span>
    </a>
</li>
                    
            <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-redo" data-tooltip="重做 (Ctrl+Y)">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="redo">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-redo ">重做</span>
    </a>
</li>
            </ul>
        </div>                                                    <div id="draft-status" style="display:none"></div>
                <div class="aui-toolbar2-secondary">
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-searchreplace">
                <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-searchreplace" data-tooltip="查找/替换">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="searchreplace">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-search ">查找/替换</span>
    </a>
</li>
            </ul>
            <ul class="aui-buttons aui-button-subtle rte-toolbar-group-help">
                <li class="toolbar-item aui-button aui-button-subtle" id="rte-button-help" data-tooltip="帮助">
    <a class="toolbar-trigger" href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" data-control-id="help">
                    
    
    <span class="icon aui-icon aui-icon-small aui-iconfont-editor-help ">键盘快捷方式帮助</span>
    </a>
</li>
            </ul>
        </div>    </div></div>


                <div id="editor-notifications-container"><div id="all-messages"><div id="action-messages"></div></div></div><div id="editor-precursor"><div class="cell"><div class="aui-buttons aui-toolbar2"><button class="aui-button aui-button-subtle rte-button-labels" type="button" data-tooltip="标签" id="rte-button-labels" data-explicit-restrictions="" data-inherited-restrictions=""><span class="icon aui-icon aui-icon-small aui-iconfont-devtools-tag"></span></button><button class="aui-button aui-button-subtle rte-button-restrictions" type="button" data-tooltip="未限制" id="rte-button-restrictions" data-explicit-restrictions="false" data-inherited-restrictions="false"><span class="icon aui-icon aui-icon-small aui-iconfont-unlocked"></span></button></div><div id="content-title-div" class="collaborative"><input type="text" name="title" id="content-title" tabindex="1" class="text pagetitle" autocomplete="off" value="SaaS及API支持FDA电子签名" placeholder="页面标题"></div></div></div>
    
<div id="wysiwyg">
    <div id="rte" class="cell editor-default collaborative">
        <textarea id="wysiwygTextarea" name="wysiwygContent" class="hidden tinymce-editor"></textarea>
    </div>
</div>
<div id="editor-html-source-container" class="hidden">
    <textarea id="editor-html-source" class="monospaceInput"></textarea>
</div>

<div id="preview">
    <div id="previewArea" class="cell">
    </div>
</div>

    <div id="savebar-container"><div id="rte-savebar" class="aui-toolbar aui-toolbar2"><span id="watermark_span" disabled="" style="float:right;display:none"> <input id="watermark_checkbox" onchange="sendCheckboxStatus(this)" type="checkbox"> <font id="add_watermark"> </font>  </span><div class="toolbar-split toolbar-split-row"><div class="toolbar-split toolbar-split-left"><div class="aui-buttons"></div></div><div class="toolbar-split toolbar-split-right"><div id="pluggable-status-container" class="toolbar-item rte-toolbar-pluggable-status"><div id="pluggable-status" class="synchrony"><div class="synchrony-status-indicator"><div class="status-indicator-icon aui-icon aui-icon-small aui-iconfont-devtools-task-in-progress" data-tooltip="自动保存所有修改到草稿中"></div><div class="status-indicator-message" data-tooltip="自动保存所有修改到草稿中">连接中...</div></div></div></div><div class="aui-buttons" id="rte-savebar-tinymce-plugin-point"></div><div class="aui-buttons"><span id="rte-spinner" class="toolbar-item shared-drafts">&nbsp;</span></div><div class="aui-buttons toolbar-group-edit assistive"><button id="rte-button-edit" class="aui-button" title="返回编辑模式" type="button"><span class="trigger-text">编辑</span></button></div><div class="aui-buttons toolbar-group-preview toolbar-group-preview-page toolbar-group-preview-shared-draft"></div><div class="save-button-container"><button class="aui-button aui-button-primary" type="submit" id="rte-button-publish" name="confirm" value="Save" title="保存"><span class="trigger-text">保存</span></button></div><div class="aui-buttons cancel-button-container-shared-draft"><button class="aui-button" type="submit" id="rte-button-cancel" name="cancel" value="cancel">取消</button></div><div class="aui-buttons toolbar-group-preview toolbar-group-ellipsis"><button class="aui-button toolbar-item aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" aria-haspopup="true" id="rte-button-ellipsis" type="button" resolved="" aria-controls="rte-ellipsis-menu" aria-expanded="false"><span class="aui-icon aui-icon-small aui-iconfont-more"></span></button></div><div id="rte-ellipsis-menu" data-aui-alignment="top auto" class="aui-style-default aui-dropdown2 aui-layer" resolved="" aria-hidden="true"><div class="aui-dropdown2-section"><ul class="aui-list-truncate"><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-button-preview">预览</a></li><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-show-changes">查看更改</a></li></ul></div><div class="aui-dropdown2-section"><ul class="aui-list-truncate"><li><a href="http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=*********#" id="rte-show-revert">恢复到最新已发布版本</a></li></ul></div></div></div></div></div></div>

    <section role="dialog" id="quit-editor-dialog" class="aui-layer aui-dialog2 aui-dialog2-medium" aria-hidden="true"><header class="aui-dialog2-header"></header><div class="aui-dialog2-content"></div><footer class="aui-dialog2-footer"><div class="aui-dialog2-footer-actions"><button id="qed-publish-button" class="aui-button aui-button-primary update">更新</button><button id="qed-discard-button" title="丢弃所有未发布的变更" class="aui-button toolbar-item exit">恢复页面</button><button id="qed-save-exit-button" class="aui-button aui-button-primary exit">保留草稿</button><button id="qed-close-button" class="aui-button toolbar-item">取消</button></div></footer></section>

    
</div>



<script type="text/x-template" title="dynamic-editor-metadata" id="dynamic-editor-metadata-template">
            <meta name="ajs-use-watch" content="true">
            <meta name="ajs-attachment-source-content-id" content="*********">
            <meta name="ajs-use-inline-tasks" content="true">
            <meta name="ajs-heartbeat" content="true">
            <meta name="ajs-action-locale" content="zh_CN">
            <meta name="ajs-editor-plugin-resource-prefix" content="/s/zh_CN/7901/0b59262db6a7cd3dbeee6d1b1416b77c01706b36/6.13.4/_">
            <meta name="ajs-edit-mode" content="collaborative">
            <meta name="ajs-user-watching-own-content" content="true">
            <meta name="ajs-new-page" content="false">
            <meta name="ajs-editor-mode" content="richtext">
            <meta name="ajs-auto-start" content="false">
            <meta name="ajs-conf-revision" content="confluence$content$*********.214">
            <meta name="ajs-sync-revision-source" content="synchrony">
            <meta name="ajs-draft-id" content="217672604">
            <meta name="ajs-draft-share-id" content="a3f94197-67c5-419c-9245-a2ca4fb8f55b">
            <meta name="ajs-content-type" content="page">
            <meta name="ajs-collaborative-editor-status" content="">
            <meta name="ajs-existing-draft-id" content="0">
            <meta name="ajs-content-id" content="*********">
            <meta name="ajs-form-name" content="inlinecommentform">
            <meta name="ajs-can-attach-files" content="true">
            <meta name="ajs-show-draft-message" content="false">
            <meta name="ajs-shared-drafts" content="true">
            <meta name="ajs-collaborative-content" content="true">
            <meta name="ajs-min-editor-height" content="150">
            <meta name="ajs-version-comment" content="">
            <meta name="ajs-draft-type" content="page">
                
                <meta name="ajs-synchrony-token" content="eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJodHRwOlwvXC93aWtpLnRpbWV2YWxlLmNuOjgwODFcL3N5bmNocm9ueS1wcm94eVwvdjEiLCJzdWIiOiIyYzlkODM1MTc5OGEzZTU1MDE3YTEyYjc3YmRhMDA0NyIsImFjY2VzcyI6eyJcL2RhdGFcL1N5bmNocm9ueS1hMWM2MDA2Mi1iYmYxLTMxZDItODBmYS1kNTVhZjhiNGU1MGZcL2NvbmZsdWVuY2UtMjE3NjcyNDIyIjoiZnVsbCIsIlwvZGF0YVwvU3luY2hyb255LWExYzYwMDYyLWJiZjEtMzFkMi04MGZhLWQ1NWFmOGI0ZTUwZlwvY29uZmx1ZW5jZS0yMTc2NzI0MjItdGl0bGUiOiJmdWxsIn0sInJldmlzaW9uTWV0YSI6eyJ1c2VyS2V5IjoiMmM5ZDgzNTE3OThhM2U1NTAxN2ExMmI3N2JkYTAwNDcifSwic2Vzc2lvbiI6eyJhdmF0YXJVUkwiOiJcL2Rvd25sb2FkXC9hdHRhY2htZW50c1wvMTI4MzYxNzAwXC91c2VyLWF2YXRhciIsIm5hbWUiOiJ0ZW5ncWluZyIsImZ1bGxuYW1lIjoi6KqK6Z2SIn0sImlzcyI6IlN5bmNocm9ueS1hMWM2MDA2Mi1iYmYxLTMxZDItODBmYS1kNTVhZjhiNGU1MGYiLCJleHAiOjE3NTM4NDM2ODUsImlhdCI6MTc1Mzc1NzI4NX0.USbLN3y2j4vFHuaI_XkH5S93qPbsTJblPk3H7iToNHs">
    <meta name="ajs-synchrony-base-url" content="http://wiki.timevale.cn:8081/synchrony-proxy,http://wiki.timevale.cn:8081/synchrony-proxy">
    <meta name="ajs-synchrony-app-id" content="Synchrony-a1c60062-bbf1-31d2-80fa-d55af8b4e50f">
    <meta name="ajs-synchrony-expiry" content="**********">
    <meta name="ajs-use-xhr-fallback" content="true">

            		    <meta name="ajs-max-thumb-width" content="300">
		    <meta name="ajs-max-thumb-height" content="300">
		    <meta name="ajs-can-send-email" content="false">
		    <meta name="ajs-is-dev-mode" content="false">
		    <meta name="ajs-draft-save-interval" content="30000">
		    <meta name="ajs-show-hidden-user-macros" content="false">
		    <meta name="ajs-can-view-profile" content="true">
		    <meta name="ajs-is-admin" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autocomplete" content="false">
		    <meta name="ajs-confluence.prefs.editor.disable.autoformat" content="false">
		    <meta name="ajs-heartbeat-interval" content="30000">
	
    </script>

<script type="text/x-template" title="tableForm" id="table-form-template">
    <form id="tinymce-table-form" class="aui">
        <div class="field-group">
            <label for="rows">行</label>
            <input id="rows" name="rows" type="text" size="3" autocomplete="off" value="{0}">
        </div>
        <div class="field-group">
            <label for="cols">列</label>
            <input id="cols" name="cols" type="text" size="3" autocomplete="off" value="{1}">
        </div>
        <div class="field-group hidden">
            <input id="width" type="hidden" name="width" value="">
            <label for="width">宽</label>
        </div>
        <div class="group">
            <div class="checkbox">
                <input id="table-heading-checkbox" class="checkbox" type="checkbox" name="heading" checked="checked" value="true">
                <label for="table-heading-checkbox">首行设为表头</label>
            </div>
        </div>
        <div class="group hidden">
            <div class="checkbox">
                <input id="table-equal-width-columns-checkbox" class="checkbox" type="checkbox" name="equal-width-columns" value="false">
                <label for="table-equal-width-columns-checkbox">等宽列</label>
            </div>
        </div>
    </form>
</script>
<input type="hidden" name="draftId" value="217672604" id="draftId"><input type="hidden" name="originalVersion" value="34" id="originalVersion">
<input type="hidden" name="syncRev" value="30.gfduVHdp1d2B5oPiT8qijgc.2" id="syncRev">    <input type="hidden" name="atl_token" value="c77c6148172317aaca6271580e8a47f2dc64efc9">
</div><div class="confluence-page-loading-errors"></div><div class="confluence-page-loading-blanket aui-blanket" aria-hidden="false" style="display: none;"><div class="confluence-loading-indicator"><aui-spinner filled="" size="small" style="color: rgb(240, 240, 240);" resolved=""><div class="aui-spinner spinner"><svg focusable="false" size="20" height="20" width="20" viewBox="0 0 20 20" style="top: 40px;"><circle cx="10" cy="10" r="9"></circle></svg></div></aui-spinner></div></div><div id="content-hover-0" class="ajs-content-hover aui-box-shadow" style="display: none;"><div class="contents" style="width: 300px;"></div></div><div id="content-hover-1" class="ajs-content-hover aui-box-shadow" style="display: none;"><div class="contents" style="width: 300px;"></div></div><div id="inline-dialog-selection-action-panel" class="aui-inline-dialog" style="left: 684.141px; right: auto; top: 11144px; display: none;"><div class="aui-inline-dialog-contents contents" unselectable="on" style="width: auto; max-height: 678px;"><button data-key="com.atlassian.confluence.plugins.confluence-inline-comments:create-inline-comment" class="aui-button aui-button-compact aui-button-subtle" original-title="Add inline comment" style="display: inline-block;"><span class="aui-icon aui-icon-small aui-iconfont-comment"></span></button><button data-key="com.atlassian.confluence.plugins.confluence-jira-content:create-Jira-issue-summary" class="aui-button aui-button-compact aui-button-subtle" original-title="Create Jira issue" style="display: inline-block;"><span class="aui-icon aui-icon-small aui-iconfont-jira"></span></button></div><div id="arrow-selection-action-panel" class="aui-inline-dialog-arrow arrow aui-css-arrow aui-bottom-arrow" unselectable="on" style="position: absolute; left: 31.5px; right: auto; top: 34px;"></div></div><div id="action-dialog-target" style="top: 11186px; height: 21px; left: 684.141px; width: 159.062px; display: none;" class=""></div><div data-tether-id="0" style="top: 0px; left: 0px; position: absolute;"></div></body><div id="pvtMessageDiv" class="pvt-message-div"></div></html>