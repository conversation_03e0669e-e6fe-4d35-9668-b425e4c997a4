#def: create_third_user_paramters($idNo, $mobileNew, $name,$thirdPartyUserId)
request:
  url: ${ENV(footstone_user_url)}/v1/accounts/createByThirdPartyUserId
  method: POST
  headers: ${get_headers($app_id)}
  json:
    {
      #          "cardNo": $cardNo,
      #          "email": null,
      "idNumber": $idNo,
      #          "idType": $idType,
      "mobile": $mobileNew,
      "name": $name,
      "thirdPartyUserId": $thirdPartyUserId,
      #          "thirdPartyUserType": $thirdPartyUserType
    }
validate:
  - eq: [status_code, 200]
#获取第三方userId详情