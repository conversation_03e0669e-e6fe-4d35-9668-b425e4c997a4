- config:
    name: 额外增加的接口用例
    request:
      base_url: https://trestapi.tsign.cn
      headers:
        Content-Type: application/json
      X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
      X-Tsign-Open-Auth-Mode: simple

- test:
    name: 个人账号创建->正常场景
    api: api/user/api-addtional/create_account.yml
    parameters:
      - name-mobile-email:
      - ["郭德纲","***********","<EMAIL>"]
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]

- test:
    name: 个人账号创建->异常场景->$casename
    api: api/user/api-addtional/create_account.yml
    parameters:
      - name-mobile-email-casename:
      - ["","***********","<EMAIL>","必填项:缺少name"]
      - ["小蜜","","<EMAIL>","必填项:mobile"]
      - ["小蜜","***********","","必填项:email"]
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data",null]

- test:
    name: 按照证件类型修改个人账号->正常场景
    api: api/user/api-addtional/updateByIdNumber.yml
    parameters:
      - name-idNumber-idType-mobile-email-thirdPartyUserId:
      - ["name成功修改了","140930196206242298","CRED_PSN_CH_IDCARD","***********","<EMAIL>","818631"]
      # validate:
      - eq: ["content.message",成功]

- test:
    name: 按照证件类型修改个人账号->异常场景->$casename
    api: api/user/api-addtional/updateByIdNumber.yml
    parameters:
      - name-idNumber-idType-mobile-email-thirdPartyUserId-casename:
      - ["name成功修改了","","CRED_PSN_CH_IDCARD","***********","<EMAIL>","818631","必填项idNumber没传"]
      - ["name成功修改了","140930196206242298","CRED_PSN_CH_IDCARD","","<EMAIL>","818631","必填项mobile没传"]
      - ["name成功修改了","140930196206242298","CRED_PSN_CH_IDCARD","***********","","818631","必填项email没传"]
      - ["name成功修改了","1409301962062422xx","CRED_PSN_CH_IDCARD","***********","","818631","根据不存在的idNumber来查询"]
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data",null]

- test:
    name: 按照第三方ID修改个人账号->正常场景
    api: api/user/api-addtional/updateByThirdId.yml
    parameters:
      - name-mobile-email-thirdPartyUserId:
      - ["name成功修改了","***********","<EMAIL>","818631"]
      # validate:
      - eq: ["content.message",成功]

- test:
    name: 按照第三方ID修改个人账号->异常场景->$casename
    api: api/user/api-addtional/updateByThirdId.yml
    parameters:
      - name-mobile-email-thirdPartyUserId-casename:
      - ["name成功修改了","1810000000","<EMAIL>","","必填项thirdPartyUserId没传"]
      - ["name成功修改了","","<EMAIL>","818631","必填项mobile没传"]
      - ["name成功修改了","1810000000","","818631","必填项mobile没传"]
      - ["name成功修改了","1810000000","<EMAIL>","666","按照不存在的thirdPartyUserId来查询"]
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data",null]

- test:
    name: 根据证件号查询个人账号->正常场景
    api: api/user/api-addtional/getByIdNumber.yml
    parameters:
      - idNumber-idType:
      - ["140930196206242298","CRED_PSN_CH_IDCARD"]
      # validate:
      - eq: ["content.message",成功]

- test:
    name: 根据证件号查询个人账号->异常场景->$casename
    api: api/user/api-addtional/getByIdNumber.yml
    parameters:
      - idNumber-idType-casename:
      - ["1409301962062422xx","CRED_PSN_CH_IDCARD","查询不存在的身份证号"]
      - ["140930196206242298","CRED_PSN_PASSPORT","证件号和证件类型不匹配"]
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data",null]

- test:
    name: 根据按照第三方用户ID查询个人账号->正常场景
    api: api/user/api-addtional/getByThirdId.yml
    parameters:
      - thirdPartyUserId: ["818631"]
      # validate:
      - eq: ["content.message",成功]

- test:
    name: 按照第三方用户ID查询个人账号->异常场景->$casename
    api: api/user/api-addtional/getByThirdId.yml
    parameters:
      - thirdPartyUserId-casename:
      - ["666","查询不存在的第三方用户ID"]
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data",null]

- test:
    name: 按照按照证件号注销个人账号->正常场景
    api: api/user/api-addtional/deleteByIdNumber.yml
    parameters:
      - idNumber-idType:
      - ["140930196206242298","CRED_PSN_CH_IDCARD"]
      # validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]   #因为证件号使用的随机的，所以没法写死，只判断状态码

- test:
    name: 按照按照证件号注销个人账号->异常场景->$casename
    api: api/user/api-addtional/deleteByIdNumber.yml
    parameters:
      - idNumber-idType-casename:
      - ["","CRED_PSN_CH_IDCARD","必填项idNumber没传"]
      - ["140930196206242298","","必填项idType没传"]
      - ["1409301962062422xx","CRED_PSN_CH_IDCARD","根据不存在的idNumber来查询"]
      - ["140930196206242298","CRED_PSN_PASSPORT","idNumber和idType不匹配"]
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",用户不存在]

- test:
    name: 按照证件号修改企业账号->正常场景
    api: api/user/api-addtional/updateByIdNumber_ORG.yml
    parameters:
      - idNumber-idType-name:
      - ["91610102MA6W0UDCXM","CRED_ORG_USCC","西安满诺满商贸有限公司"]
      # validate:
      - eq: ["content.message",成功]

- test:
    name: 按照证件号修改企业账号->异常场景->$casename
    api: api/user/api-addtional/updateByIdNumber_ORG.yml
    parameters:
      - idNumber-idType-name-casename:
      - ["","CRED_ORG_USCC","杭州天谷信息科技有限公司","必填项idNumber没传"]
      - ["913301087458306077","CRED_ORG_USCC","杭州天谷信息科技有限公司","按照不存在的idNumber来修改企业账号"]
      - ["913301087458306077","CRED_ORG_USCC","杭州天谷信息科技有限公司","idNumber和idType不匹配"]
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",用户不存在]

- test:
    name: 按照第三方ID修改企业账号->正常场景
    api: api/user/api-addtional/updateByThirdId_ORG.yml
    parameters:
      - thirdPartyUserId-name:
      - ["91610102MA6W0UDCXM","西安满诺满商贸有限公司"]
      # validate:
      - eq: ["content.message",成功]

- test:
    name: 按照第三方ID修改企业账号->异常场景->$casename
    api: api/user/api-addtional/updateByThirdId_ORG.yml
    parameters:
      - thirdPartyUserId-name-casename:
      - ["","西安满诺满商贸有限公司","必填项thirdPartyUserId没传"]
      - ["913301087458306078","西安满诺满商贸有限公司","按照不存在的thirdPartyUserId来修改企业账号"]
      - ["913301087458306077",西安满诺满商贸有限公司1","idNumber和idType不匹配"]
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",用户不存在]