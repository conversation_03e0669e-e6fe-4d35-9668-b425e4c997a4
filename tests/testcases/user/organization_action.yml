- config:
    name: "organizations组织相关的各种操作"
    ###def: organization_action($idNo,$name,$orgType,$orgCode,$legalName,$legalIdNo)
#    variables:
#        - idNo: "513230198904164146"
#        - idType: "19"
#        - person: "唐飞燕"
#    request:
#        "base_url": "${ENV(footstone_api_url)}"
#    teardown_hooks:
#        - ${teardown_organization(${ENV(footstone_api_url)}+"/api/v1/organizations/"+)}

- test:
    name: "创建一个新账号"
    api: api/user/accounts/create_accounts.yml  #无email/mobile
    extract:
      - creatorId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]
    output:
      - $creatorId

- test:
    name: "创建一个新组织"
    api: api/user/organizations/create_organizations.yml   #无email
    extract:
      - organId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]
        output:
          - $orgId

- test:
    name: "查询组织信息"
    api: api/user/organizations/get_organizations.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.userName",$name]

- test:
    name: "再次创建这个组织-验证幂等性"
    api: api/user/organizations/create_organizations.yml   #验证幂等性
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "更新组织信息"
    api: api/user/organizations/update_organizations.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "查询组织信息-新增字段了"
    api: api/user/organizations/get_organizations.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.emails",[$email]]

- test:
    name: "创建成员账号"
    api: api/user/accounts/create_accounts.yml   #无email/mobile
    extract:
      - applierAccountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]
        output:
          - $creatorId

- test:
    name: "加入组织"
    api: api/user/organizations/add_members_into_organizations.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "注销组织"
    api: api/user/organizations/delete_organizations.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "注销后查询不到组织"
    api: api/user/organizations/get_organizations.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","组织架构不存在"]
      - eq: ["content.data",null]

- test:
    name: "再次注销组织提示组织不存在"
    api: api/user/organizations/delete_organizations.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","组织架构不存在"]
      - eq: ["content.data",null]

- test:
    name: "注销成员账号"
    api: api/user/accounts/delete_accounts.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "注销创建者账号"
    api: api/user/accounts/delete_accounts.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]