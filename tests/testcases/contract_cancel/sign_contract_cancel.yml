- config:
    name: "合同解约"
    base_url: ${ENV(contract-rescind_url)}
    variables:
      - app_id1: ${ENV(opponent_appid)}
      - app_id: ${ENV(opponent_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(opponent_appid)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - original_flowId: ${ENV(original_flowId)}
      - original_flowId_accountId: ${ENV(original_flowId_accountId)}
      - original_flowId_sealId: ${ENV(original_flowId_sealId)}
      - original_flowId_signfieldId: ${ENV(original_flowId_signfieldId)}

- test:
    name: 签署 - 挂起状态流程不允许签署
    variables:
      - accountId: $original_flowId_accountId
      - flowId: $original_flowId
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $original_flowId_accountId, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $original_flowId_sealId, signfieldId: $original_flowId_signfieldId, signerOperatorAuthorizerId: $original_flowId_accountId, signerOperatorId: $original_flowId_accountId}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",签署失败：当前流程不是“签署中”的状态]


- test:
    name: 签署 - 挂起状态流程不允许签署
    variables:
      - accountId: $original_flowId_accountId
      - flowId: $original_flowId
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $original_flowId_accountId, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $original_flowId_sealId, signfieldId: $original_flowId_signfieldId, signerOperatorAuthorizerId: $original_flowId_accountId, signerOperatorId: $original_flowId_accountId}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",签署失败：当前流程不是“签署中”的状态]


      #- test:
      #    name: 签署 - 挂起状态流程不允许签署
      #    variables:
      #      - accountId: $original_flowId_accountId
      #      - flowId: $original_flowId
      #      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      #      - updateSignfield: {authorizedAccountId: $original_flowId_accountId, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $original_flowId_sealId, signfieldId: $original_flowId_signfieldId, signerOperatorAuthorizerId: $original_flowId_accountId, signerOperatorId: $original_flowId_accountId}
      #      - signfieldIds: [$updateSignfield]
      #      #      - app_id: $app_id1
      #    api: api/signflow/signfields/signfieldsExecute.yml
      #    validate:
      - eq: ["content.code",1435011]
      - eq: ["content.message",签署失败：当前流程不是“签署中”的状态]