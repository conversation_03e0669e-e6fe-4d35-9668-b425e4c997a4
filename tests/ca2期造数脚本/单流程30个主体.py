import json
import requests
from untils import 吊销证书
def getSignUrl(主体个数=1,是否注销证书=False,是否项目环境=1,appid版本=1):
    oidList = ['a97780e49e1f48f8bd3a1e87af0cd2fe', '265210bd57734f61aabd5e363396e5d2', '7b7d72816ce64118a1efa74d0be15d4c', '0a5175c19ea94ebc8a10b85ad21286e3', '7e4304113b0f4c0fb8865a8577022fd7', '516da7bba9c943b79eeca70672fa1f5a', '276c40d8e1fa4bd491bac1589a366a64', '0ca63bccb60347339ed22a1a632430c3', '4e339dcd181b432188bb970870ed6ab2', '53814038def94c29943ba39b327b21c6', '143a0faa1c5c4a3b8159f61f68fe8672', '619bb81c85c343c9a8fa4779d2001168', '575faadbe58449598faac8d6e1ea212e', 'e0d2865520674b7c8b35823a1469e573', '6b43dd64cb2a43e6aeba85f5063dd5fc', '7653775e7ccc477aa8ef9b6aabd196ad', '0d47627a08c6401f870928714facf64e', '110e456fa11b43f6b9ae3988aed899c0', '2739b2dffbb1445581c1ef2cbee3dfb9', 'a65973b35f714e5597e80e0a7ef72c89', '369448f2d88143edaaa153fce10e2dcc', 'e56380d70443498884023b4dadb21936', '778bd371743e4177a047cd2e2c2f8124', 'fb26d89b7d3748d08ce6568190c6e530', '1beb74a275ca4997a2230a1268984593', 'bf4c93b908b54412978f61455b69caf7', '5aed8c8d801c4b9086669a9697477df2', '76c8ccb4c1fa47e492f7bb16af636caa', 'facf560e861041d3a944e317059e6521', 'a29f4dc01bd1406a9c37a5a12814ef8a', '92e910f5740a41c79dcc996d595b1e11', '06b21eae7bef4999949bf3a1c269a6e1', '6b86bf0ddc9240c9a0f089619e5321e5', '2db96687f684481992ab47820428df45', '03a43fef86c5426d8a1044841c55421f', '4a10d4f6590c4a4992a25fe06417cf04', '1a01eccd476543638566e17a51cc8524', '192eaa2566034a2ab9ae45730fc7ab31', '0d54694453fd49a3818e320e6bc55f10', '4104cfa911474411938c6fcd859e4a22', 'e32b02f809f54f2db8ad95ab497ad5f9', '80097ba01f7746088365acf4eb0b669f', '71c03c36ca0e421792bf6e97591bdf64', '23eded9b4f244c0892764fd8d4e4ff5a', '59587d5d6570462f8af16d1074585924', '7bce1c8e9f044b6eafe2319088b939c2', 'a000b1a225674f6d9549a77c9c148ed2', '80ab2c98a6474df48d7014cb85e0843d', '1af667c683e9494c981c048e81e46fdb', 'ece77544129a4a5da059059b2cedb798', '7e5a2670b12e4535b97bc4103d3a03b2', '5b8daf01de054638b2006ace1fd06e93', '27e4724fcd06460fa0ed6e2cfdfedd9a']
    #获取签署的flowid
    oidDList = []
    if appid版本 == 1:
        appid = "**********"
    elif appid版本 == 2:
        appid = "**********"
    elif appid版本 == 3:
        appid = "**********"
    else:
        appid = "**********"
    for oid in oidList:
        if 是否注销证书:
            吊销证书(oid)
        else:
            pass
        oidD = {
            "signOrder": 1,
            "signerAccount": {
                "signerAccountId": "713e3c56d0b24b8d98766693d6e675b8",
                "authorizedAccountId": oid
            },
            "signfields": [
                {
                    "autoExecute": False,
                    "actorIndentityType": 2,
                    "certId": "",
                    "fileId": "63a913cb97ad4ef9b8834947b48e8862",
                    "sealType": "",
                    "posBean": {
                        "posX": 100,
                        "posY": 500,
                        "posPage": "1"  # 注意：这里有两个 posPage，建议检查是否正确
                    },
                    "signType": 1
                }
            ],
            "thirdOrderNo": "xyz"
        }
        oidDList.append(oidD)
        if len(oidDList) >= 主体个数:
            break

    url = 'http://in-test-openapi.tsign.cn/api/v3/signflows/createFlowOneStep'
    headers = {
        'X-Tsign-Open-App-Id': '**********',
        'X-Tsign-Open-Auth-Mode': 'simple',
        'Content-Type': 'application/json;charset=UTF-8',

    }


    if 是否项目环境 == 1:
        headers['X-Tsign-Service-Group'] = 'eSignCA'
    else:
        pass
    data = {
        "docs": [
            {
                "encryption": 0,
                "fileId": "63a913cb97ad4ef9b8834947b48e8862",
                "fileName": "测试文档.pdf",
                "source": 0
            }
        ],
        "flowInfo": {
            "autoArchive": True,
            "autoInitiate": True,
            "businessScene": "一步创建流程-一个人签50个企业",
            "flowConfigInfo": {
                "notifyConfig": {
                    "noticeDeveloperUrl": "http://www.94rg.com",
                    "noticeType": "1,2,4"
                },
                "signConfig": {
                    "redirectUrl": "",
                    "signPlatform": "1,2,3,5"
                }
            },
            "hashSign": False,
            "remark": "ca2期",
            "signValidity": *************
        },
        "signers": oidDList
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))

    # 打印响应
    print(response.status_code)
    res = response.json()
    flowId = res.get("data").get("flowId")
    print(flowId)


    #获取签署链接
    url = 'http://in-test-openapi.tsign.cn/v3/sign-flow/{}/sign-url'.format(flowId)
    data = {
        "operator": {
            "psnId": "",
            "psnAccount": "***********"
        },
        "organization": {
            "orgId": "",
            "orgName": ""
        },
        "clientType": "",
        "appScheme": "",
        "needLogin": False,
        "redirectConfig": {
            "redirectDelayTime": 3,
            "redirectUrl": "https://www.alibaba.com"
        }
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))


    res = response.json()
    shortUrl = res.get("data").get("shortUrl")
    print(shortUrl)
    print(res)

    if 0:
        #发送给tengqing的校验接口

        url = "http://ui-e2e.testk8s.tsign.cn/ui/auto/run"
        headers = {
            'Content-Type': 'application/json;charset=UTF-8'
        }
        data = {
            "app": "test",
            "url": shortUrl,
            "terminal": "PC",
            "env": "test",
            "scene": "ca2期单流程主体",
            "flowId": flowId,
            "commitId": flowId,
            "token": "a885e33e771525bfe29344f33c9030b42ab4d04e9c21397baea89972d05fa4fc",
            "at": True
        }

        response = requests.post(url, headers=headers, json=data)
        print(f"调用tengqing的校验接口返回的为:{response.json()}")


if __name__ == '__main__':
    getSignUrl(主体个数=1,是否注销证书=False,是否项目环境=0,appid版本=1)