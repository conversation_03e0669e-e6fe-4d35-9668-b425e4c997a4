- config:
    name: "0422接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id)}
      - path_pdf1: 个人借贷合同.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf1)}
      - group: ''

- test:
    name: "创建appid下的签署人账号"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_id
      - idNo: 130627198311032236
      - mobile:
      - name: 卜伟强
      - thirdPartyUserId: weiqiang193er8323456ytdfgh
    extract:
      - signer_accountId: content.data.accountId

- test:
    name: "appid下的签署主体-企业账号（已经有同名实名组织）"
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_id
      - idNo: 91370502060407048H
      - name: ｅｓｉｇｎｔｅｓｔ东营伟信建筑安装工程有限公司
      - thirdPartyUserId: dongy13213241362221
      - creator: $signer_accountId
    extract:
      - org_accountId: content.data.orgId

- test:
    name: "一步到位创建流程-全角半角主体名称-企业签署"
    variables:
      - app_id: $app_id
      - json:
          {
            "docs": [
              {
                "fileId": "$fileId1",
                "fileName": "个人借贷合同.pdf"
              }
            ],
            "flowInfo": {
              "autoArchive": true,
              "autoInitiate": true,
              "businessScene": "全角半角企业主体名称测试",
              "contractRemind": 1,
              "contractValidity": "${get_timestamp(10)}",
              "flowConfigInfo": {
                "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}",
                "noticeType": "1,2,3",
                "redirectUrl": "",
                "signPlatform": "1,2"
              },
              "remark": "",
              "signValidity": "${get_timestamp(10)}"
            },
            "signers": [
              {
                "platformSign": false,
                "signOrder": 1,
                "signerAccount": {
                  "authorizedAccountId": "$org_accountId",
                  "signerAccountId": "$signer_accountId"
                },
                "signfields": [
                  {
                    "actorIndentityType": "2",
                    "autoExecute": false,
                    "fileId": "$fileId1",
                    "posBean": {
                      "posPage": "1",
                      "posX": 440,
                      "posY": 440
                    },
                    "sealType": "",
                    "signDateBean": {
                      "fontSize": 12,
                      "format": "yyyy-MM-dd"
                    },
                    "signType": 1,
                    "width": 150
                  }
                ],
                "thirdOrderNo": "111"
              }
            ]
          }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: $app_id
      - orgId: $org_accountId
    extract:
      - realname_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.prop.properties.name","esigntest东营伟信建筑安装工程有限公司" ]

- test:
    setup_hooks:
      - ${hook_sleep_n_secs(3)}
    name: 查询流程签署人：如果已经转交的话，取转交人的accountId做为signerAccountId
    api: api/signflow/flowSigners/get_flow_signers.yml
    variables:
      - flowId: $sign_flowId
      - app_id: $app_id
    extract:
      - signer_accountId: content.data.signers.0.signerAccountId
    validate:
      - eq: [ status_code, 200 ]
      - len_eq: [ "content.data.signers",1 ]
      - eq: [ "status_code", 200 ]


- test:
    name: "查询签署可用印章列表-全角半角企业名称校验不通过"
    variables:
      - app_id: ${ENV(BZQ-App-Id)}
      - accountId: $signer_accountId
      - flowId: $sign_flowId
      - authorizerId: ""
      - signerAccountId: $signer_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1437172 ]
      - contains: [ "content.message","$org_accountId" ]

 # taolang标记
- test:
    name: 个人无法设置静默授权
    variables:
      - app_id: $app_id
      - accountId: $signer_accountId
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435107 ]
      - contains: [ "content.message",个人静默签授权 ]

- test:
    name: 企业无法设置静默授权
    variables:
      - app_id: $app_id
      - accountId: $org_accountId
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435106 ]
      - contains: [ "content.message",企业静默签授权 ]