- config:
    name: 创建个人+企业账号
    base_url: ${ENV(base_url)}
    ###def: user_real_prepare()
    variables:
    export:
      - personOid1

- test:
    name: 创建个人账号1
    variables:
      - json:
          {
            "name": $name_person_1,
            "thirdPartyUserId": $thirdPartyUserId_person_1,
            "idType": $idType_person,
            "idNumber": $idNumber_person_1,
            "mobile": $mobile_1,
            "email": $email_1
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - personOid1: content.data.accountId
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 查询个人账号1
    variables:
      - accountId: $personOid1
    api: api/user/account_third/xy_get_info.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.name",$name_person_1]
      - eq: ["content.data.thirdPartyUserId",$thirdPartyUserId_person_1]

      #
      #- test:
      #    name: 创建机构账号
      #    variables:
      #      - json:
      #      {
      #        "thirdPartyUserId": $thirdPartyUserId_org,
      #        "name": $name_org,
      #        "creator": $personOid2,
      #        "idType": $idType_org,
      #        "idNumber": $idNumber_org
      #      }
      #    api: api/user/organizations_third/xy_org_create.yml
      #    extract:
      #      - orgOid1: content.data.orgId
      #    validate:
      #      - len_gt: ["content.data.orgId",0]
      #    output:
      #      - $orgOid1
      #
      #
      #- test:
      #    name: 查询机构账号
      #    variables:
      #      - orgId: $orgOid1
      #    api: api/user/organizations_third/xy_org_get_info.yml
      #    validate:
      - eq: ["content.data.name",$name_org]
      - eq: ["content.data.thirdPartyUserId",$thirdPartyUserId_org]
