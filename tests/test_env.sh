#!/usr/bin/env bash
#workdir=$(cd $(dirname $0); pwd)
#echo $workdir

#环境变量替换
function env_params_replace() {
   params=`grep 'os.environ' debugtalk.py |awk -F'=' '{print $1}' | sed 's/\#\s*\([a-zA-Z0-9_.-]*\)/\1/'`
   param_replace=`grep 'os.environ' debugtalk.py |awk -F'=' '{print $2}' |sed 's/os.environ\['"[\']"'\([a-zA-Z0-9_.-]*\)'"[\']"'\].*/${ENV(\1)}/'`
   echo 'params:  '$params

   arr=(${params// / })
   length=${#arr[@]}
   arr2=(${param_replace// / })

   for ((i=0; i<$length; i++))
    do
       echo ${arr[$i]}'替换为： '${arr2[$i]}
       sed -i 's|\$'${arr[$i]}'|'"${arr2[$i]}"'|' `grep -rl '[\$]'${arr[$i]} ./`
    done
}

env_params_replace