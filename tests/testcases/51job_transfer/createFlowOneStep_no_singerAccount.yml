- config:
    name: "前程无忧二期 - createFlowOneStep接口发起允许不传入经办人（只针对企业主体签署区）"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(mobile_shield_wukong_appid)}
      - path_pdf1: data/个人借贷合同.pdf
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3' }
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}
      - appid_oid: ${ENV(mobile_shield_wukong_appid_oid)}
      - another_orgid_paltformAutoSign: ${ENV(another_orgid_paltformAutoSign)}
      - dongying_transfer_receiver: ${ENV(dongying_transfer_receiver)}
      - sixian_transfer_receiver: ${ENV(sixian_transfer_receiver)}

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "mock手机号"
    api: api/utils/demosdk/mock/phones.yml
    variables:
      - json: { "env": "测试环境","list": [ "***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********","***********" ] }
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.list",0 ]

- test:
    name: "创建机构 - esigntest测试书雪企业账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying2201211346wer
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId


- test:
    name: "创建机构 - esigntest沙箱2 - 该企业实名组织已注销"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201911141222231
      - creator: $caogu_accountId
      - idNumber: 911111111121212121
      - idType: CRED_ORG_USCC
      - name: esigntest沙箱2
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - shaxiang2_organize_id: content.data.orgId

- test:
    name: "创建机构 -  esigntest江门市诚峰镖局物流有限公司（印章平台测试企业）-该企业实名组织不存在管理员"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201912221141231
      - creator: $caogu_accountId
      - idNumber: 91440705MA52G73E89
      - idType: CRED_ORG_USCC
      - name: esigntest江门市诚峰镖局物流有限公司（印章平台测试企业）
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - jiangmen_organize_id: content.data.orgId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司1"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201211
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying1_organize_id: content.data.orgId


- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司2"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201212
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying2_organize_id: content.data.orgId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司3"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201213
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying3_organize_id: content.data.orgId


- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司4"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201214
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying4_organize_id: content.data.orgId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司5"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201215
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying5_organize_id: content.data.orgId


- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司6"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201216
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying6_organize_id: content.data.orgId

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId




- test:
    name: "静默授权 - 陈凯丽"
    variables:
      - accountId: $accountId_ckl
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data", True ]

- test:
    name: "静默授权 - esigntest东营伟信建筑安装工程有限公司"
    variables:
      - accountId: $dongying1_organize_id
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data", True ]



- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", "成功" ]

#异常case
- test:
    name: createFlowOneStep - "签署主体不存在实名组织，签署人不传，发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $jiangmen_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
#      - eq: ["content.code", 1435002]
#      - eq: ["content.message", "参数错误: 签署人账号不能为空"] 会成功



- test:
    name: createFlowOneStep - "签署主体存在实名组织但是实名组织不存在接收人，签署人不传，发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $shaxiang2_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1437607 ]
      - eq: [ "content.message", "未查到企业接收人，请传入签署人" ]


- test:
    name: createFlowOneStep - "签署主体存在实名组织且实名组织存在接收人，但签署人不传签署区超过5个且签署区主体均不相同，发起失败 - 全为企业签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying2_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying3_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying4_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying5_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying6_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1437606 ]
      - eq: [ "content.message", "单流程最多能指定5个不指定接收人的企业签署任务" ]


- test:
    name: createFlowOneStep - "签署主体存在实名组织且实名组织存在接收人，但签署人不传签署区超过5个且签署区主体均不相同，发起失败 - 混合企业、法人、经办人"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 3, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying2_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 4, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying3_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying4_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying5_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying6_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1437606 ]
      - eq: [ "content.message", "单流程最多能指定5个不指定接收人的企业签署任务" ]


- test:
    name: createFlowOneStep - "企业签署区，签署人传，签署主体不传，发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId ,authorizedAccountId: "" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 签署主体和签署区主体类型不一致" ]

- test:
    name: createFlowOneStep - "个人签署区，签署人不传，签署主体传，发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "" ,authorizedAccountId: $caogu_accountId }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 签署人账号不能为空" ]

- test:
    name: createFlowOneStep - "企业签署区，签署人传，签署主体传，签署人维度通知传中文，发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId ,authorizedAccountId: $dongying1_organize_id, noticeType: "你好" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 签署人通知方式不支持" ]

- test:
    name: createFlowOneStep - "企业签署区，签署人传，签署主体传，签署人维度通知传英文，发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId ,authorizedAccountId: $dongying1_organize_id, noticeType: nihj" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 签署人通知方式不支持" ]


- test:
    name: createFlowOneStep - "企业签署区，签署人传，签署主体传，签署人维度通知传特殊字符，发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId ,authorizedAccountId: $dongying1_organize_id, noticeType: "@4536611@！@@" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 签署人通知方式不支持" ]


- test:
    name: createFlowOneStep - "企业签署区，签署人传，签署主体传，签署人维度通知传非枚举值（枚举值为1、2、3、4），发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId ,authorizedAccountId: $dongying1_organize_id, noticeType: "1，2，3，4，5" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 签署人通知方式不支持" ]


- test:
    name: createFlowOneStep - "个人签署区，签署人传，签署主体传，签署人维度通知传值格式错误（加个空格），发起失败"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId ,authorizedAccountId: $caogu_accountId, noticeType: "1，2 " }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 签署人通知方式不支持" ]









#正向case
- test:
    name: createFlowOneStep - "签署主体存在实名组织且实名组织存在接收人，签署人不传签署区=5个，发起成功 - 全为企业签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying2_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying3_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying4_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying5_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId1
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $dongying_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $dongying1_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 2 ]

- test:
    name: createFlowOneStep - "签署主体存在实名组织且实名组织存在接收人，签署人不传签署区>5个但是签署区签署主体相同，发起成功-全为企业签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying2_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying3_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying4_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying5_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying5_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId2
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $dongying_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $dongying1_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 2 ]


- test:
    name: createFlowOneStep - "签署主体存在实名组织且存在接收人，签署人不传，发起成功 -企业签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId3
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $dongying_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $dongying1_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 2 ]


- test:
    name: createFlowOneStep - "签署主体存在实名组织且存在接收人，签署人不传，发起成功 -法人签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 3, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId4: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId4
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $dongying_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $dongying1_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 3 ]


- test:
    name: createFlowOneStep - "签署主体存在实名组织且存在接收人，签署人不传，发起成功 -经办人签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying2_organize_id, noticeType: "1,2,3,4" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 4, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId5: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId5
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $dongying_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $dongying2_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 4 ]




- test:
    name: createFlowOneStep - "签署主体存在实名组织且存在接收人，签署人传，发起成功"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId,authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId6: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId6
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $caogu_accountId ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $dongying1_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 2 ]

- test:
    name: createFlowOneStep - "签署主体不存在实名组织，签署人传，发起成功"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId,authorizedAccountId: $shaxiang2_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId7: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId7
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $caogu_accountId ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $shaxiang2_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 2 ]



- test:
    name: createFlowOneStep - "签署主体存在实名组织且实名组织存在接收人，签署人不传签署区>5个但是签署区签署主体相同，发起成功- 混合企业签、法人签、经办人"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying2_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 3, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying3_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 4, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying4_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying5_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying5_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId8: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId8
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $dongying_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $dongying1_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 2 ]

- test:
    name: createFlowOneStep - "签署主体存在实名组织且实名组织存在接收人，签署人不传签署区=5个，发起成功 - 混合企业签、法人签、经办人签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying1_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying2_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying3_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying4_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 },{ platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $dongying5_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId9: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]


- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId9
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $dongying_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $dongying1_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 2 ]








- test:
    name: createFlowOneStep - "签署主体存在实名组织且存在接收人，企业不开启转交，签署人不传，发起成功 -经办人签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $sixian_organize_id, noticeType: "1,2,3,4" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 4, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId10: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId10
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $sixian_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $sixian_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 4 ]


- test:
    name: createFlowOneStep - "签署主体存在实名组织且存在接收人，企业不开启转交，签署人不传，发起成功 -企业签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $sixian_organize_id, noticeType: "1,2,3,4" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 3, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId10: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId10
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $sixian_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $sixian_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 3 ]



- test:
    name: createFlowOneStep - "签署主体存在实名组织且存在接收人，企业不开启转交，签署人不传，发起成功 -经办人签"
    variables:
      - doc: { encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0 }
      - attachments: [ ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay": 1 }, hashSign: false }
      - signers: [ { platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $sixian_organize_id, noticeType: "1,2,3,4" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110 }, signType: 1,   width: 150 } ],  thirdOrderNo: 777 } ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId10: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId10
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.signfields.0.signerAccountId", $sixian_transfer_receiver ]
      - eq: [ "content.data.signfields.0.authorizedAccountId", $sixian_organize_id ]
      - eq: [ "content.data.signfields.0.actorIndentityType", 2 ]